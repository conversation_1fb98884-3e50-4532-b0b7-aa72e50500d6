#!/usr/bin/env python3
"""
Direct test of video serving functionality
"""

import requests
import urllib.parse

# Configuration
BASE_URL = "http://127.0.0.1:5001"
TEST_USERNAME = "assigner"
TEST_PASSWORD = "Shiva@123"

# Test video file path
VIDEO_PATH = r"T:\To_Process\Rough folder\restored files\Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019\Output\Cauvery-Calling-core-video-2_5min-V2.mov"

def test_login():
    """Test login and return session"""
    session = requests.Session()
    
    login_data = {
        'username': TEST_USERNAME,
        'password': TEST_PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        print("✅ Login successful")
        return session
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def test_video_info(session):
    """Test video info API"""
    print(f"🎥 Testing video info for: {VIDEO_PATH}")
    
    # Test with proper URL encoding
    encoded_path = urllib.parse.quote(VIDEO_PATH, safe='')
    url = f"{BASE_URL}/api/video-info?path={encoded_path}"
    
    print(f"Request URL: {url}")
    
    response = session.get(url)
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Response: {data}")
        
        if data.get('success'):
            info = data.get('info', {})
            print(f"✅ Video info working")
            print(f"   Name: {info.get('name')}")
            print(f"   Size: {info.get('size_formatted')}")
            return True
        else:
            print(f"❌ Video info failed: {data.get('error')}")
            return False
    else:
        print(f"❌ Video info API failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def test_video_serving(session):
    """Test video serving API"""
    print(f"🎬 Testing video serving for: {VIDEO_PATH}")
    
    # Test with proper URL encoding
    encoded_path = urllib.parse.quote(VIDEO_PATH, safe='')
    url = f"{BASE_URL}/api/serve-video?path={encoded_path}"
    
    print(f"Request URL: {url}")
    
    # Test HEAD request first
    response = session.head(url)
    print(f"HEAD Response status: {response.status_code}")
    print(f"HEAD Response headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        print("✅ Video serving working (HEAD request)")
        
        # Test partial GET request
        headers = {'Range': 'bytes=0-1023'}  # First 1KB
        response = session.get(url, headers=headers)
        print(f"GET Response status: {response.status_code}")
        print(f"GET Response size: {len(response.content)} bytes")
        
        if response.status_code in [200, 206]:
            print("✅ Video serving working (GET request)")
            return True
        else:
            print(f"❌ GET request failed: {response.status_code}")
            return False
    else:
        print(f"❌ Video serving failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def main():
    """Run direct video tests"""
    print("🚀 Direct Video Serving Test\n")
    
    # Test login
    session = test_login()
    if not session:
        print("❌ Cannot proceed without login")
        return
    
    print()
    
    # Test video info
    info_result = test_video_info(session)
    print()
    
    # Test video serving
    serve_result = test_video_serving(session)
    print()
    
    # Summary
    if info_result and serve_result:
        print("🎉 ALL VIDEO TESTS PASSED!")
        print("✅ Video info API working")
        print("✅ Video serving API working")
        print("✅ Large .mov file support confirmed")
    else:
        print("❌ Some video tests failed")
        if not info_result:
            print("   - Video info API failed")
        if not serve_result:
            print("   - Video serving API failed")

if __name__ == "__main__":
    main()
