#!/usr/bin/env python3
"""
Test the simple video interface
"""

import requests
import time

# Test the simple interface
BASE_URL = "http://127.0.0.1:5001"

def test_simple_interface():
    """Test the simple interface"""
    print("🧪 Testing Simple Interface")
    
    # Test login
    session = requests.Session()
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if response.status_code == 200:
        print("✅ Login successful")
    else:
        print("❌ Login failed")
        return
    
    # Test simple assigner page
    response = session.get(f"{BASE_URL}/simple-assigner")
    if response.status_code == 200:
        print("✅ Simple assigner page loads")
    else:
        print("❌ Simple assigner page failed")
        return
    
    # Test file API
    response = session.get(f"{BASE_URL}/api/simple-files/Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019")
    if response.status_code == 200:
        files = response.json()
        print(f"✅ File API working - found {len(files)} files")
        
        # Find a video file
        video_file = None
        print(f"Files structure: {type(files)} - {files if len(str(files)) < 200 else 'Large response'}")  # Debug

        for file in files:
            if isinstance(file, dict) and file.get('name', '').lower().endswith('.mov'):
                video_file = file
                break
            elif isinstance(file, str) and file.lower().endswith('.mov'):
                # Handle if files are just strings
                video_file = {'name': file, 'path': file}
                break
        
        if video_file:
            print(f"🎥 Testing video: {video_file['name']}")
            
            # Test video info
            video_info_url = f"{BASE_URL}/api/video-info?path={requests.utils.quote(video_file['path'])}"
            response = session.get(video_info_url)
            if response.status_code == 200:
                info = response.json()
                if info.get('success'):
                    print(f"✅ Video info working - {info['info']['size_formatted']}")
                else:
                    print(f"❌ Video info failed: {info.get('error')}")
            else:
                print(f"❌ Video info API failed: {response.status_code}")
            
            # Test video serving
            video_serve_url = f"{BASE_URL}/api/serve-video?path={requests.utils.quote(video_file['path'])}"
            response = session.head(video_serve_url)
            if response.status_code == 200:
                print("✅ Video serving working (HEAD request)")
                
                # Test partial content
                headers = {'Range': 'bytes=0-1023'}
                response = session.get(video_serve_url, headers=headers)
                if response.status_code in [200, 206]:
                    print(f"✅ Video streaming working (status: {response.status_code})")
                else:
                    print(f"❌ Video streaming failed: {response.status_code}")
            else:
                print(f"❌ Video serving failed: {response.status_code}")
        else:
            print("❌ No video files found")
    else:
        print("❌ File API failed")
    
    print("\n🎉 Simple interface test complete!")

if __name__ == "__main__":
    test_simple_interface()
