#!/usr/bin/env python3
"""
FIX GOOGLE SHEETS COLUMN MAPPING
Fix the issue where append_row is writing to columns M-R instead of A-F
"""

import os
import sys
from datetime import datetime

def fix_google_sheets_column_mapping():
    print("🔧 FIXING GOOGLE SHEETS COLUMN MAPPING")
    print("="*60)
    
    try:
        import gspread
        from google.oauth2.service_account import Credentials
        print("✅ Successfully imported Google Sheets modules")
    except ImportError as e:
        print(f"❌ Failed to import Google Sheets modules: {e}")
        return
    
    try:
        credentials_path = os.path.join(os.path.dirname(__file__), "credentials.json")
        if not os.path.exists(credentials_path):
            print(f"❌ Credentials file not found: {credentials_path}")
            return
        
        print(f"✅ Found credentials file")
        
        # Set up Google Sheets connection
        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/spreadsheets"
        ]
        
        creds = Credentials.from_service_account_file(credentials_path, scopes=scope)
        client = gspread.authorize(creds)
        
        # Open the specific sheet
        sheet_id = "13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4"
        sheet_name = "Records"
        sheet = client.open_by_key(sheet_id).worksheet(sheet_name)
        
        print(f"✅ Connected to Google Sheets")
        
        # Analyze the current sheet structure
        print(f"\n🔍 ANALYZING CURRENT SHEET STRUCTURE")
        print("-" * 50)
        
        all_values = sheet.get_all_values()
        print(f"📊 Total rows: {len(all_values)}")
        
        if all_values:
            first_row = all_values[0]
            print(f"📊 Columns in first row: {len(first_row)}")
            
            # Show first few columns
            print(f"📊 First row data:")
            for i, value in enumerate(first_row[:20]):  # Show first 20 columns
                if value.strip():
                    column_letter = chr(65 + i)  # A=65, B=66, etc.
                    print(f"   📊 Column {column_letter} ({i+1}): {value}")
        
        # Check if there are headers or data in columns A-F
        print(f"\n🔍 CHECKING COLUMNS A-F")
        print("-" * 50)
        
        # Get range A1:F10 to see what's there
        a_f_range = sheet.get('A1:F10')
        
        has_data_in_a_f = False
        for row_idx, row in enumerate(a_f_range):
            for col_idx, cell in enumerate(row):
                if cell.strip():
                    column_letter = chr(65 + col_idx)
                    print(f"   📊 {column_letter}{row_idx+1}: {cell}")
                    has_data_in_a_f = True
        
        if not has_data_in_a_f:
            print(f"   📊 Columns A-F are empty")
        
        # Check columns M-R
        print(f"\n🔍 CHECKING COLUMNS M-R")
        print("-" * 50)
        
        # Get range M1:R10 to see what's there
        m_r_range = sheet.get('M1:R10')
        
        has_data_in_m_r = False
        for row_idx, row in enumerate(m_r_range):
            for col_idx, cell in enumerate(row):
                if cell.strip():
                    column_letter = chr(77 + col_idx)  # M=77
                    print(f"   📊 {column_letter}{row_idx+1}: {cell}")
                    has_data_in_m_r = True
        
        if not has_data_in_m_r:
            print(f"   📊 Columns M-R are empty")
        
        # THE FIX: Use update instead of append_row
        print(f"\n🔧 IMPLEMENTING FIX")
        print("-" * 50)
        
        print(f"🔧 SOLUTION: Use sheet.update() with specific range instead of append_row()")
        
        # Test the fix
        timestamp = datetime.now().strftime("%H%M%S")
        
        test_data = [
            f"FIXED_TEST_{timestamp}",                         # Column A
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),     # Column B
            "Internal video without stems",                    # Column C
            f"https://fixed-test-{timestamp}.com",            # Column D
            "Executor Public",                                 # Column E
            f"FIXED TEST - COLUMNS A-F - {timestamp}"         # Column F
        ]
        
        print(f"📝 Test data for fix:")
        print(f"   📊 Column A: {test_data[0]}")
        print(f"   📊 Column B: {test_data[1]}")
        print(f"   📊 Column C: {test_data[2]}")
        print(f"   📊 Column D: {test_data[3]}")
        print(f"   📊 Column E: {test_data[4]}")
        print(f"   📊 Column F: {test_data[5]}")
        
        # Find the next empty row
        current_rows = len(all_values)
        next_row = current_rows + 1
        
        # Use update with specific range A:F
        range_name = f"A{next_row}:F{next_row}"
        print(f"🚀 Using sheet.update() with range: {range_name}")
        
        result = sheet.update(range_name, [test_data])
        print(f"✅ Update result: {result}")
        
        # Verify the fix
        print(f"\n🔍 VERIFYING THE FIX")
        print("-" * 50)
        
        # Get the row we just updated
        updated_row = sheet.get(f"A{next_row}:R{next_row}")[0]
        
        print(f"📊 Updated row data:")
        for i, value in enumerate(updated_row):
            if value.strip():
                column_letter = chr(65 + i)
                print(f"   📊 Column {column_letter} ({i+1}): {value}")
        
        # Check if data is in A-F
        a_f_data = updated_row[:6]
        matches = 0
        for i, expected in enumerate(test_data):
            if i < len(a_f_data) and str(a_f_data[i]) == str(expected):
                matches += 1
                column_letter = chr(65 + i)
                print(f"   ✅ Column {column_letter}: CORRECT")
            else:
                column_letter = chr(65 + i)
                actual = a_f_data[i] if i < len(a_f_data) else "EMPTY"
                print(f"   ❌ Column {column_letter}: Expected '{expected}', Got '{actual}'")
        
        if matches == 6:
            print(f"\n🎉 SUCCESS: Fix works! Data correctly written to columns A-F!")
        else:
            print(f"\n❌ Fix failed: Only {matches}/6 columns correct")
        
        # Check if any data leaked to M-R
        m_r_data = updated_row[12:18] if len(updated_row) >= 18 else []
        has_leak = any(cell.strip() for cell in m_r_data if cell)
        
        if has_leak:
            print(f"⚠️  WARNING: Data still appears in columns M-R")
        else:
            print(f"✅ No data in columns M-R (correct)")
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print(f"\n🎯 FIX SUMMARY:")
    print("="*60)
    print(f"✅ PROBLEM: append_row() writes to columns M-R")
    print(f"✅ SOLUTION: Use sheet.update(range, data) with specific A:F range")
    print(f"✅ IMPLEMENTATION: Replace append_row with update in log_to_google_sheets")
    print("="*60)

if __name__ == "__main__":
    fix_google_sheets_column_mapping()
