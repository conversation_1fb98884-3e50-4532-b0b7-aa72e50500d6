#!/usr/bin/env python3
"""
DEBUG ASSIGNMENT ERROR
Reproduce the exact error from the browser
"""

import requests
import json

def debug_assignment_error():
    print("🔍 DEBUGGING ASSIGNMENT ERROR")
    print("="*50)
    
    session = requests.Session()
    
    # Step 1: Login as assigner
    print("\n1. Logging in as assigner...")
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
    print(f"Login status: {login_response.status_code}")
    
    if login_response.status_code not in [200, 302]:
        print("❌ Login failed!")
        return
    
    # Step 2: Get folders
    print("\n2. Getting folders...")
    folders_response = session.get('http://127.0.0.1:5001/api/get-folders')
    print(f"Folders API status: {folders_response.status_code}")
    
    if folders_response.status_code != 200:
        print("❌ Folders API failed!")
        return
    
    folders_data = folders_response.json()
    print(f"Folders API success: {folders_data.get('success')}")
    
    if not folders_data.get('success'):
        print(f"❌ Folders API error: {folders_data.get('error')}")
        return
    
    folders = folders_data.get('folders', [])
    print(f"Found {len(folders)} folders")
    
    if not folders:
        print("❌ No folders found!")
        return
    
    # Step 3: Test assignment with first folder
    test_folder = folders[0]['path']
    print(f"\n3. Testing assignment with folder: {test_folder}")
    
    # Check if folder exists
    import os
    print(f"Folder exists: {os.path.exists(test_folder)}")
    print(f"Is directory: {os.path.isdir(test_folder) if os.path.exists(test_folder) else 'N/A'}")
    
    # Test assignment
    assign_data = {
        'folder_paths': [test_folder],
        'category': 'Social media outputs with stems',
        'assign_to': 'Executor Public',
        'video_ids': 'TEST001',
        'url': 'https://test.com',
        'remarks': 'Debug test assignment',
        'operation_type': 'copy'
    }
    
    print(f"\n4. Assignment data:")
    print(json.dumps(assign_data, indent=2))
    
    print(f"\n5. Making assignment request...")
    assign_response = session.post(
        'http://127.0.0.1:5001/api/add-to-queue',
        json=assign_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Assignment status: {assign_response.status_code}")
    print(f"Assignment response headers: {dict(assign_response.headers)}")
    
    try:
        response_data = assign_response.json()
        print(f"\n6. Assignment response:")
        print(json.dumps(response_data, indent=2))
        
        if response_data.get('success'):
            print("✅ Assignment successful!")
            print(f"Added: {response_data.get('added', 0)}")
            print(f"Failed: {response_data.get('failed', 0)}")
        else:
            print("❌ Assignment failed!")
            print(f"Error: {response_data.get('error')}")
            
            if 'failed_items' in response_data:
                print("Failed items:")
                for item in response_data['failed_items']:
                    print(f"  - {item}")
                    
    except Exception as e:
        print(f"❌ Failed to parse response as JSON: {e}")
        print(f"Raw response: {assign_response.text}")
    
    # Step 4: Check queue status
    print(f"\n7. Checking queue status...")
    queue_response = session.get('http://127.0.0.1:5001/api/queue-status')
    if queue_response.status_code == 200:
        queue_data = queue_response.json()
        if queue_data.get('success'):
            queue_status = queue_data.get('queue_status', {})
            items = queue_status.get('items', [])
            print(f"Queue items: {len(items)}")
            
            # Show recent items
            recent_items = items[-3:] if len(items) > 3 else items
            for item in recent_items:
                print(f"  - ID: {item.get('id')}, Status: {item.get('status')}, Folder: {item.get('folder_name')}")
        else:
            print(f"Queue status error: {queue_data.get('error')}")
    else:
        print(f"Queue status API failed: {queue_response.status_code}")

if __name__ == "__main__":
    debug_assignment_error()
