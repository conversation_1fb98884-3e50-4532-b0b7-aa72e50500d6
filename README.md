# 🎬 Multi-User File Management System

A comprehensive Flask-based web application for managing video file processing workflows with role-based access control.

## 🌟 Features

### 👥 Five User Roles
- **Assigner** - Browse folders, select video files, assign to processing categories
- **Cross Checker** - Review and validate assigned files before processing
- **Executor Public** - Process cross-checked files (excluding private content)
- **Executor Private** - Process all files including private content
- **Main Admin** - System administration and user management

### 📁 File Management
- Browse source folders with hierarchical tree view
- Support for .mov, .mp4, .avi, .mkv, .wmv, .flv, .webm video files
- Automatic file scanning and database tracking
- File metadata display (size, type, modification date)

### 🔄 Processing Categories
- Social media single output with stems
- Social media single output without stems
- Internal video with stems
- Internal video without stems
- Private one video
- Tamil files
- Miscellaneous
- Multiple output with stems
- Multiple output only
- Multiple output with project file
- To Be Deleted

### 🔐 Security Features
- Role-based access control
- Secure password hashing
- Session management
- Private file access restrictions

## 🚀 Quick Start

### Prerequisites
- Python 3.7 or higher
- Windows OS (configured for Windows paths)

### Installation

1. **Clone or download the project files**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application:**
   ```bash
   python run.py
   ```

4. **Access the application:**
   - Open your browser and go to: `http://localhost:5001`
   - Alternative URL: `http://127.0.0.1:5001`
   - Use the demo credentials provided below

## 🔑 Demo Login Credentials

| Role | Username | Password |
|------|----------|----------|
| Assigner | `assigner` | `assign123` |
| Cross Checker | `crosschecker` | `check123` |
| Executor Public | `executor_public` | `exec123` |
| Executor Private | `executor_private` | `execpriv123` |
| Main Admin | `admin` | `admin123` |

## 📂 Directory Structure

```
project/
├── app.py                 # Main Flask application
├── run.py                 # Application runner
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── file_management.db    # SQLite database (auto-created)
└── templates/            # HTML templates
    ├── base.html         # Base template
    ├── login.html        # Login page
    ├── dashboard.html    # Dashboard
    ├── assigner.html     # Assigner interface
    ├── cross_checker.html # Cross checker interface
    ├── executor_public.html # Public executor interface
    ├── executor_private.html # Private executor interface
    └── admin.html        # Admin panel
```

## 🛠️ Configuration

### File Paths
The application is configured with these default paths:
- **Source Path:** `O:\video\00_restore\00_Archive_Stems\for spliting reference`
- **Destination Path:** `T:\To_Process\Rough folder`

To change these paths, edit the `Config` class in `app.py`:

```python
class Config:
    SOURCE_PATH = r"your\source\path"
    DEST_PATH = r"your\destination\path"
```

### Database
The application uses SQLite database (`file_management.db`) which is automatically created on first run.

## 👤 User Workflows

### 1. Assigner Workflow
1. Login with assigner credentials
2. Browse source folders in the left panel
3. Select video files from folders
4. Choose processing category
5. For social media categories, enter URL
6. Process selected files

### 2. Cross Checker Workflow
1. Login with cross checker credentials
2. Review files assigned by Assigner
3. Approve or reject assignments
4. Add notes if needed

### 3. Executor Workflows
1. Login with executor credentials
2. View cross-checked files ready for processing
3. Process files and mark as completed
4. Add processing notes

### 4. Admin Workflow
1. Login with admin credentials
2. View system statistics
3. Manage users (create, activate/deactivate, delete)
4. Monitor file processing statistics

## 🔧 Technical Details

### Backend
- **Framework:** Flask 2.3.3
- **Database:** SQLite with custom schema
- **Authentication:** Session-based with password hashing
- **File Operations:** Python os/shutil modules

### Frontend
- **UI Framework:** Bootstrap 5.3.0
- **Icons:** Font Awesome 6.4.0
- **JavaScript:** jQuery 3.7.0
- **Responsive Design:** Mobile-friendly interface

### Database Schema
- **users** - User accounts and roles
- **files** - File tracking and metadata
- **operations_log** - Activity logging

## 🎨 Features Highlights

### Modern UI/UX
- Gradient backgrounds and animations
- Responsive design for all screen sizes
- Interactive file selection
- Real-time status updates
- Loading indicators and progress feedback

### File Processing
- Automatic directory structure creation
- File copying/moving based on category
- Multiple output handling for complex workflows
- URL tracking for social media content

### Security
- Role-based page access
- Secure password storage
- Session timeout handling
- Private file access restrictions

## 🔍 API Endpoints

### Authentication
- `POST /login` - User login
- `GET /logout` - User logout

### File Operations
- `GET /api/folders` - Get folder structure
- `GET /api/files/<path>` - Get files in folder
- `POST /api/assign-files` - Assign files to categories
- `POST /api/cross-check` - Cross-check files
- `POST /api/process-file` - Process files

### Admin Operations
- `POST /api/admin/users` - User management

## 🚨 Important Notes

1. **File Paths:** Ensure source and destination paths exist and are accessible
2. **Permissions:** Application needs read/write access to configured directories
3. **Video Files:** Only video files with supported extensions are processed
4. **Database:** SQLite database is created automatically on first run
5. **Security:** Change default passwords in production environment

## 🐛 Troubleshooting

### Common Issues

1. **Path not found errors:**
   - Verify source and destination paths exist
   - Check file permissions

2. **Database errors:**
   - Delete `file_management.db` to reset database
   - Restart application

3. **Login issues:**
   - Use exact credentials provided
   - Check for typos in username/password

## 📝 License

This project is created for educational and demonstration purposes.

## 🤝 Support

For issues or questions, please check the troubleshooting section above or review the code comments for detailed implementation details.

---

**🎉 Enjoy using the Multi-User File Management System!**
