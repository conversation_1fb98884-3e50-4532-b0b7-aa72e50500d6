#!/usr/bin/env python3
"""
Test URL encoding for video paths
"""

import urllib.parse

# Test path
test_path = r"T:\To_Process\Rough folder\restored files\Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019\Output\Cauvery-Calling-core-video-2_5min-V2.mov"

print("Original path:")
print(test_path)
print()

print("URL encoded:")
encoded = urllib.parse.quote(test_path, safe='')
print(encoded)
print()

print("URL decoded:")
decoded = urllib.parse.unquote(encoded)
print(decoded)
print()

print("Are they equal?", test_path == decoded)
print()

# Test the problematic URL from logs
bad_url = "T:To_ProcessRough%20folder%0Destored%20filesZ6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019OutputCauvery-Calling-core-video-2_5min-V2.mov"
print("Bad URL from logs:")
print(bad_url)
print()

print("Decoded bad URL:")
decoded_bad = urllib.parse.unquote(bad_url)
print(decoded_bad)
print()

# Test with safe characters
print("URL encoded with safe=':':")
encoded_safe = urllib.parse.quote(test_path, safe=':')
print(encoded_safe)
print()

print("URL encoded with safe=':\':")
encoded_safe2 = urllib.parse.quote(test_path, safe=':\\')
print(encoded_safe2)
