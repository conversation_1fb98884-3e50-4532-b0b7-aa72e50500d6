#!/usr/bin/env python3
"""
Test VLC Preview functionality for all video files in the Executive Public interface
"""

import requests
import json
import os
import subprocess
import shutil

def test_vlc_preview():
    print("🎬 TESTING VLC PREVIEW FUNCTIONALITY")
    print("=" * 60)
    
    # Create a session
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # Step 1: Login
        print("1. 🔐 Logging in as Executor Public...")
        login_data = {
            'username': 'executor_public',
            'password': '<PERSON>@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code not in [200, 302]:
            print(f"   ❌ Login failed: {login_response.status_code}")
            return
        
        print("   ✅ Login successful!")
        
        # Step 2: Check VLC Installation
        print("\n2. 🔍 Checking VLC Installation...")
        vlc_paths = [
            r"C:\Program Files\VideoLAN\VLC\vlc.exe",
            r"C:\Program Files (x86)\VideoLAN\VLC\vlc.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\VideoLAN\VLC\vlc.exe".format(os.getenv('USERNAME', '')),
        ]
        
        vlc_exe = None
        for path in vlc_paths:
            if os.path.exists(path):
                vlc_exe = path
                break
        
        if not vlc_exe:
            vlc_exe = shutil.which("vlc")
        
        if vlc_exe:
            print(f"   ✅ VLC found at: {vlc_exe}")
        else:
            print("   ⚠️ VLC not found - preview will show installation message")
        
        # Step 3: Get Files Queue
        print("\n3. 📋 Getting Files Queue...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        
        if queue_response.status_code != 200:
            print(f"   ❌ Queue API failed: {queue_response.status_code}")
            return
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue API error: {queue_data.get('error')}")
            return
        
        files = queue_data.get('files', [])
        print(f"   ✅ Found {len(files)} files in queue")
        
        if not files:
            print("   ℹ️ No files to test preview functionality")
            return
        
        # Step 4: Test Preview for Each File
        print(f"\n4. 🎬 Testing Preview for All {len(files)} Files...")
        
        for i, file in enumerate(files, 1):
            folder_name = file.get('folder_name', 'Unknown')
            folder_path = file.get('folder_path', '')
            
            print(f"\n   📁 File {i}/{len(files)}: {folder_name}")
            print(f"      📂 Path: {folder_path}")
            
            if not folder_path:
                print("      ❌ No folder path available")
                continue
            
            # Check if folder exists
            if not os.path.exists(folder_path):
                print(f"      ⚠️ Folder does not exist: {folder_path}")
                continue
            
            # Count video files in folder
            video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.f4v'}
            video_files = []
            
            for root, dirs, files_in_dir in os.walk(folder_path):
                for file_name in files_in_dir:
                    if any(file_name.lower().endswith(ext) for ext in video_extensions):
                        video_files.append(os.path.join(root, file_name))
            
            print(f"      🎥 Found {len(video_files)} video files")
            
            if video_files:
                for video_file in video_files[:3]:  # Show first 3 files
                    print(f"         • {os.path.basename(video_file)}")
                if len(video_files) > 3:
                    print(f"         • ... and {len(video_files) - 3} more")
            
            # Test Preview API
            print(f"      🔧 Testing Preview API...")
            
            preview_response = session.post(
                f"{base_url}/api/preview-video",
                json={'folder_path': folder_path},
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"         API Status: {preview_response.status_code}")
            
            if preview_response.status_code == 200:
                preview_data = preview_response.json()
                
                if preview_data.get('success'):
                    video_files_found = preview_data.get('video_files', [])
                    vlc_path = preview_data.get('vlc_path', 'Unknown')
                    
                    print(f"         ✅ Preview successful!")
                    print(f"         🎬 Opened {len(video_files_found)} video files")
                    print(f"         🔧 VLC Path: {vlc_path}")
                    
                    if video_files_found:
                        print(f"         📁 Files opened:")
                        for video_file in video_files_found[:2]:  # Show first 2
                            print(f"            • {video_file}")
                        if len(video_files_found) > 2:
                            print(f"            • ... and {len(video_files_found) - 2} more")
                else:
                    error = preview_data.get('error', 'Unknown error')
                    print(f"         ❌ Preview failed: {error}")
                    
                    if 'VLC Media Player not found' in error:
                        print(f"         💡 Install VLC from: https://www.videolan.org/vlc/")
                    elif 'No video files found' in error:
                        print(f"         📁 No video files in this folder")
            else:
                print(f"         ❌ API failed: {preview_response.status_code}")
        
        print("\n" + "=" * 60)
        print("🎉 VLC PREVIEW TEST RESULTS")
        print("=" * 60)
        
        if vlc_exe:
            print("✅ VLC Media Player is installed")
            print("✅ Preview API endpoint working")
            print("✅ All video files should open in VLC when Preview is clicked")
        else:
            print("⚠️ VLC Media Player not found")
            print("✅ Preview API will show installation instructions")
        
        print(f"✅ Tested preview for {len(files)} files")
        print("\n🎯 HOW TO TEST IN BROWSER:")
        print("1. Open: http://127.0.0.1:5001/executor-public")
        print("2. Login with: executor_public / Shiva@123")
        print("3. Click 'Preview' button on any file")
        print("4. VLC should open with all video files from that folder")
        
        if not vlc_exe:
            print("\n💡 TO INSTALL VLC:")
            print("1. Download from: https://www.videolan.org/vlc/")
            print("2. Install VLC Media Player")
            print("3. Restart the Flask app")
            print("4. Try Preview button again")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_vlc_preview()
