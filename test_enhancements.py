#!/usr/bin/env python3
"""
Test script to verify the enhanced Archives management system functionality
"""

import requests
import json
import os
import sys

# Configuration
BASE_URL = "http://127.0.0.1:5001"
TEST_USERNAME = "assigner"
TEST_PASSWORD = "Shiva@123"

def test_login():
    """Test login functionality"""
    print("Testing login...")
    
    session = requests.Session()
    
    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    if response.status_code != 200:
        print(f"❌ Failed to access login page: {response.status_code}")
        return None
    
    # Login
    login_data = {
        'username': TEST_USERNAME,
        'password': TEST_PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200 and "dashboard" in response.url:
        print("✅ Login successful")
        return session
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def test_directory_tree(session):
    """Test directory tree API"""
    print("Testing directory tree API...")
    
    response = session.get(f"{BASE_URL}/api/directory-tree")
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"✅ Directory tree loaded successfully")
            print(f"   Found {data['tree'].get('video_count', 0)} video files")
            return True
        else:
            print(f"❌ Directory tree API error: {data.get('error')}")
            return False
    else:
        print(f"❌ Directory tree API failed: {response.status_code}")
        return False

def test_folder_contents(session):
    """Test folder contents API"""
    print("Testing folder contents API...")
    
    # Test with empty folder path (root)
    test_data = {'folder_path': ''}
    response = session.post(
        f"{BASE_URL}/api/folder-contents/",
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            files = data.get('files', [])
            print(f"✅ Folder contents API working")
            print(f"   Found {len(files)} files in root directory")
            
            # Test supported file extensions
            supported_extensions = ['.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.mxf', '.m4v', '.3gp', '.f4v']
            found_extensions = set()
            for file in files:
                ext = os.path.splitext(file['name'])[1].lower()
                if ext in supported_extensions:
                    found_extensions.add(ext)
            
            if found_extensions:
                print(f"   Supported file types found: {', '.join(sorted(found_extensions))}")
            
            return True
        else:
            print(f"❌ Folder contents API error: {data.get('error')}")
            return False
    else:
        print(f"❌ Folder contents API failed: {response.status_code}")
        return False

def test_enhanced_assign_api(session):
    """Test enhanced assignment API (dry run)"""
    print("Testing enhanced assignment API...")
    
    # This is a dry run test - we won't actually assign files
    test_data = {
        'file_paths': [],  # Empty for testing
        'category': 'Internal video with stems',
        'assign_to': 'Executor Public',
        'video_ids': 'TEST001',
        'url': '',
        'remarks': 'Test assignment'
    }
    
    response = session.post(
        f"{BASE_URL}/api/enhanced-batch-assign",
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ Enhanced assignment API working")
            print(f"   Processed: {data.get('processed', 0)} files")
            print(f"   Failed: {data.get('failed', 0)} files")
            return True
        else:
            # Expected to fail with empty file list
            if "Missing required data" in data.get('error', ''):
                print("✅ Enhanced assignment API validation working")
                return True
            else:
                print(f"❌ Enhanced assignment API error: {data.get('error')}")
                return False
    else:
        print(f"❌ Enhanced assignment API failed: {response.status_code}")
        return False

def test_tree_assigner_page(session):
    """Test tree assigner page loads"""
    print("Testing tree assigner page...")
    
    response = session.get(f"{BASE_URL}/tree-assigner")
    if response.status_code == 200:
        content = response.text
        if "Archives Tree View" in content and "Directory Tree" in content:
            print("✅ Tree assigner page loads successfully")
            return True
        else:
            print("❌ Tree assigner page content incomplete")
            return False
    else:
        print(f"❌ Tree assigner page failed: {response.status_code}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Archives Management System Enhancement Tests\n")
    
    # Test login
    session = test_login()
    if not session:
        print("\n❌ Cannot proceed without login")
        sys.exit(1)
    
    print()
    
    # Run tests
    tests = [
        test_tree_assigner_page,
        test_directory_tree,
        test_folder_contents,
        test_enhanced_assign_api
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test(session):
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    # Summary
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced functionality is working correctly.")
    else:
        print(f"⚠️  {total - passed} tests failed. Please check the issues above.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
