#!/usr/bin/env python3
"""
Multi-User File Management System
Run script for the Flask application
"""

import os
import sys
from app import app

def main():
    """Main function to run the Flask application"""
    
    print("=" * 60)
    print("🚀 MULTI-USER FILE MANAGEMENT SYSTEM")
    print("=" * 60)
    print()
    print("📁 Source Path: O:\\video\\00_restore\\00_Archive_Stems\\for spliting reference")
    print("📂 Destination Path: T:\\To_Process\\Rough folder")
    print()
    print("👥 Available User Roles:")
    print("   • Assigner - Assigns files to processing categories")
    print("   • Cross Checker - Reviews and validates assigned files")
    print("   • Executor Public - Processes public files")
    print("   • Executor Private - Processes all files including private")
    print("   • Main Admin - System administration and user management")
    print()
    print("🔐 Demo Login Credentials:")
    print("   • Assigner: assigner / assign123")
    print("   • Cross Checker: crosschecker / check123")
    print("   • Executor Public: executor_public / exec123")
    print("   • Executor Private: executor_private / execpriv123")
    print("   • Main Admin: admin / admin123")
    print()
    print("🌐 Starting Flask application...")
    print("   URL: http://localhost:5001")
    print("   Alternative: http://127.0.0.1:5001")
    print("   Debug Mode: Enabled")
    print()
    print("=" * 60)

    try:
        # Run the Flask application
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5001,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n\n🛑 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
