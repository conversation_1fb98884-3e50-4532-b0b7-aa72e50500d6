#!/usr/bin/env python3
"""
Quick test to verify the system is working
"""

import os
import sys
import sqlite3
from datetime import datetime

def test_database():
    """Test database connectivity"""
    try:
        conn = sqlite3.connect('archives.db')
        cursor = conn.cursor()
        
        # Test if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"✅ Database connected successfully")
        print(f"📊 Found {len(tables)} tables: {[t[0] for t in tables]}")
        
        # Test users table
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"👥 Users in database: {user_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_directories():
    """Test required directories"""
    required_dirs = [
        r"T:\To_Process\Rough folder\restored files",
        r"T:\To_Process\Rough folder\Folder to be cross checked",
        r"T:\To_Process\Rough folder\Audio Sent to TR",
        r"T:\To_Process\Rough folder\Audios Repository",
        r"T:\To_Process\Rough folder\To Reingest",
        r"T:\To_Process\Rough folder\To Be Deleted"
    ]
    
    existing_dirs = 0
    for directory in required_dirs:
        if os.path.exists(directory):
            existing_dirs += 1
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory} - NOT FOUND")
            # Create the directory
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"✅ Created: {directory}")
                existing_dirs += 1
            except Exception as e:
                print(f"❌ Failed to create {directory}: {e}")
    
    print(f"📁 Directory status: {existing_dirs}/{len(required_dirs)} available")
    return existing_dirs == len(required_dirs)

def test_flask_import():
    """Test Flask application import"""
    try:
        import app
        print("✅ Flask app imported successfully")
        
        # Test app configuration
        print(f"📱 App name: {app.app.name}")
        print(f"🔧 Debug mode: {app.app.debug}")
        
        return True
        
    except Exception as e:
        print(f"❌ Flask import failed: {e}")
        return False

def main():
    print("🚀 Quick System Test")
    print("=" * 40)
    
    tests = [
        ("Database connectivity", test_database),
        ("Directory structure", test_directories),
        ("Flask application", test_flask_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Testing: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
