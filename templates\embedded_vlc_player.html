{% extends "base.html" %}

{% block title %}Embedded VLC Media Player - Archives Management System{% endblock %}

{% block extra_css %}
<style>
    .vlc-player-container {
        background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        color: white;
    }

    .video-display {
        background: #000;
        border-radius: 10px;
        width: 100%;
        height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }

    .video-placeholder {
        color: #666;
        font-size: 1.2rem;
        text-align: center;
    }

    .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .video-overlay.show {
        opacity: 1;
    }

    .video-info {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(0,0,0,0.8);
        padding: 10px;
        border-radius: 5px;
        font-size: 0.9rem;
    }

    .controls-panel {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .control-group {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }

    .control-group:last-child {
        margin-bottom: 0;
    }

    .control-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 10px 15px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .control-btn:disabled {
        background: #666;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .progress-container {
        flex: 1;
        margin: 0 15px;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(255,255,255,0.2);
        border-radius: 4px;
        cursor: pointer;
        position: relative;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 4px;
        width: 0%;
        transition: width 0.1s ease;
    }

    .time-display {
        color: #ccc;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        min-width: 100px;
        text-align: center;
    }

    .volume-control {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .volume-slider {
        width: 100px;
        height: 6px;
        background: rgba(255,255,255,0.2);
        border-radius: 3px;
        outline: none;
        cursor: pointer;
    }

    .file-browser {
        background: rgba(255,255,255,0.05);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        max-height: 300px;
        overflow-y: auto;
    }

    .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        margin: 2px 0;
        border-radius: 6px;
        cursor: pointer;
        transition: background 0.2s ease;
    }

    .file-item:hover {
        background: rgba(255,255,255,0.1);
    }

    .file-item.selected {
        background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .file-icon {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    .status-panel {
        background: rgba(255,255,255,0.05);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }

    .status-item:last-child {
        margin-bottom: 0;
    }

    .status-label {
        color: #ccc;
    }

    .status-value {
        color: #fff;
        font-weight: bold;
    }

    .loading-spinner {
        border: 3px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        border-top: 3px solid #667eea;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .error-message {
        background: rgba(220, 53, 69, 0.2);
        border: 1px solid #dc3545;
        color: #dc3545;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
    }

    .success-message {
        background: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        color: #28a745;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
    }

    @media (max-width: 768px) {
        .control-group {
            flex-wrap: wrap;
        }
        
        .progress-container {
            order: 3;
            width: 100%;
            margin: 10px 0;
        }
        
        .video-display {
            height: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-6 mb-0">
                <i class="fas fa-play-circle me-2 text-gradient"></i>Embedded VLC Media Player
            </h1>
            <p class="text-muted">Standalone video player with full VLC capabilities</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- VLC Player Container -->
            <div class="vlc-player-container">
                <div class="video-display" id="videoDisplay">
                    <div class="video-placeholder" id="videoPlaceholder">
                        <i class="fas fa-video fa-3x mb-3"></i>
                        <p>Select a video file to start playback</p>
                    </div>
                    
                    <div class="video-overlay" id="videoOverlay">
                        <div class="loading-spinner"></div>
                    </div>
                    
                    <div class="video-info" id="videoInfo" style="display: none;">
                        <div><strong id="videoTitle">No media loaded</strong></div>
                        <div><small id="videoPath"></small></div>
                    </div>
                </div>

                <!-- Media Controls -->
                <div class="controls-panel">
                    <div class="control-group">
                        <button class="control-btn" id="playBtn" onclick="togglePlayPause()" disabled>
                            <i class="fas fa-play"></i> Play
                        </button>
                        <button class="control-btn" id="stopBtn" onclick="stopPlayback()" disabled>
                            <i class="fas fa-stop"></i> Stop
                        </button>
                        
                        <div class="progress-container">
                            <div class="progress-bar" id="progressBar" onclick="seekTo(event)">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                        </div>
                        
                        <div class="time-display" id="timeDisplay">00:00 / 00:00</div>
                    </div>
                    
                    <div class="control-group">
                        <div class="volume-control">
                            <i class="fas fa-volume-up"></i>
                            <input type="range" class="volume-slider" id="volumeSlider" 
                                   min="0" max="100" value="50" onchange="setVolume(this.value)">
                            <span id="volumeDisplay">50%</span>
                        </div>
                        
                        <button class="control-btn" onclick="openExternalVLC()" id="externalVLCBtn" disabled>
                            <i class="fas fa-external-link-alt"></i> Open in VLC
                        </button>
                        
                        <button class="control-btn" onclick="refreshPlayer()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Status Panel -->
                <div class="status-panel">
                    <h6><i class="fas fa-info-circle me-2"></i>Player Status</h6>
                    <div class="status-item">
                        <span class="status-label">State:</span>
                        <span class="status-value" id="playerState">Idle</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Session:</span>
                        <span class="status-value" id="sessionId">Not connected</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">VLC Version:</span>
                        <span class="status-value" id="vlcVersion">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- File Browser -->
            <div class="vlc-player-container">
                <h6><i class="fas fa-folder-open me-2"></i>Media Files</h6>
                <div class="file-browser" id="fileBrowser">
                    <div class="text-center py-4">
                        <div class="loading-spinner mx-auto mb-3"></div>
                        <p>Loading media files...</p>
                    </div>
                </div>
                
                <button class="control-btn w-100" onclick="refreshFileList()">
                    <i class="fas fa-refresh me-2"></i>Refresh Files
                </button>
            </div>

            <!-- Quick Actions -->
            <div class="vlc-player-container">
                <h6><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
                <div class="d-grid gap-2">
                    <button class="control-btn" onclick="loadTestVideo()">
                        <i class="fas fa-play-circle me-2"></i>Load Test Video
                    </button>
                    <button class="control-btn" onclick="showVLCInfo()">
                        <i class="fas fa-info me-2"></i>VLC Information
                    </button>
                    <button class="control-btn" onclick="clearPlayer()">
                        <i class="fas fa-trash me-2"></i>Clear Player
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Messages Container -->
<div id="messagesContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>

<script>
    // Global variables
    let vlcSessionId = null;
    let currentMediaPath = null;
    let isPlaying = false;
    let statusUpdateInterval = null;
    let mediaFiles = [];

    // Initialize player on page load
    document.addEventListener('DOMContentLoaded', function() {
        initializeVLCPlayer();
        loadVLCInfo();
        loadMediaFiles();
    });

    // Initialize VLC player session
    async function initializeVLCPlayer() {
        try {
            showMessage('Initializing VLC player...', 'info');
            
            const response = await fetch('/api/vlc/create-session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const data = await response.json();
            
            if (data.success) {
                vlcSessionId = data.session_id;
                document.getElementById('sessionId').textContent = vlcSessionId.substring(0, 8) + '...';
                showMessage('VLC player initialized successfully!', 'success');
                startStatusUpdates();
            } else {
                showMessage('Failed to initialize VLC: ' + data.error, 'error');
            }
        } catch (error) {
            showMessage('Error initializing VLC: ' + error.message, 'error');
        }
    }

    // Load VLC information
    async function loadVLCInfo() {
        try {
            const response = await fetch('/api/vlc/info');
            const data = await response.json();

            if (data.success) {
                document.getElementById('vlcVersion').textContent = data.info.version || 'Unknown';
            }
        } catch (error) {
            console.error('Error loading VLC info:', error);
        }
    }

    // Load media files from source directory
    async function loadMediaFiles() {
        try {
            const response = await fetch('/api/get-folders');
            const data = await response.json();

            if (data.success) {
                mediaFiles = [];
                extractMediaFiles(data.folders);
                renderFileList();
            } else {
                showMessage('Error loading media files: ' + data.error, 'error');
            }
        } catch (error) {
            showMessage('Error loading media files: ' + error.message, 'error');
        }
    }

    // Extract media files from folder structure
    function extractMediaFiles(folders) {
        folders.forEach(item => {
            if (item.type === 'file' && item.is_media) {
                mediaFiles.push(item);
            } else if (item.type === 'folder' && item.children) {
                extractMediaFiles(item.children);
            }
        });
    }

    // Render file list
    function renderFileList() {
        const browser = document.getElementById('fileBrowser');

        if (mediaFiles.length === 0) {
            browser.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-folder-open fa-2x mb-3" style="color: #666;"></i>
                    <p>No media files found</p>
                </div>
            `;
            return;
        }

        let html = '';
        mediaFiles.slice(0, 50).forEach((file, index) => {
            const icon = getFileIcon(file.extension);
            html += `
                <div class="file-item" onclick="selectMediaFile('${file.path}', '${file.name}', ${index})">
                    <div class="file-icon">
                        <i class="${icon}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div style="font-weight: 500;">${file.name}</div>
                        <small style="color: #ccc;">${file.size}</small>
                    </div>
                </div>
            `;
        });

        browser.innerHTML = html;
    }

    // Get file icon based on extension
    function getFileIcon(extension) {
        const iconMap = {
            '.mp4': 'fas fa-file-video text-primary',
            '.avi': 'fas fa-file-video text-primary',
            '.mov': 'fas fa-file-video text-primary',
            '.mkv': 'fas fa-file-video text-primary',
            '.wmv': 'fas fa-file-video text-primary',
            '.mp3': 'fas fa-file-audio text-success',
            '.wav': 'fas fa-file-audio text-success',
            '.m4a': 'fas fa-file-audio text-success'
        };
        return iconMap[extension] || 'fas fa-file text-secondary';
    }

    // Select media file for playback
    async function selectMediaFile(filePath, fileName, index) {
        try {
            // Update UI
            document.querySelectorAll('.file-item').forEach(item => item.classList.remove('selected'));
            document.querySelectorAll('.file-item')[index].classList.add('selected');

            showMessage(`Loading ${fileName}...`, 'info');
            showVideoOverlay();

            // Load media in VLC
            const response = await fetch('/api/vlc/load-media', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_id: vlcSessionId,
                    file_path: filePath
                })
            });

            const data = await response.json();

            if (data.success) {
                currentMediaPath = filePath;
                document.getElementById('videoTitle').textContent = fileName;
                document.getElementById('videoPath').textContent = filePath;
                document.getElementById('videoInfo').style.display = 'block';

                // Enable controls
                document.getElementById('playBtn').disabled = false;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('externalVLCBtn').disabled = false;

                hideVideoOverlay();
                showMessage(`${fileName} loaded successfully!`, 'success');
            } else {
                hideVideoOverlay();
                showMessage('Error loading media: ' + data.error, 'error');
            }
        } catch (error) {
            hideVideoOverlay();
            showMessage('Error loading media: ' + error.message, 'error');
        }
    }

    // Toggle play/pause
    async function togglePlayPause() {
        if (!vlcSessionId || !currentMediaPath) {
            showMessage('No media loaded', 'warning');
            return;
        }

        try {
            const action = isPlaying ? 'pause' : 'play';

            const response = await fetch('/api/vlc/control', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_id: vlcSessionId,
                    action: action
                })
            });

            const data = await response.json();

            if (data.success) {
                isPlaying = !isPlaying;
                updatePlayButton();
                showMessage(data.message, 'success');
            } else {
                showMessage('Control error: ' + data.error, 'error');
            }
        } catch (error) {
            showMessage('Control error: ' + error.message, 'error');
        }
    }

    // Stop playback
    async function stopPlayback() {
        if (!vlcSessionId) return;

        try {
            const response = await fetch('/api/vlc/control', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_id: vlcSessionId,
                    action: 'stop'
                })
            });

            const data = await response.json();

            if (data.success) {
                isPlaying = false;
                updatePlayButton();
                showMessage('Playback stopped', 'success');
            }
        } catch (error) {
            showMessage('Stop error: ' + error.message, 'error');
        }
    }

    // Set volume
    async function setVolume(volume) {
        if (!vlcSessionId) return;

        try {
            const response = await fetch('/api/vlc/control', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_id: vlcSessionId,
                    action: 'volume',
                    volume: volume
                })
            });

            document.getElementById('volumeDisplay').textContent = volume + '%';
        } catch (error) {
            console.error('Volume error:', error);
        }
    }

    // Seek to position
    async function seekTo(event) {
        if (!vlcSessionId || !currentMediaPath) return;

        const progressBar = event.currentTarget;
        const rect = progressBar.getBoundingClientRect();
        const percentage = (event.clientX - rect.left) / rect.width;

        try {
            // Get current status to calculate position
            const statusResponse = await fetch(`/api/vlc/status?session_id=${vlcSessionId}`);
            const statusData = await statusResponse.json();

            if (statusData.success && statusData.status.duration > 0) {
                const seekPosition = percentage * statusData.status.duration;

                const response = await fetch('/api/vlc/control', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        session_id: vlcSessionId,
                        action: 'seek',
                        position: seekPosition
                    })
                });

                const data = await response.json();
                if (data.success) {
                    showMessage(`Seeked to ${formatTime(seekPosition)}`, 'success');
                }
            }
        } catch (error) {
            console.error('Seek error:', error);
        }
    }

    // Update play button state
    function updatePlayButton() {
        const playBtn = document.getElementById('playBtn');
        if (isPlaying) {
            playBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        } else {
            playBtn.innerHTML = '<i class="fas fa-play"></i> Play';
        }
    }

    // Start status updates
    function startStatusUpdates() {
        if (statusUpdateInterval) {
            clearInterval(statusUpdateInterval);
        }

        statusUpdateInterval = setInterval(updatePlayerStatus, 1000);
    }

    // Update player status
    async function updatePlayerStatus() {
        if (!vlcSessionId) return;

        try {
            const response = await fetch(`/api/vlc/status?session_id=${vlcSessionId}`);
            const data = await response.json();

            if (data.success) {
                const status = data.status;

                // Update state
                document.getElementById('playerState').textContent = status.state.charAt(0).toUpperCase() + status.state.slice(1);

                // Update progress
                if (status.length > 0) {
                    const progressPercent = (status.position * 100);
                    document.getElementById('progressFill').style.width = progressPercent + '%';

                    // Update time display
                    const currentTime = formatTime(status.current_time);
                    const duration = formatTime(status.duration);
                    document.getElementById('timeDisplay').textContent = `${currentTime} / ${duration}`;
                }

                // Update playing state
                isPlaying = (status.state === 'playing');
                updatePlayButton();
            }
        } catch (error) {
            console.error('Status update error:', error);
        }
    }

    // Format time in MM:SS format
    function formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // Show/hide video overlay
    function showVideoOverlay() {
        document.getElementById('videoOverlay').classList.add('show');
    }

    function hideVideoOverlay() {
        document.getElementById('videoOverlay').classList.remove('show');
    }

    // Open in external VLC
    async function openExternalVLC() {
        if (!currentMediaPath) {
            showMessage('No media loaded', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/open-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ file_path: currentMediaPath })
            });

            const data = await response.json();

            if (data.success) {
                showMessage('Opened in external VLC', 'success');
            } else {
                showMessage('Error opening VLC: ' + data.error, 'error');
            }
        } catch (error) {
            showMessage('Error opening VLC: ' + error.message, 'error');
        }
    }

    // Refresh player
    function refreshPlayer() {
        if (statusUpdateInterval) {
            clearInterval(statusUpdateInterval);
        }

        vlcSessionId = null;
        currentMediaPath = null;
        isPlaying = false;

        // Reset UI
        document.getElementById('videoInfo').style.display = 'none';
        document.getElementById('playBtn').disabled = true;
        document.getElementById('stopBtn').disabled = true;
        document.getElementById('externalVLCBtn').disabled = true;
        document.getElementById('progressFill').style.width = '0%';
        document.getElementById('timeDisplay').textContent = '00:00 / 00:00';
        document.getElementById('playerState').textContent = 'Idle';
        document.getElementById('sessionId').textContent = 'Not connected';

        // Reinitialize
        initializeVLCPlayer();
    }

    // Refresh file list
    function refreshFileList() {
        loadMediaFiles();
    }

    // Load test video
    function loadTestVideo() {
        if (mediaFiles.length > 0) {
            selectMediaFile(mediaFiles[0].path, mediaFiles[0].name, 0);
        } else {
            showMessage('No media files available for testing', 'warning');
        }
    }

    // Show VLC info
    function showVLCInfo() {
        loadVLCInfo();
        showMessage('VLC information updated', 'info');
    }

    // Clear player
    async function clearPlayer() {
        if (vlcSessionId) {
            try {
                await fetch('/api/vlc/cleanup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ session_id: vlcSessionId })
                });
            } catch (error) {
                console.error('Cleanup error:', error);
            }
        }

        refreshPlayer();
        showMessage('Player cleared', 'success');
    }

    // Show message
    function showMessage(message, type = 'info') {
        const container = document.getElementById('messagesContainer');
        const messageDiv = document.createElement('div');

        const typeClass = {
            'success': 'success-message',
            'error': 'error-message',
            'warning': 'error-message',
            'info': 'success-message'
        }[type] || 'success-message';

        messageDiv.className = typeClass;
        messageDiv.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; cursor: pointer; font-size: 1.2rem;">&times;</button>
            </div>
        `;

        container.appendChild(messageDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        if (vlcSessionId) {
            navigator.sendBeacon('/api/vlc/cleanup', JSON.stringify({ session_id: vlcSessionId }));
        }
    });
</script>

{% endblock %}
