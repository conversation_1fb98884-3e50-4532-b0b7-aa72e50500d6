#!/usr/bin/env python3
"""
Performance Test for Archives Department File Management System
Tests system performance with multiple concurrent operations
"""

import os
import sys
import time
import json
import threading
import tempfile
from datetime import datetime
from pathlib import Path

class PerformanceTest:
    def __init__(self):
        self.test_results = []
        self.test_files_created = []
        
        # Test paths
        self.test_paths = {
            'source': r"T:\To_Process\Rough folder\restored files",
            'cross_check': r"T:\To_Process\Rough folder\Folder to be cross checked",
            'audio_tr': r"T:\To_Process\Rough folder\Audio Sent to TR",
            'audio_repo': r"T:\To_Process\Rough folder\Audios Repository"
        }

    def log_test(self, test_name, status, duration, details=""):
        """Log test results"""
        result = {
            'test_name': test_name,
            'status': status,
            'duration': duration,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status} ({duration:.2f}s)")
        if details:
            print(f"   Details: {details}")

    def create_test_files(self, count=10):
        """Create multiple test files for performance testing"""
        print(f"\n📁 Creating {count} test files...")
        
        start_time = time.time()
        created_files = []
        
        try:
            for i in range(count):
                # Create test folder
                test_folder = os.path.join(
                    self.test_paths['source'], 
                    f"PERF_TEST_{i}_{datetime.now().strftime('%H%M%S')}"
                )
                os.makedirs(test_folder, exist_ok=True)
                
                # Create test video file
                test_video = os.path.join(test_folder, f"test_video_{i}.mp4")
                with open(test_video, 'wb') as f:
                    f.write(b'FAKE_VIDEO_DATA' * 1000)  # ~15KB file
                
                # Create test audio file
                test_audio = os.path.join(test_folder, f"test_audio_{i}.wav")
                with open(test_audio, 'wb') as f:
                    f.write(b'RIFF\x00\x00\x00\x00WAVEfmt \x00\x00\x00\x00' * 100)
                
                # Create metadata file
                metadata_file = os.path.join(test_folder, "metadata.json")
                with open(metadata_file, 'w') as f:
                    json.dump({
                        'folder_name': f'PERF_TEST_{i}',
                        'video_type': 'Internal Only Output',
                        'department': 'Archives',
                        'created': datetime.now().isoformat(),
                        'test_file': True
                    }, f)
                
                created_files.extend([test_folder, test_video, test_audio, metadata_file])
            
            duration = time.time() - start_time
            self.test_files_created.extend(created_files)
            
            self.log_test(
                f"Create {count} test files", 
                "PASS", 
                duration,
                f"Created {len(created_files)} files and folders"
            )
            
            return created_files
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(f"Create {count} test files", "FAIL", duration, str(e))
            return []

    def test_concurrent_file_operations(self, thread_count=5):
        """Test concurrent file operations"""
        print(f"\n🔄 Testing {thread_count} concurrent file operations...")
        
        def file_operation_worker(worker_id):
            """Worker function for concurrent operations"""
            try:
                # Create a test file
                test_file = os.path.join(
                    self.test_paths['source'],
                    f"concurrent_test_{worker_id}_{datetime.now().strftime('%H%M%S')}.txt"
                )
                
                with open(test_file, 'w') as f:
                    f.write(f"Concurrent test file {worker_id}\n" * 100)
                
                # Simulate processing time
                time.sleep(0.5)
                
                # Move file to cross-check
                dest_file = os.path.join(
                    self.test_paths['cross_check'],
                    os.path.basename(test_file)
                )
                
                os.rename(test_file, dest_file)
                self.test_files_created.append(dest_file)
                
                return True
                
            except Exception as e:
                print(f"Worker {worker_id} failed: {e}")
                return False
        
        start_time = time.time()
        threads = []
        
        # Start concurrent operations
        for i in range(thread_count):
            thread = threading.Thread(target=file_operation_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        duration = time.time() - start_time
        
        self.log_test(
            f"Concurrent operations ({thread_count} threads)",
            "PASS",
            duration,
            f"Completed {thread_count} concurrent file operations"
        )

    def test_large_file_handling(self):
        """Test handling of large files"""
        print("\n📦 Testing large file handling...")
        
        start_time = time.time()
        
        try:
            # Create a larger test file (1MB)
            large_file = os.path.join(
                self.test_paths['source'],
                f"large_test_{datetime.now().strftime('%H%M%S')}.dat"
            )
            
            with open(large_file, 'wb') as f:
                f.write(b'LARGE_FILE_DATA' * 70000)  # ~1MB
            
            self.test_files_created.append(large_file)
            
            # Test file operations
            file_size = os.path.getsize(large_file)
            
            # Move to cross-check
            dest_file = os.path.join(
                self.test_paths['cross_check'],
                os.path.basename(large_file)
            )
            
            os.rename(large_file, dest_file)
            self.test_files_created.append(dest_file)
            
            duration = time.time() - start_time
            
            self.log_test(
                "Large file handling",
                "PASS",
                duration,
                f"Processed {file_size // 1024}KB file successfully"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Large file handling", "FAIL", duration, str(e))

    def test_database_performance(self):
        """Test database operations performance"""
        print("\n🗄️ Testing database performance...")
        
        start_time = time.time()
        
        try:
            import sqlite3
            
            # Test database operations
            conn = sqlite3.connect('archives.db')
            cursor = conn.cursor()
            
            # Test multiple queries
            for i in range(100):
                cursor.execute("SELECT COUNT(*) FROM users")
                cursor.fetchone()
            
            # Test insert operations
            for i in range(10):
                cursor.execute("""
                    INSERT INTO operations_log (user_id, operation, details, timestamp)
                    VALUES (1, 'performance_test', ?, ?)
                """, (f"Performance test operation {i}", datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            duration = time.time() - start_time
            
            self.log_test(
                "Database performance",
                "PASS",
                duration,
                "Completed 100 queries and 10 inserts"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Database performance", "FAIL", duration, str(e))

    def test_memory_usage(self):
        """Test memory usage during operations"""
        print("\n💾 Testing memory usage...")
        
        start_time = time.time()
        
        try:
            import psutil
            import os
            
            # Get current process
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Perform memory-intensive operations
            large_data = []
            for i in range(1000):
                large_data.append("x" * 1000)  # Create some data
            
            # Check memory after operations
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            duration = time.time() - start_time
            
            status = "PASS" if memory_increase < 50 else "WARNING"  # 50MB threshold
            
            self.log_test(
                "Memory usage",
                status,
                duration,
                f"Memory increase: {memory_increase:.1f}MB (Initial: {initial_memory:.1f}MB, Final: {final_memory:.1f}MB)"
            )
            
        except ImportError:
            duration = time.time() - start_time
            self.log_test(
                "Memory usage",
                "SKIP",
                duration,
                "psutil not available - install with 'pip install psutil'"
            )
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Memory usage", "FAIL", duration, str(e))

    def run_performance_tests(self):
        """Run all performance tests"""
        print("🚀 Starting Performance Testing")
        print("=" * 60)
        
        start_time = time.time()
        
        # Create test files
        self.create_test_files(10)
        
        # Run performance tests
        self.test_concurrent_file_operations(5)
        self.test_large_file_handling()
        self.test_database_performance()
        self.test_memory_usage()
        
        # Calculate overall results
        total_duration = time.time() - start_time
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warning_tests = len([r for r in self.test_results if r['status'] == 'WARNING'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 PERFORMANCE TEST RESULTS")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️ Warnings: {warning_tests}")
        print(f"⏭️ Skipped: {skipped_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Total Duration: {total_duration:.2f} seconds")
        
        # Performance metrics
        avg_duration = sum(r['duration'] for r in self.test_results) / len(self.test_results)
        print(f"Average Test Duration: {avg_duration:.2f} seconds")
        
        # Save results
        self.save_performance_results()
        
        return success_rate >= 80

    def save_performance_results(self):
        """Save performance test results"""
        try:
            results_file = f"performance_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(results_file, 'w') as f:
                json.dump({
                    'summary': {
                        'total_tests': len(self.test_results),
                        'passed': len([r for r in self.test_results if r['status'] == 'PASS']),
                        'failed': len([r for r in self.test_results if r['status'] == 'FAIL']),
                        'warnings': len([r for r in self.test_results if r['status'] == 'WARNING']),
                        'skipped': len([r for r in self.test_results if r['status'] == 'SKIP']),
                        'success_rate': (len([r for r in self.test_results if r['status'] == 'PASS']) / len(self.test_results)) * 100,
                        'avg_duration': sum(r['duration'] for r in self.test_results) / len(self.test_results)
                    },
                    'detailed_results': self.test_results
                }, f, indent=2)
            
            print(f"\n📄 Performance results saved to: {results_file}")
            
        except Exception as e:
            print(f"❌ Failed to save performance results: {e}")

    def cleanup_test_files(self):
        """Clean up test files"""
        print("\n🧹 Cleaning up test files...")
        
        cleaned_count = 0
        for file_path in self.test_files_created:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    cleaned_count += 1
                elif os.path.isdir(file_path):
                    import shutil
                    shutil.rmtree(file_path)
                    cleaned_count += 1
            except Exception as e:
                print(f"❌ Failed to clean up {file_path}: {e}")
        
        print(f"✅ Cleaned up {cleaned_count} test files and folders")

if __name__ == "__main__":
    tester = PerformanceTest()
    
    try:
        success = tester.run_performance_tests()
        
        if success:
            print("\n🎉 PERFORMANCE TESTING COMPLETED SUCCESSFULLY!")
            print("✅ System performance is excellent")
        else:
            print("\n⚠️ PERFORMANCE TESTING COMPLETED WITH ISSUES")
            print("❌ Please review performance metrics")
            
    except KeyboardInterrupt:
        print("\n⏹️ Performance testing interrupted by user")
    except Exception as e:
        print(f"\n💥 Performance testing failed with error: {e}")
    finally:
        tester.cleanup_test_files()
