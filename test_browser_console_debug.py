#!/usr/bin/env python3
"""
TEST BROWSER CONSOLE DEBUG
Check what the browser console should show
"""

import requests
import json

def test_browser_console_debug():
    print("🔍 BROWSER CONSOLE DEBUG TEST")
    print("="*50)
    
    session = requests.Session()
    
    # Login
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
    
    # Get folders
    folders_response = session.get('http://127.0.0.1:5001/api/get-folders')
    folders_data = folders_response.json()
    folders = folders_data.get('folders', [])
    test_folder = folders[0]['path'] if folders else None
    
    print(f"Test folder: {test_folder}")
    
    # Test assignment with exact browser headers
    assign_data = {
        'folder_paths': [test_folder],
        'category': 'Social media outputs with stems',
        'assign_to': 'Executor Public',
        'video_ids': 'CONSOLE-TEST-001',
        'url': 'https://console-test.com',
        'remarks': 'Console debug test',
        'operation_type': 'copy'
    }
    
    print("\n🔍 Making assignment request...")
    assign_response = session.post(
        'http://127.0.0.1:5001/api/add-to-queue',
        json=assign_data,
        headers={
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    )
    
    print(f"Response status: {assign_response.status_code}")
    
    if assign_response.status_code == 200:
        response_data = assign_response.json()
        
        print("\n🔍 CONSOLE DEBUG SIMULATION:")
        print("="*40)
        print(f"🔍 DEBUG: Assignment response data: {json.dumps(response_data, indent=2)}")
        print(f"🔍 DEBUG: data.success: {response_data.get('success')}")
        print(f"🔍 DEBUG: data.added: {response_data.get('added')}")
        print(f"🔍 DEBUG: data.failed: {response_data.get('failed')}")
        print(f"🔍 DEBUG: data.failed_items: {response_data.get('failed_items')}")
        
        # Simulate the JavaScript logic exactly
        if response_data.get('success'):
            print(f"\n✅ SUCCESS ALERT: Successfully added {response_data.get('added')} folders to queue!")
            
            failed_count = response_data.get('failed', 0)
            print(f"🔍 DEBUG: Checking if {failed_count} > 0...")
            
            if failed_count > 0:
                print(f"🔍 DEBUG: Showing failed warning because data.failed > 0")
                print(f"⚠️  WARNING ALERT: Failed to add {failed_count} folders. Check console for details.")
                print(f"Failed items: {response_data.get('failed_items')}")
            else:
                print(f"🔍 DEBUG: No failed items, not showing warning")
                print("✅ NO WARNING ALERT SHOULD APPEAR")
        else:
            print(f"❌ ERROR ALERT: Error adding to queue: {response_data.get('error')}")
    else:
        print(f"❌ HTTP Error: {assign_response.status_code}")
        print(f"Response: {assign_response.text}")
    
    print("\n" + "="*50)
    print("🎯 EXPECTED BROWSER BEHAVIOR:")
    print("1. Success message should appear")
    print("2. NO warning message should appear (because failed = 0)")
    print("3. If warning appears anyway, it's a frontend caching/logic issue")
    print("="*50)

if __name__ == "__main__":
    test_browser_console_debug()
