#!/usr/bin/env python3
"""
Test script to verify the admin panel is working correctly
"""

import requests
import json
from datetime import datetime

def test_admin_panel():
    """Test the admin panel functionality"""
    base_url = "http://localhost:3000"
    
    print("🧪 Testing Admin Panel Functionality")
    print("=" * 50)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # Test 1: Login as admin
        print("1. Testing admin login...")
        login_data = {
            'username': 'admin',
            'password': 'Shiva@123'
        }
        
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            return False
        
        # Test 2: Access admin panel
        print("2. Testing admin panel access...")
        response = session.get(f"{base_url}/admin")
        if response.status_code == 200:
            print("✅ Admin panel accessible")
            
            # Check if key elements are present
            content = response.text
            if 'Admin Control Center' in content:
                print("✅ Admin panel title found")
            if 'Dashboard' in content and 'Reports' in content:
                print("✅ Navigation tabs found")
            if 'System Health' in content:
                print("✅ System health section found")
            
        else:
            print(f"❌ Admin panel access failed: {response.status_code}")
            return False
        
        # Test 3: Test system health API
        print("3. Testing system health API...")
        response = session.get(f"{base_url}/api/admin/system-health")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ System health API working")
                health_checks = data.get('health', [])
                print(f"   Found {len(health_checks)} health checks")
                for check in health_checks:
                    status_icon = "✅" if check['status'] == 'good' else "⚠️" if check['status'] == 'warning' else "❌"
                    print(f"   {status_icon} {check['component']}: {check['status']}")
            else:
                print(f"❌ System health API error: {data.get('error')}")
        else:
            print(f"❌ System health API failed: {response.status_code}")
        
        # Test 4: Test system paths API
        print("4. Testing system paths API...")
        response = session.get(f"{base_url}/api/admin/system-paths")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ System paths API working")
                paths = data.get('paths', [])
                print(f"   Found {len(paths)} configured paths")
                
                # Count paths by category
                categories = {}
                for path in paths:
                    category = path.get('category', 'unknown')
                    categories[category] = categories.get(category, 0) + 1
                
                for category, count in categories.items():
                    print(f"   - {category}: {count} paths")
            else:
                print(f"❌ System paths API error: {data.get('error')}")
        else:
            print(f"❌ System paths API failed: {response.status_code}")
        
        # Test 5: Test user management API (get users)
        print("5. Testing user management...")
        # First, let's try to get the admin page which should show users
        response = session.get(f"{base_url}/admin")
        if response.status_code == 200 and 'admin' in response.text.lower():
            print("✅ User management interface accessible")
        else:
            print("❌ User management interface not accessible")
        
        # Test 6: Test report generation
        print("6. Testing report generation...")
        report_data = {
            'type': 'activity',
            'filters': {
                'dateFrom': '',
                'dateTo': '',
                'user': '',
                'role': ''
            }
        }
        
        response = session.post(
            f"{base_url}/api/admin/generate-report",
            json=report_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Report generation working")
                report = data.get('report', {})
                print(f"   Report type: {report.get('type')}")
                print(f"   Data entries: {len(report.get('data', []))}")
            else:
                print(f"❌ Report generation error: {data.get('error')}")
        else:
            print(f"❌ Report generation failed: {response.status_code}")
        
        print("\n" + "=" * 50)
        print("🎉 ADMIN PANEL TESTING COMPLETED")
        print("✅ All core admin features are working correctly!")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask app. Make sure it's running on port 3000")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_admin_ui_elements():
    """Test that admin UI elements are properly rendered"""
    print("\n🎨 Testing Admin UI Elements")
    print("=" * 50)
    
    session = requests.Session()
    base_url = "http://localhost:3000"
    
    try:
        # Login first
        login_data = {'username': 'admin', 'password': 'Shiva@123'}
        session.post(f"{base_url}/login", data=login_data)
        
        # Get admin page
        response = session.get(f"{base_url}/admin")
        content = response.text
        
        # Check for key UI elements
        ui_elements = [
            ('Admin Control Center', 'Main title'),
            ('nav-pills', 'Navigation tabs'),
            ('dashboard-pane', 'Dashboard tab'),
            ('reports-pane', 'Reports tab'),
            ('paths-pane', 'Path config tab'),
            ('metadata-pane', 'Metadata tab'),
            ('users-pane', 'Users tab'),
            ('system-pane', 'System tab'),
            ('stat-card', 'Statistics cards'),
            ('System Health Overview', 'Health monitoring'),
            ('Quick Actions', 'Quick actions section'),
            ('chart-container', 'Chart containers'),
            ('createUserModal', 'Create user modal'),
            ('backupModal', 'Backup modal')
        ]
        
        found_elements = 0
        for element, description in ui_elements:
            if element in content:
                print(f"✅ {description}: Found")
                found_elements += 1
            else:
                print(f"❌ {description}: Missing")
        
        success_rate = (found_elements / len(ui_elements)) * 100
        print(f"\n📊 UI Elements Success Rate: {success_rate:.1f}% ({found_elements}/{len(ui_elements)})")
        
        if success_rate >= 90:
            print("🎉 Excellent! Admin UI is comprehensive and complete")
        elif success_rate >= 75:
            print("👍 Good! Most admin UI elements are present")
        else:
            print("⚠️ Some admin UI elements may be missing")
        
        return success_rate >= 75
        
    except Exception as e:
        print(f"❌ UI test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Admin Panel Testing")
    print("Make sure Flask app is running on port 3000")
    print()
    
    # Test functionality
    func_success = test_admin_panel()
    
    # Test UI elements
    ui_success = test_admin_ui_elements()
    
    print("\n" + "=" * 60)
    print("📊 FINAL TEST RESULTS")
    print("=" * 60)
    print(f"✅ Functionality Tests: {'PASS' if func_success else 'FAIL'}")
    print(f"✅ UI Elements Tests: {'PASS' if ui_success else 'FAIL'}")
    
    if func_success and ui_success:
        print("\n🎉 ADMIN PANEL IS FULLY FUNCTIONAL!")
        print("✅ Ready for production use")
    else:
        print("\n⚠️ Some issues detected")
        print("❌ Please review the test results above")
