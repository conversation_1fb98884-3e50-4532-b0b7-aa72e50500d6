#!/usr/bin/env python3
import sqlite3

def check_users_and_queue():
    print("🔍 CHECKING USERS AND QUEUE STATUS")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('archives_management.db')
        cursor = conn.cursor()
        
        # Check users
        print("👥 ACTIVE USERS:")
        cursor.execute('SELECT username, role FROM users WHERE is_active = 1')
        users = cursor.fetchall()
        for username, role in users:
            print(f"   {username} → {role}")
        
        # Check queue items
        print(f"\n📋 QUEUE ITEMS:")
        cursor.execute('SELECT COUNT(*) FROM queue_items')
        total = cursor.fetchone()[0]
        print(f"   Total: {total}")
        
        if total > 0:
            cursor.execute('''
                SELECT folder_name, assign_to, status, created_at 
                FROM queue_items 
                ORDER BY created_at DESC 
                LIMIT 5
            ''')
            items = cursor.fetchall()
            print("   Recent items:")
            for item in items:
                print(f"     - {item[0]}")
                print(f"       Assigned to: {item[1]}")
                print(f"       Status: {item[2]}")
                print(f"       Created: {item[3]}")
                print()
        
        # Check Executive Public specific query
        print("🎬 EXECUTIVE PUBLIC QUEUE QUERY:")
        cursor.execute('''
            SELECT folder_name, category, status
            FROM queue_items
            WHERE assign_to = 'Executor Public'
            AND status = 'completed'
            AND category != 'Private one video'
            ORDER BY processed_at DESC
        ''')
        exec_public_items = cursor.fetchall()
        print(f"   Found {len(exec_public_items)} items for Executor Public")
        for item in exec_public_items:
            print(f"     - {item[0]} ({item[1]}) - {item[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_users_and_queue()
