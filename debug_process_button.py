#!/usr/bin/env python3
"""
DEBUG PROCESS BUTTON ISSUE
Simulate exact user flow to identify why metadata form doesn't appear
"""

import requests
import json
import time

def debug_process_button_flow():
    print("🔍 DEBUGGING PROCESS BUTTON ISSUE")
    print("=" * 80)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # STEP 1: Login (like real user)
        print("1. 🔐 Login to Executor Public...")
        login_data = {
            'username': 'executor_public',
            'password': 'Shiva@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return False
        
        print("   ✅ Login successful!")
        
        # STEP 2: Load Executor Public page (like opening the page)
        print("\n2. 🌐 Load Executor Public page...")
        page_response = session.get(f"{base_url}/executor-public")
        if page_response.status_code != 200:
            print("   ❌ Page load failed!")
            return False
        
        page_content = page_response.text
        print("   ✅ Page loaded!")
        
        # Check if Process button exists in HTML
        process_button_exists = 'processFileWithMetadata' in page_content
        print(f"   🔧 Process button function exists: {'✅ YES' if process_button_exists else '❌ NO'}")
        
        # Check if metadata form container exists
        metadata_form_exists = 'metadataForm' in page_content
        print(f"   🔧 Metadata form container exists: {'✅ YES' if metadata_form_exists else '❌ NO'}")
        
        # Check if modal exists
        modal_exists = 'metadataModal' in page_content
        print(f"   🔧 Metadata modal exists: {'✅ YES' if modal_exists else '❌ NO'}")
        
        # STEP 3: Load files queue (like page loading files)
        print("\n3. 📋 Load files queue...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        if queue_response.status_code != 200:
            print("   ❌ Queue load failed!")
            return False
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue error: {queue_data.get('error')}")
            return False
        
        files = queue_data.get('files', [])
        print(f"   ✅ Queue loaded: {len(files)} files")
        
        if not files:
            print("   ⚠️ No files to test!")
            return False
        
        # STEP 4: Select first file (like user clicking on a file)
        print("\n4. 📁 Select file...")
        test_file = files[0]
        queue_item_id = test_file.get('queue_item_id')
        folder_name = test_file.get('folder_name', 'Unknown')
        folder_path = test_file.get('folder_path', '')
        
        print(f"   📂 Selected: {folder_name}")
        print(f"   🆔 Queue ID: {queue_item_id}")
        print(f"   📍 Path: {folder_path[:80]}...")
        
        # STEP 5: Test file details API (what happens when Process button is clicked)
        print("\n5. 🔍 Test file details API (Process button action)...")
        details_response = session.get(f"{base_url}/api/file-details/{queue_item_id}")
        print(f"   📥 File Details API Status: {details_response.status_code}")
        
        if details_response.status_code != 200:
            print("   ❌ File details API failed!")
            print(f"      This is likely why the metadata form doesn't appear!")
            return False
        
        details_data = details_response.json()
        if not details_data.get('success'):
            print(f"   ❌ File details error: {details_data.get('error')}")
            print(f"      This is likely why the metadata form doesn't appear!")
            return False
        
        file_details = details_data.get('file_details', {})
        print("   ✅ File details loaded successfully!")
        print("   📊 Auto-Detection Results:")
        print(f"      📁 Folder: {file_details.get('folder_name', 'Unknown')}")
        print(f"      📊 File Count: {file_details.get('file_count', 0)}")
        print(f"      💾 Total Size: {file_details.get('total_size_formatted', 'Unknown')}")
        print(f"      ⏱️ Duration: {file_details.get('detected_duration', 'Unknown')}")
        print(f"      🆔 Video IDs: {file_details.get('video_ids', 'Not specified')}")
        print(f"      💬 Remarks: {file_details.get('remarks', 'No remarks')}")
        
        # STEP 6: Test metadata options API (form dropdown data)
        print("\n6. 📝 Test metadata options API...")
        options_response = session.get(f"{base_url}/api/executive-public/metadata-options")
        print(f"   📥 Metadata Options API Status: {options_response.status_code}")
        
        if options_response.status_code != 200:
            print("   ❌ Metadata options API failed!")
            print(f"      This could prevent form dropdowns from loading!")
            return False
        
        options_data = options_response.json()
        if not options_data.get('success'):
            print(f"   ❌ Metadata options error: {options_data.get('error')}")
            print(f"      This could prevent form dropdowns from loading!")
            return False
        
        options = options_data.get('options', {})
        print("   ✅ Metadata options loaded successfully!")
        print(f"      🎬 Video Types: {len(options.get('video_types', []))}")
        print(f"      🌍 Languages: {len(options.get('languages', []))}")
        print(f"      🏢 Departments: {len(options.get('departments', []))}")
        print(f"      🏷️ Content Tags: {len(options.get('content_tags', []))}")
        
        # STEP 7: Check JavaScript functionality (simulate what happens in browser)
        print("\n7. 🔧 Check JavaScript functionality...")
        
        # Check if the page has the necessary JavaScript functions
        js_functions = {
            'processFileWithMetadata': 'processFileWithMetadata' in page_content,
            'showMetadataForm': 'showMetadataForm' in page_content,
            'loadFileDetails': 'loadFileDetails' in page_content,
            'populateMetadataOptions': 'populateMetadataOptions' in page_content,
            'bootstrap_modal': 'modal' in page_content.lower()
        }
        
        print("   🔧 JavaScript Functions Check:")
        for func, exists in js_functions.items():
            status = "✅" if exists else "❌"
            print(f"      {status} {func}: {exists}")
        
        # STEP 8: Analyze potential issues
        print("\n8. 🔍 Analyze potential issues...")
        
        issues_found = []
        
        if not process_button_exists:
            issues_found.append("❌ Process button function missing")
        
        if not metadata_form_exists:
            issues_found.append("❌ Metadata form container missing")
        
        if not modal_exists:
            issues_found.append("❌ Metadata modal missing")
        
        if not js_functions.get('showMetadataForm', False):
            issues_found.append("❌ showMetadataForm function missing")
        
        if not js_functions.get('bootstrap_modal', False):
            issues_found.append("❌ Bootstrap modal functionality missing")
        
        if issues_found:
            print("   🚨 ISSUES FOUND:")
            for issue in issues_found:
                print(f"      {issue}")
            return False
        else:
            print("   ✅ No obvious issues found in API responses")
            print("   🔍 Issue might be in JavaScript event handling or modal display")
            return True
        
    except Exception as e:
        print(f"\n❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚨 DEBUGGING PROCESS BUTTON - METADATA FORM NOT APPEARING")
    print("=" * 80)
    
    success = debug_process_button_flow()
    
    print("\n" + "=" * 80)
    print("🎯 DEBUG RESULTS")
    print("=" * 80)
    
    if success:
        print("✅ API ENDPOINTS WORKING CORRECTLY")
        print("   The issue is likely in the frontend JavaScript")
        print("   Need to check:")
        print("   1. Button click event handler")
        print("   2. Modal display logic")
        print("   3. Form rendering code")
        print("   4. Bootstrap modal initialization")
    else:
        print("❌ ISSUES FOUND IN BACKEND/API")
        print("   Check the specific errors above")
        print("   Fix backend issues first, then test frontend")
    
    return success

if __name__ == "__main__":
    main()
