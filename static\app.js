/**
 * Archives Department File Management System - Main JavaScript
 */

// Global variables
let currentUser = null;
let systemConfig = {};

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    // Load user session
    loadUserSession();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize alerts
    initializeAlerts();
    
    // Load system configuration
    loadSystemConfig();
    
    console.log('Archives System initialized successfully');
}

// Load user session
function loadUserSession() {
    // This would typically load from server session
    const userElement = document.querySelector('[data-user]');
    if (userElement) {
        currentUser = {
            username: userElement.dataset.username,
            role: userElement.dataset.role,
            id: userElement.dataset.userId
        };
    }
}

// Initialize Bootstrap tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialize alert system
function initializeAlerts() {
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    });
}

// Load system configuration
async function loadSystemConfig() {
    try {
        const response = await fetch('/api/system-config');
        if (response.ok) {
            systemConfig = await response.json();
        }
    } catch (error) {
        console.warn('Could not load system configuration:', error);
    }
}

// Utility Functions

/**
 * Show alert message
 * @param {string} message - Alert message
 * @param {string} type - Alert type (success, danger, warning, info)
 * @param {number} duration - Duration in milliseconds (default: 5000)
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alertContainer') || document.body;
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
    }
    
    return alertDiv;
}

/**
 * Get icon for alert type
 * @param {string} type - Alert type
 * @returns {string} Icon class
 */
function getAlertIcon(type) {
    const icons = {
        success: 'check-circle',
        danger: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Format file size
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format date
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date
 */
function formatDate(date) {
    if (!date) return 'N/A';
    const d = new Date(date);
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
}

/**
 * Format duration
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration
 */
function formatDuration(seconds) {
    if (!seconds || seconds < 0) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * Debounce function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function
 * @param {Function} func - Function to throttle
 * @param {number} limit - Limit in milliseconds
 * @returns {Function} Throttled function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Sleep function
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after ms milliseconds
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Copy text to clipboard
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} Success status
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showAlert('Copied to clipboard', 'success', 2000);
        return true;
    } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        showAlert('Failed to copy to clipboard', 'danger', 3000);
        return false;
    }
}

/**
 * Download file
 * @param {string} url - File URL
 * @param {string} filename - Filename
 */
function downloadFile(url, filename) {
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

/**
 * Create loading spinner
 * @param {string} size - Spinner size (sm, md, lg)
 * @returns {HTMLElement} Spinner element
 */
function createSpinner(size = 'md') {
    const spinner = document.createElement('div');
    spinner.className = `spinner-border text-primary spinner-border-${size}`;
    spinner.setAttribute('role', 'status');
    spinner.innerHTML = '<span class="visually-hidden">Loading...</span>';
    return spinner;
}

/**
 * Show loading state
 * @param {HTMLElement} element - Element to show loading in
 * @param {string} message - Loading message
 */
function showLoading(element, message = 'Loading...') {
    const originalContent = element.innerHTML;
    element.dataset.originalContent = originalContent;
    
    element.innerHTML = `
        <div class="text-center py-3">
            ${createSpinner().outerHTML}
            <p class="mt-2 text-muted">${message}</p>
        </div>
    `;
}

/**
 * Hide loading state
 * @param {HTMLElement} element - Element to hide loading from
 */
function hideLoading(element) {
    if (element.dataset.originalContent) {
        element.innerHTML = element.dataset.originalContent;
        delete element.dataset.originalContent;
    }
}

/**
 * Validate form
 * @param {HTMLFormElement} form - Form to validate
 * @returns {boolean} Validation result
 */
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

/**
 * Clear form validation
 * @param {HTMLFormElement} form - Form to clear validation from
 */
function clearFormValidation(form) {
    const fields = form.querySelectorAll('.is-invalid, .is-valid');
    fields.forEach(field => {
        field.classList.remove('is-invalid', 'is-valid');
    });
}

/**
 * Animate element
 * @param {HTMLElement} element - Element to animate
 * @param {string} animation - Animation class
 * @param {number} duration - Animation duration in milliseconds
 */
function animateElement(element, animation, duration = 500) {
    element.classList.add(animation);
    setTimeout(() => {
        element.classList.remove(animation);
    }, duration);
}

/**
 * Check if user has permission
 * @param {string} permission - Permission to check
 * @returns {boolean} Permission status
 */
function hasPermission(permission) {
    if (!currentUser) return false;
    
    const rolePermissions = {
        'Main Admin': ['all'],
        'Assigner': ['assign', 'view'],
        'Executor Public': ['process', 'view'],
        'Executor Private': ['process', 'view'],
        'Cross Checker': ['validate', 'view']
    };
    
    const userPermissions = rolePermissions[currentUser.role] || [];
    return userPermissions.includes('all') || userPermissions.includes(permission);
}

/**
 * Log user action
 * @param {string} action - Action performed
 * @param {Object} details - Action details
 */
async function logUserAction(action, details = {}) {
    try {
        await fetch('/api/log-action', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: action,
                details: details,
                timestamp: new Date().toISOString()
            })
        });
    } catch (error) {
        console.warn('Failed to log user action:', error);
    }
}

// Export functions for use in other scripts
window.ArchivesSystem = {
    showAlert,
    formatFileSize,
    formatDate,
    formatDuration,
    debounce,
    throttle,
    sleep,
    copyToClipboard,
    downloadFile,
    showLoading,
    hideLoading,
    validateForm,
    clearFormValidation,
    animateElement,
    hasPermission,
    logUserAction
};
