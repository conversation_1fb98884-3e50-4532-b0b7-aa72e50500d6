<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>🔒 Executive Private - Archives Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a1a2e;
            --secondary-color: #16213e;
            --accent-color: #e94560;
            --success-color: #27ae60;
            --info-color: #3498db;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --private-color: #8e44ad;
        }

        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            color: #ecf0f1;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 2px solid var(--private-color);
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color), var(--private-color));
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px solid var(--accent-color);
        }

        .header-section h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .header-section p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .tab-content {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            color: #2c3e50;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            background: #34495e;
            color: white;
            font-weight: 600;
            margin-right: 5px;
            padding: 15px 25px;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link.active {
            background: var(--private-color);
            color: white;
            transform: translateY(-2px);
        }

        .nav-tabs .nav-link:hover {
            background: var(--accent-color);
            color: white;
        }

        .queue-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid var(--private-color);
        }

        .queue-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 15px;
            border: none;
        }

        .queue-table td {
            padding: 12px 15px;
            vertical-align: middle;
            border-bottom: 1px solid #eee;
        }

        .queue-table tbody tr:hover {
            background: #f8f9fa;
            transform: translateY(-1px);
            transition: all 0.3s ease;
        }

        .btn-preview {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-preview:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn-process {
            background: var(--private-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-process:hover {
            background: #7d3c98;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .search-filter-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid var(--private-color);
        }

        .notification-area {
            background: var(--private-color);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 2px solid var(--accent-color);
        }

        .metadata-form {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
            border: 2px solid var(--private-color);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--private-color);
            box-shadow: 0 0 0 0.2rem rgba(142, 68, 173, 0.25);
        }

        .processing-logs {
            background: var(--primary-color);
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            border: 2px solid var(--private-color);
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress {
            height: 25px;
            border-radius: 12px;
            background: #e9ecef;
        }

        .progress-bar {
            border-radius: 12px;
            background: linear-gradient(45deg, var(--private-color), #9b59b6);
        }

        .alert-custom {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .file-preview-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid var(--private-color);
        }

        .social-media-section {
            background: #e8f4fd;
            border: 2px solid var(--private-color);
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
        }

        .batch-processing-section {
            background: #fff3cd;
            border: 2px solid var(--warning-color);
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--private-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tooltip-icon {
            color: var(--private-color);
            margin-left: 5px;
            cursor: help;
        }

        .required-field {
            color: var(--danger-color);
        }

        .btn-save-draft {
            background: var(--warning-color);
            color: #333;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .btn-copy-metadata {
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
        }

        .private-badge {
            background: var(--private-color);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .executive-private-header {
            background: linear-gradient(45deg, var(--primary-color), var(--private-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }

        /* Folder Tree Styles (matching Archives Assignment Console) */
        .tree-node {
            margin: 2px 0;
            user-select: none;
        }

        .file-node {
            background: rgba(142,68,173,0.05);
            border-left: 3px solid var(--private-color);
            border-radius: 6px;
            margin: 1px 0;
            transition: all 0.2s ease;
        }

        .file-node:hover {
            background: rgba(142,68,173,0.15);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(142,68,173,0.2);
        }

        .folder-node:hover .node-content {
            background: rgba(255,193,7,0.1);
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .node-content:hover {
            background: rgba(142,68,173,0.1);
        }

        .folder-node .node-content {
            font-weight: 500;
        }

        .folder-node.selected .node-content {
            background: linear-gradient(135deg, var(--private-color), #9b59b6);
            color: white;
        }

        .expand-icon {
            cursor: pointer;
            margin-right: 8px;
            transition: transform 0.2s ease;
            width: 16px;
            text-align: center;
        }

        .folder-children {
            border-left: 2px solid rgba(142,68,173,0.2);
            margin-left: 15px;
            padding-left: 5px;
        }

        .tree-node .btn {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .tree-node:hover .btn {
            opacity: 1;
        }

        .private-folder-tree {
            background: white;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid var(--private-color);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-user-lock me-3"></i>Executive Private Dashboard</h1>
            <p>Advanced File Processing & Metadata Management System</p>
            <span class="private-badge"><i class="fas fa-shield-alt me-1"></i>PRIVATE ACCESS</span>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="queue-tab" data-bs-toggle="tab" data-bs-target="#queue-pane" type="button" role="tab">
                    <i class="fas fa-list me-2"></i>Files in Queue to be Processed
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="folder-tab" data-bs-toggle="tab" data-bs-target="#folder-pane" type="button" role="tab">
                    <i class="fas fa-folder-tree me-2"></i>Folder Navigation
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="processing-tab" data-bs-toggle="tab" data-bs-target="#processing-pane" type="button" role="tab">
                    <i class="fas fa-cogs me-2"></i>File Processing
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Queue Tab -->
            <div class="tab-pane fade show active" id="queue-pane" role="tabpanel">
                <!-- Notification Area -->
                <div class="notification-area" id="notificationArea">
                    <i class="fas fa-shield-alt me-2"></i>
                    <span id="notificationText">Welcome to Executive Private! Loading your secure file queue...</span>
                </div>

                <!-- Search and Filter Section -->
                <div class="search-filter-section">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Search Files</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="Search by file name or folder...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Sort By</label>
                            <select class="form-select" id="sortBy">
                                <option value="name">File Name</option>
                                <option value="size">File Size</option>
                                <option value="date">Date</option>
                                <option value="folder">Folder</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Filter Size</label>
                            <select class="form-select" id="filterSize">
                                <option value="">All Sizes</option>
                                <option value="small">< 1GB</option>
                                <option value="medium">1-5GB</option>
                                <option value="large">> 5GB</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Filter Date</label>
                            <input type="date" class="form-control" id="filterDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100" onclick="applyFilters()">
                                <i class="fas fa-filter me-2"></i>Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Queue Table -->
                <div class="table-responsive">
                    <table class="table queue-table" id="queueTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll"></th>
                                <th>File Name</th>
                                <th>File Size</th>
                                <th>Number of Files</th>
                                <th>Assigned Date</th>
                                <th>Folder Path</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="queueTableBody">
                            <!-- Dynamic content will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Batch Actions -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <button class="btn btn-copy-metadata" onclick="showBatchProcessing()">
                            <i class="fas fa-copy me-2"></i>Batch Process Selected
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <span id="selectedCount">0 files selected</span>
                    </div>
                </div>
            </div>

            <!-- Folder Navigation Tab (EXACT MATCH - Archives Assignment Console) -->
            <div class="tab-pane fade" id="folder-pane" role="tabpanel">
                <!-- Folder Browser Section (EXACT COPY from Archives Console) -->
                <div class="folder-browser">
                    <h3 class="mb-4">
                        <i class="fas fa-folder-tree me-2"></i>Executive Private Folder Browser
                        <span class="private-badge ms-2">PRIVATE ACCESS</span>
                        <button class="btn btn-outline-primary btn-sm float-end" onclick="refreshFolders()">
                            <i class="fas fa-refresh me-1"></i>Refresh
                        </button>
                    </h3>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="folder-tree" id="folderTree">
                                <div class="text-center py-4">
                                    <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                                    <p class="mt-2">Loading private folders...</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="selected-folders-panel">
                                <h5><i class="fas fa-check-square me-2"></i>Selected Folders</h5>
                                <div id="selectedFoldersList" class="selected-folders-list">
                                    <p class="text-muted">No folders selected</p>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="selectAllFolders()">
                                        <i class="fas fa-check-double me-1"></i>Select All
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                        <i class="fas fa-times me-1"></i>Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Tab -->
            <div class="tab-pane fade" id="processing-pane" role="tabpanel">
                <!-- File Preview Section -->
                <div class="file-preview-section" id="filePreviewSection" style="display: none;">
                    <h4><i class="fas fa-play-circle me-2"></i>File Preview <span class="private-badge">PRIVATE</span></h4>
                    <div class="row">
                        <div class="col-md-8">
                            <div id="vlcPreview" class="text-center p-4 bg-light rounded">
                                <i class="fas fa-video fa-3x text-muted mb-3"></i>
                                <p>Select a file to preview</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>File Metadata</h6>
                            <div id="fileMetadata">
                                <p><strong>Duration:</strong> <span id="fileDuration">--</span></p>
                                <p><strong>Resolution:</strong> <span id="fileResolution">--</span></p>
                                <p><strong>Format:</strong> <span id="fileFormat">--</span></p>
                                <p><strong>Size:</strong> <span id="fileSize">--</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Metadata Entry Form -->
                <div class="metadata-form" id="metadataForm">
                    <h4><i class="fas fa-edit me-2"></i>Executive Private Metadata Entry Form</h4>
                    <p class="text-muted">Complete all required fields marked with <span class="required-field">*</span></p>
                    <div class="alert alert-info">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>Executive Private Access:</strong> You have access to all file categories including private and restricted content.
                    </div>

                    <form id="metadataEntryForm">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        OCD/VP Number <span class="required-field">*</span>
                                        <i class="fas fa-info-circle tooltip-icon" title="Unique identifier for internal tracking"></i>
                                    </label>
                                    <input type="text" class="form-control" id="ocdVpNumber" placeholder="OCD-2025-001" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        Edited File Name <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="editedFileName" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Language <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="language" required>
                                        <option value="">Select Language</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Edited Year <span class="required-field">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="editedYear" min="2000" max="2030" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Trim Closed Date</label>
                                    <input type="date" class="form-control" id="trimClosedDate">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Total Duration (HH:MM:SS) <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="totalDuration" placeholder="00:05:30" pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Type of Video <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="videoType" required>
                                        <option value="">Select Video Type</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Edited in Ashram/US <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="editedLocation" required>
                                        <option value="">Select Location</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Metadata Fields -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Published Platforms <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="publishedPlatforms" required>
                                        <option value="">Select Platform</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Department Name <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="departmentName" required>
                                        <option value="">Select Department</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Component <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="component" required>
                                        <option value="">Select Component</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Content Tag <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="contentTag" required>
                                        <option value="">Select Content Tag</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Backup Type <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="backupType" required>
                                        <option value="">Select Backup Type</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Access Level <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="accessLevel" required>
                                        <option value="">Select Access Level</option>
                                        <option value="Public">Public</option>
                                        <option value="Private">Private</option>
                                        <option value="Restricted">Restricted</option>
                                        <option value="Internal">Internal</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Software Show / Renamed Folder Name</label>
                                    <input type="text" class="form-control" id="renamedFolderName" placeholder="New folder name">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Number of Files</label>
                                    <input type="number" class="form-control" id="numberOfFiles" min="1">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Size of Folder (GB/MB)</label>
                                    <input type="text" class="form-control" id="folderSize" placeholder="e.g., 2.5 GB">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">AUDIO/Transcript Code</label>
                                    <input type="text" class="form-control" id="audioTranscriptCode" placeholder="AUD-2025-001">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Audio File Name</label>
                                    <input type="text" class="form-control" id="audioFileName">
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Audio File Selection & Extraction for Private -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Private Audio File Selection & Extraction</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="mb-3">
                                                    <label for="selectedPrivateAudioFile" class="form-label">Select Private Audio File from Current Folder</label>
                                                    <select class="form-select" id="selectedPrivateAudioFile" name="selected_private_audio_file">
                                                        <option value="">Select a private audio file...</option>
                                                    </select>
                                                    <small class="text-muted">Private audio files will be loaded when you process a folder</small>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="loadPrivateAudioFiles()">
                                                            <i class="fas fa-refresh me-1"></i>Refresh Audio Files
                                                        </button>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="previewSelectedPrivateAudio()" disabled id="previewPrivateAudioBtn">
                                                            <i class="fas fa-play me-1"></i>Preview Audio
                                                        </button>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <button type="button" class="btn btn-danger btn-sm w-100" onclick="extractPrivateAudioFile()" disabled id="extractPrivateAudioBtn">
                                                            <i class="fas fa-file-export me-1"></i>Extract Private Audio
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="private-audio-status">
                                                    <h6><i class="fas fa-info-circle me-2"></i>Extraction Status</h6>
                                                    <div id="privateAudioExtractionStatus">
                                                        <p class="text-muted">No extraction in progress</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Transcription File Name</label>
                                    <input type="text" class="form-control" id="transcriptionFileName">
                                </div>
                            </div>
                        </div>

                        <!-- Audio Extraction Section -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-volume-up me-2"></i>Private Audio Extraction</h6>
                                        <span class="private-badge">PRIVATE ACCESS</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label">Select Audio File from Folder</label>
                                                    <select class="form-select" id="audioFileSelect">
                                                        <option value="">Select audio file...</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label">Extraction Progress</label>
                                                    <div class="progress" id="audioExtractionProgress" style="display: none;">
                                                        <div class="progress-bar bg-info" role="progressbar" style="width: 0%" id="audioProgressBar">
                                                            <span id="audioProgressText">0%</span>
                                                        </div>
                                                    </div>
                                                    <button type="button" class="btn btn-info" id="extractAudioBtn" onclick="extractAudio()">
                                                        <i class="fas fa-download me-2"></i>Extract Audio (.wav)
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Private audio will be extracted in .wav format and saved to: T:\To_Process\Rough folder\Folder to be cross checked
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Social Media Details Section -->
                        <div class="social-media-section">
                            <h5><i class="fas fa-share-alt me-2"></i>Social Media Details <span class="private-badge">PRIVATE ACCESS</span></h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Published Date</label>
                                        <input type="date" class="form-control" id="socialPublishedDate">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Video ID</label>
                                        <input type="text" class="form-control" id="socialVideoId" placeholder="VID-2025-001">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Social Media Duration</label>
                                        <select class="form-select" id="socialDuration">
                                            <option value="">Select Duration</option>
                                            <option value="<1min">&lt;1min</option>
                                            <option value="1-3min">1-3min</option>
                                            <option value="3-5min">3-5min</option>
                                            <option value=">5min">&gt;5min</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Social Media Title</label>
                                        <input type="text" class="form-control" id="socialTitle" placeholder="Enter social media title">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Social Media URL</label>
                                        <input type="url" class="form-control" id="socialUrl" placeholder="https://...">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Social Media Description</label>
                                        <textarea class="form-control" id="socialDescription" rows="3" placeholder="Enter description..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Batch Processing Section -->
                        <div class="batch-processing-section" id="batchSection" style="display: none;">
                            <h5><i class="fas fa-layer-group me-2"></i>Executive Private Batch Processing</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-copy-metadata" onclick="copyMetadataToSelected()">
                                        <i class="fas fa-copy me-2"></i>Copy Metadata to Selected Files
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-warning" onclick="processBatch()">
                                        <i class="fas fa-layer-group me-2"></i>Process Batch
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-save-draft me-2" onclick="saveDraft()">
                                    <i class="fas fa-save me-2"></i>Save Draft
                                </button>
                                <button type="button" class="btn btn-process" onclick="processMetadata()">
                                    <i class="fas fa-check me-2"></i>Process & Submit
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="clearForm()">
                                    <i class="fas fa-times me-2"></i>Clear Form
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Processing Logs -->
                <div class="processing-logs" id="processingLogs" style="display: none;">
                    <h6><i class="fas fa-terminal me-2"></i>Executive Private Processing Logs</h6>
                    <div id="logContent">
                        Ready to process private files...
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-container" id="progressContainer" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript will be added in the next part -->
    <script>
        // Initialize the Executive Private dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔒 Executive Private Dashboard Loaded');
            loadQueue();
            loadMetadataOptions();

            // Add event listener for folder tab
            document.getElementById('folder-tab').addEventListener('click', function() {
                loadExecutivePrivateFolderTree();
            });
        });

        // Load files in queue for Executive Private
        async function loadQueue() {
            try {
                const response = await fetch('/api/executive-private/queue');
                const data = await response.json();
                
                if (data.success) {
                    displayQueueFiles(data.files);
                    updateNotification(`${data.total_count} files in private queue for processing`);
                } else {
                    updateNotification('Error loading private queue: ' + data.error, 'error');
                }
            } catch (error) {
                updateNotification('Error loading private queue: ' + error.message, 'error');
            }
        }

        // Display queue files in table
        function displayQueueFiles(files) {
            const tbody = document.getElementById('queueTableBody');
            tbody.innerHTML = '';

            files.forEach((file, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="file-checkbox" value="${file.folder_path}"></td>
                    <td><span class="private-badge me-2">PRIVATE</span>${file.folder_name}</td>
                    <td>${file.total_size}</td>
                    <td>${file.file_count}</td>
                    <td>${file.assigned_date}</td>
                    <td title="${file.folder_path}">${file.folder_path.substring(0, 50)}...</td>
                    <td>
                        <button class="btn btn-preview btn-sm me-2" onclick="previewFile('${file.folder_path}')">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                        <button class="btn btn-process btn-sm" onclick="processFile('${file.folder_path}')">
                            <i class="fas fa-cogs"></i> Process
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update checkbox listeners
            updateCheckboxListeners();
        }

        // Update notification area
        function updateNotification(message, type = 'info') {
            const notificationArea = document.getElementById('notificationArea');
            const notificationText = document.getElementById('notificationText');
            
            notificationText.textContent = message;
            
            // Update styling based on type
            notificationArea.className = 'notification-area';
            if (type === 'error') {
                notificationArea.style.background = 'var(--danger-color)';
            } else if (type === 'success') {
                notificationArea.style.background = 'var(--success-color)';
            } else {
                notificationArea.style.background = 'var(--private-color)';
            }
        }

        // Load metadata options for Executive Private
        async function loadMetadataOptions() {
            try {
                const response = await fetch('/api/executive-private/metadata-options');
                const data = await response.json();
                
                if (data.success) {
                    populateDropdowns(data.options);
                }
            } catch (error) {
                console.error('Error loading Executive Private metadata options:', error);
            }
        }

        // Populate dropdown options
        function populateDropdowns(options) {
            // Populate all dropdowns with their respective options
            populateSelect('language', options.languages);
            populateSelect('videoType', options.video_types);
            populateSelect('editedLocation', options.edited_locations);
            populateSelect('publishedPlatforms', options.published_platforms);
            populateSelect('departmentName', options.departments);
            populateSelect('component', options.components);
            populateSelect('contentTag', options.content_tags);
            populateSelect('backupType', options.backup_types);
        }

        function populateSelect(selectId, options) {
            const select = document.getElementById(selectId);
            if (select) {
                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = option;
                    select.appendChild(optionElement);
                });
            }
        }

        // Preview file function
        function previewFile(folderPath) {
            // Switch to processing tab
            document.getElementById('processing-tab').click();

            // Show file preview section
            document.getElementById('filePreviewSection').style.display = 'block';

            // Load file for preview (VLC integration)
            loadFilePreview(folderPath);
        }

        // Process file function
        function processFile(folderPath) {
            // Switch to processing tab and load metadata form
            document.getElementById('processing-tab').click();

            // Pre-populate form with file information
            populateMetadataForm(folderPath);
        }

        // Load file preview with VLC integration
        async function loadFilePreview(folderPath) {
            try {
                console.log('Previewing private files in folder:', folderPath);

                if (!folderPath || folderPath === '--' || folderPath === 'Unknown') {
                    updateNotification('No folder path available for preview', 'warning');
                    return;
                }

                // Show loading message
                updateNotification('Opening private video files in VLC Media Player...', 'info');

                // Find first video file in folder and open with VLC
                const response = await fetch('/api/preview-video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        folder_path: folderPath
                    })
                });

                const result = await response.json();
                console.log('Preview response:', result);

                if (result.success) {
                    const fileCount = result.video_files ? result.video_files.length : 0;
                    const fileList = result.video_files ? result.video_files.join(', ') : '';

                    updateNotification(
                        `✅ Opened ${fileCount} private video file(s) in VLC: ${fileList}`,
                        'success'
                    );

                    // Update file metadata display
                    document.getElementById('fileDuration').textContent = 'Auto-detecting...';
                    document.getElementById('fileResolution').textContent = 'Auto-detecting...';
                    document.getElementById('fileFormat').textContent = 'Video';
                    document.getElementById('fileSize').textContent = 'Calculating...';
                } else {
                    console.error('Preview error:', result.error);

                    if (result.error.includes('VLC Media Player not found')) {
                        updateNotification(
                            '❌ VLC Media Player not found. Please install VLC Media Player to preview videos. Download from: https://www.videolan.org/vlc/',
                            'error'
                        );
                    } else if (result.error.includes('No video files found')) {
                        const message = result.video_files && result.video_files.length > 0
                            ? `No video files found in folder. Available files: ${result.video_files.join(', ')}`
                            : 'No video files found in the selected folder.';
                        updateNotification(`⚠️ ${message}`, 'warning');
                    } else {
                        updateNotification(`❌ Preview failed: ${result.error}`, 'error');
                    }
                }
            } catch (error) {
                console.error('Preview error:', error);
                updateNotification('Error previewing private file: ' + error.message, 'error');
            }
        }

        // Populate metadata form with file information
        function populateMetadataForm(folderPath) {
            // Auto-populate some fields based on file path
            const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();

            // Set current year as default
            document.getElementById('editedYear').value = new Date().getFullYear();

            // Generate OCD/VP number with PRIVATE prefix
            const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
            document.getElementById('ocdVpNumber').value = `OCD-PRIV-${timestamp}-001`;

            // Set edited file name based on folder name
            document.getElementById('editedFileName').value = folderName;

            // Store current folder path for processing
            document.getElementById('metadataForm').setAttribute('data-folder-path', folderPath);
        }

        // Save draft functionality
        function saveDraft() {
            const formData = collectFormData();

            // Save to localStorage for Executive Private
            localStorage.setItem('executive_private_metadata_draft', JSON.stringify(formData));

            updateNotification('Executive Private draft saved successfully', 'success');
        }

        // Process metadata and submit for Executive Private
        async function processMetadata() {
            try {
                const folderPath = document.getElementById('metadataForm').getAttribute('data-folder-path');
                const metadata = collectFormData();

                // Validate required fields
                if (!validateForm(metadata)) {
                    updateNotification('Please fill all required fields', 'error');
                    return;
                }

                // Show processing logs
                document.getElementById('processingLogs').style.display = 'block';
                document.getElementById('progressContainer').style.display = 'block';

                addLog('Starting Executive Private metadata processing...');
                updateProgress(10, 'Validating private metadata...');

                // Submit metadata to Executive Private endpoint
                const response = await fetch('/api/executive-private/process-metadata', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        folder_path: folderPath,
                        metadata: metadata
                    })
                });

                updateProgress(50, 'Processing private metadata...');

                const result = await response.json();

                if (result.success) {
                    updateProgress(100, 'Private processing complete!');
                    addLog('✅ Executive Private metadata processed successfully');
                    addLog('📁 Private folder moved to cross-checker queue');
                    addLog(`📍 Destination: ${result.destination_path}`);

                    updateNotification('Private file processed successfully and moved to cross-checker queue', 'success');

                    // Clear form and reload queue
                    document.getElementById('metadataEntryForm').reset();
                    setTimeout(() => {
                        loadQueue();
                        document.getElementById('queue-tab').click();
                    }, 2000);
                } else {
                    updateProgress(0, 'Private processing failed');
                    addLog('❌ Error: ' + result.error);
                    updateNotification('Error processing private metadata: ' + result.error, 'error');
                }

            } catch (error) {
                updateProgress(0, 'Private processing failed');
                addLog('❌ Exception: ' + error.message);
                updateNotification('Error processing private metadata: ' + error.message, 'error');
            }
        }

        // Collect form data
        function collectFormData() {
            return {
                ocd_vp_number: document.getElementById('ocdVpNumber').value,
                edited_file_name: document.getElementById('editedFileName').value,
                language: document.getElementById('language').value,
                edited_year: document.getElementById('editedYear').value,
                trim_closed_date: document.getElementById('trimClosedDate').value,
                total_duration: document.getElementById('totalDuration').value,
                video_type: document.getElementById('videoType').value,
                edited_location: document.getElementById('editedLocation').value,
                published_platforms: document.getElementById('publishedPlatforms').value,
                department_name: document.getElementById('departmentName').value,
                component: document.getElementById('component').value,
                content_tag: document.getElementById('contentTag').value,
                backup_type: document.getElementById('backupType').value,
                access_level: document.getElementById('accessLevel').value,
                renamed_folder_name: document.getElementById('renamedFolderName').value,
                number_of_files: document.getElementById('numberOfFiles').value,
                folder_size: document.getElementById('folderSize').value,
                audio_transcript_code: document.getElementById('audioTranscriptCode').value,
                audio_file_name: document.getElementById('audioFileName').value,
                transcription_file_name: document.getElementById('transcriptionFileName').value,
                social_published_date: document.getElementById('socialPublishedDate').value,
                social_video_id: document.getElementById('socialVideoId').value,
                social_duration: document.getElementById('socialDuration').value,
                social_title: document.getElementById('socialTitle').value,
                social_url: document.getElementById('socialUrl').value,
                social_description: document.getElementById('socialDescription').value
            };
        }

        // Validate form
        function validateForm(metadata) {
            const requiredFields = [
                'ocd_vp_number', 'edited_file_name', 'language', 'edited_year',
                'total_duration', 'video_type', 'edited_location', 'published_platforms',
                'department_name', 'component', 'content_tag', 'backup_type', 'access_level'
            ];

            for (const field of requiredFields) {
                if (!metadata[field] || metadata[field].trim() === '') {
                    return false;
                }
            }
            return true;
        }

        // Clear form function
        function clearForm() {
            document.getElementById('metadataEntryForm').reset();
            updateNotification('Executive Private form cleared', 'info');
        }

        // Copy metadata to selected files
        function copyMetadataToSelected() {
            const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked'));
            if (selectedFiles.length === 0) {
                updateNotification('Please select private files to copy metadata to', 'error');
                return;
            }

            const metadata = collectFormData();
            if (!validateForm(metadata)) {
                updateNotification('Please complete the private metadata form first', 'error');
                return;
            }

            // Store metadata for batch processing
            sessionStorage.setItem('executive_private_batch_metadata', JSON.stringify(metadata));
            updateNotification(`Private metadata copied to ${selectedFiles.length} files`, 'success');
        }

        // Process batch files for Executive Private
        async function processBatch() {
            const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedFiles.length === 0) {
                updateNotification('Please select private files for batch processing', 'error');
                return;
            }

            const batchMetadata = sessionStorage.getItem('executive_private_batch_metadata');
            if (!batchMetadata) {
                updateNotification('Please copy private metadata first', 'error');
                return;
            }

            // Show processing logs
            document.getElementById('processingLogs').style.display = 'block';
            document.getElementById('progressContainer').style.display = 'block';

            addLog(`Starting Executive Private batch processing for ${selectedFiles.length} files...`);

            let processed = 0;
            const total = selectedFiles.length;

            for (const filePath of selectedFiles) {
                try {
                    addLog(`Processing private file: ${filePath.split('\\').pop()}`);

                    const response = await fetch('/api/executive-private/process-metadata', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            folder_path: filePath,
                            metadata: JSON.parse(batchMetadata)
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        processed++;
                        addLog(`✅ Private file processed: ${filePath.split('\\').pop()}`);
                    } else {
                        addLog(`❌ Failed private file: ${filePath.split('\\').pop()} - ${result.error}`);
                    }

                    updateProgress((processed / total) * 100, `Processed ${processed}/${total} private files`);

                } catch (error) {
                    addLog(`❌ Error processing private file ${filePath.split('\\').pop()}: ${error.message}`);
                }
            }

            addLog(`Executive Private batch processing complete. Processed ${processed}/${total} files.`);
            updateNotification(`Executive Private batch processing complete. ${processed}/${total} files processed successfully`, 'success');

            // Reload queue after batch processing
            setTimeout(() => {
                loadQueue();
                document.getElementById('queue-tab').click();
            }, 2000);
        }

        // Add log entry
        function addLog(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `\n[${timestamp}] ${message}`;
            logContent.scrollTop = logContent.scrollHeight;
        }

        // Update progress bar
        function updateProgress(percentage, text) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            progressBar.style.width = percentage + '%';
            progressText.textContent = text || `${percentage}%`;
        }

        // Apply filters to queue table
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const sortBy = document.getElementById('sortBy').value;
            const filterSize = document.getElementById('filterSize').value;
            const filterDate = document.getElementById('filterDate').value;

            // Implement filtering logic here
            updateNotification('Executive Private filters applied', 'info');
        }

        // Show batch processing modal/section
        function showBatchProcessing() {
            const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedFiles.length === 0) {
                updateNotification('Please select private files for batch processing', 'error');
                return;
            }

            updateNotification(`Executive Private batch processing ${selectedFiles.length} files`, 'info');
            document.getElementById('batchSection').style.display = 'block';
        }

        function updateCheckboxListeners() {
            // Add checkbox event listeners
            const checkboxes = document.querySelectorAll('.file-checkbox');
            const selectAll = document.getElementById('selectAll');

            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedCount);
            });

            selectAll.addEventListener('change', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateSelectedCount();
            });
        }

        function updateSelectedCount() {
            const checked = document.querySelectorAll('.file-checkbox:checked').length;
            document.getElementById('selectedCount').textContent = `${checked} private files selected`;
        }

        // Auto-save functionality for Executive Private
        setInterval(() => {
            const formData = collectFormData();
            if (Object.values(formData).some(value => value && value.trim() !== '')) {
                localStorage.setItem('executive_private_autosave', JSON.stringify(formData));
            }
        }, 30000); // Auto-save every 30 seconds

        // Load auto-saved data on page load
        window.addEventListener('load', () => {
            const autoSaved = localStorage.getItem('executive_private_autosave');
            if (autoSaved) {
                try {
                    const data = JSON.parse(autoSaved);
                    // Populate form with auto-saved data
                    Object.keys(data).forEach(key => {
                        const element = document.getElementById(key.replace(/_/g, ''));
                        if (element && data[key]) {
                            element.value = data[key];
                        }
                    });
                } catch (error) {
                    console.error('Error loading Executive Private auto-saved data:', error);
                }
            }
        });

        // EXACT COPY - Folder Tree Functionality from Archives Assignment Console
        let selectedFolders = new Set();
        let folderData = [];

        // Load executive private folder tree when folder tab is clicked
        $('#folder-tab').on('click', function() {
            loadFolders();
        });

        // Load folder structure (EXACT COPY from Archives Console)
        async function loadFolders() {
            try {
                showAlert('Loading private folders...', 'info');
                const response = await fetch('/api/executive-private/folder-tree');
                const data = await response.json();

                if (data.success) {
                    folderData = data.folders;
                    renderFolderTree(data.folders);
                    updateStats();
                    hideAlert();
                } else {
                    showAlert('Error loading private folders: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('Error loading private folders: ' + error.message, 'danger');
            }
        }

        // Refresh folders (EXACT COPY from Archives Console)
        function refreshFolders() {
            loadFolders();
        }

        // Render folder tree (EXACT COPY from Archives Assignment Console)
        function renderFolderTree(folders) {
            const treeContainer = document.getElementById('folderTree');
            treeContainer.innerHTML = '';

            if (!folders || folders.length === 0) {
                treeContainer.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-folder-open fa-2x mb-2"></i><p>No private folders found</p></div>';
                return;
            }

            folders.forEach(folder => {
                const folderElement = createFolderElement(folder, 0);
                treeContainer.appendChild(folderElement);
            });
        }

        // Helper functions for enhanced display (EXACT COPY from Archives Assignment Console)
        function getFileIcon(extension, isMedia) {
            if (isMedia) {
                if (['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'].includes(extension)) {
                    return 'fas fa-file-video';
                } else if (['.mp3', '.wav', '.m4a', '.aac', '.ogg'].includes(extension)) {
                    return 'fas fa-file-audio';
                }
            }
            return 'fas fa-file';
        }

        function truncateFileName(name, maxLength) {
            if (name.length <= maxLength) return name;
            return name.substring(0, maxLength - 3) + '...';
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Create folder or file element with enhanced display (EXACT COPY from Archives Assignment Console)
        function createFolderElement(item, level) {
            const div = document.createElement('div');
            const isFile = item.type === 'file';
            div.className = `tree-node ${isFile ? 'file-node' : 'folder-node'}`;
            div.style.marginLeft = (level * 20) + 'px';

            const hasChildren = item.children && item.children.length > 0;

            if (isFile) {
                // File element with enhanced media detection
                const isMedia = item.is_media || false;
                const fileIcon = getFileIcon(item.extension, isMedia);
                const fileColor = isMedia ? 'text-success' : 'text-info';

                div.innerHTML = `
                    <div class="node-content" data-path="${item.path}" data-type="file">
                        <span class="expand-icon" style="visibility: hidden; width: 20px;"></span>
                        <i class="${fileIcon} me-2 ${fileColor}"></i>
                        <span class="file-name" title="${item.name}">${truncateFileName(item.name, 40)}</span>
                        <small class="text-muted ms-2">(${item.size || 'Unknown'})</small>
                        ${isMedia ? `
                            <button class="btn btn-sm btn-success ms-2" onclick="openFileInVLC('${escapeHtml(item.path)}')" title="Open in VLC">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-secondary ms-1" onclick="showFileInfo('${escapeHtml(item.path)}')" title="File Info">
                            <i class="fas fa-info"></i>
                        </button>
                    </div>
                `;
            } else {
                // Folder element with enhanced display (with private badge support)
                const folderIcon = hasChildren ? 'fas fa-folder' : 'fas fa-folder-open';
                const mediaCount = item.media_count || item.file_count || 0;
                const isPrivateFolder = item.is_private || false;

                div.innerHTML = `
                    <div class="node-content" data-path="${item.path}" data-type="folder">
                        <span class="expand-icon" onclick="toggleFolder(this)" style="visibility: ${hasChildren ? 'visible' : 'hidden'}">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                        <input type="checkbox" class="folder-checkbox" onchange="toggleFolderSelection('${escapeHtml(item.path)}', this.checked)">
                        <i class="${folderIcon} me-2 text-warning"></i>
                        <span class="folder-name" title="${item.name}">${truncateFileName(item.name, 35)}</span>
                        <small class="text-muted ms-2">(${mediaCount} media files)</small>
                        ${isPrivateFolder ? '<span class="private-badge ms-2">PRIVATE</span>' : ''}
                        ${mediaCount > 0 ? `
                            <button class="btn btn-sm btn-warning ms-2" onclick="openFolderInVLC('${escapeHtml(item.path)}')" title="Open first media file in VLC">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-info ms-1" onclick="showFolderInfo('${escapeHtml(item.path)}')" title="Folder Info">
                            <i class="fas fa-info"></i>
                        </button>
                    </div>
                `;
            }

            if (hasChildren) {
                const childrenDiv = document.createElement('div');
                childrenDiv.className = 'folder-children';
                childrenDiv.style.display = 'none';

                item.children.forEach(child => {
                    const childElement = createFolderElement(child, level + 1);
                    childrenDiv.appendChild(childElement);
                });

                div.appendChild(childrenDiv);
            }

            return div;
        }

        // Toggle folder expansion (EXACT COPY from Archives Assignment Console)
        function toggleFolder(element) {
            const icon = element.querySelector('i');
            const nodeContent = element.parentElement;
            const treeNode = nodeContent.parentElement;
            const childrenDiv = treeNode.querySelector('.folder-children');

            if (childrenDiv) {
                const isExpanded = childrenDiv.style.display !== 'none';
                childrenDiv.style.display = isExpanded ? 'none' : 'block';
                icon.className = isExpanded ? 'fas fa-chevron-right' : 'fas fa-chevron-down';
            }
        }

        // Toggle folder selection (EXACT COPY from Archives Assignment Console)
        function toggleFolderSelection(folderPath, isSelected) {
            if (isSelected) {
                selectedFolders.add(folderPath);
            } else {
                selectedFolders.delete(folderPath);
            }

            updateSelectedFoldersList();
            updateStats();

            // Update visual selection
            const nodeContent = document.querySelector(`[data-path="${folderPath}"]`);
            if (nodeContent) {
                const folderNode = nodeContent.parentElement;
                if (isSelected) {
                    folderNode.classList.add('selected');
                } else {
                    folderNode.classList.remove('selected');
                }
            }
        }

        // Update selected folders list (EXACT COPY from Archives Assignment Console)
        function updateSelectedFoldersList() {
            const listContainer = document.getElementById('selectedFoldersList');

            if (selectedFolders.size === 0) {
                listContainer.innerHTML = '<p class="text-muted">No folders selected</p>';
                return;
            }

            let html = '';
            selectedFolders.forEach(folderPath => {
                const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();
                html += `
                    <div class="selected-folder-item d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <span class="folder-name">${folderName}</span>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFolderSelection('${folderPath}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            });

            listContainer.innerHTML = html;
        }

        // Remove folder from selection (EXACT COPY from Archives Assignment Console)
        function removeFolderSelection(folderPath) {
            selectedFolders.delete(folderPath);

            // Uncheck checkbox
            const checkbox = document.querySelector(`[data-path="${folderPath}"]`).parentElement.querySelector('.folder-checkbox');
            if (checkbox) {
                checkbox.checked = false;
            }

            // Remove visual selection
            const nodeContent = document.querySelector(`[data-path="${folderPath}"]`);
            if (nodeContent) {
                nodeContent.parentElement.classList.remove('selected');
            }

            updateSelectedFoldersList();
            updateStats();
        }

        // Select all folders (EXACT COPY from Archives Assignment Console)
        function selectAllFolders() {
            const checkboxes = document.querySelectorAll('.folder-checkbox');
            checkboxes.forEach(checkbox => {
                if (!checkbox.checked) {
                    checkbox.checked = true;
                    const folderPath = checkbox.closest('.node-content').dataset.path;
                    toggleFolderSelection(folderPath, true);
                }
            });
        }

        // Clear all selections (EXACT COPY from Archives Assignment Console)
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.folder-checkbox');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    const folderPath = checkbox.closest('.node-content').dataset.path;
                    toggleFolderSelection(folderPath, false);
                }
            });
        }

        // Update statistics (EXACT COPY from Archives Assignment Console)
        function updateStats() {
            // Count only top-level folders (those with marginLeft: 0px)
            const allFolderCheckboxes = document.querySelectorAll('.folder-checkbox');
            let topLevelFolderCount = 0;

            allFolderCheckboxes.forEach(checkbox => {
                const treeNode = checkbox.closest('.tree-node');
                if (treeNode && treeNode.style.marginLeft === '0px') {
                    topLevelFolderCount++;
                }
            });
        }

        // VLC integration functions (EXACT COPY from Archives Assignment Console)
        function openFileInVLC(filePath) {
            fetch('/api/open-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `file_path=${encodeURIComponent(filePath)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotification('Private file opened in VLC successfully', 'success');
                } else {
                    updateNotification('Error opening private file: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error opening private file in VLC:', error);
                updateNotification('Failed to open private file in VLC', 'danger');
            });
        }

        function openFolderInVLC(folderPath) {
            fetch('/api/preview-folder-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ folder_path: folderPath })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotification(`Opened ${data.video_count} private videos in VLC`, 'success');
                } else {
                    updateNotification('Error previewing private folder: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error previewing private folder:', error);
                updateNotification('Failed to preview private folder', 'danger');
            });
        }

        // File/folder info functions (EXACT COPY from Archives Assignment Console)
        function showFileInfo(filePath) {
            updateNotification('Showing private file info: ' + filePath, 'info');
            // Implement file info modal
        }

        function showFolderInfo(folderPath) {
            updateNotification('Showing private folder info: ' + folderPath, 'info');
            // Implement folder info modal
        }

        function renderTreeNode(node, level) {
            if (!node) return '';

            const indent = '  '.repeat(level);
            const isFolder = node.type === 'folder';
            const icon = isFolder ? 'fa-folder' : 'fa-file';
            const iconColor = isFolder ? 'text-warning' : 'text-info';

            let html = `
                <div class="tree-node" style="margin-left: ${level * 20}px;">
                    <div class="d-flex align-items-center py-1 ${isFolder ? 'folder-item' : 'file-item'}"
                         onclick="${isFolder ? `toggleFolder(this, '${node.path}')` : `selectFile('${node.path}')`}"
                         style="cursor: pointer;">
                        <i class="fas ${icon} ${iconColor} me-2"></i>
                        <span>${node.name}</span>
                        ${node.size ? `<small class="text-muted ms-2">(${node.size})</small>` : ''}
                        ${isFolder && node.children ? `<i class="fas fa-chevron-right ms-auto toggle-icon"></i>` : ''}
                    </div>
                    ${isFolder && node.children ? `<div class="children" style="display: none;">` : ''}
            `;

            if (isFolder && node.children) {
                for (const child of node.children) {
                    html += renderTreeNode(child, level + 1);
                }
                html += '</div>';
            }

            html += '</div>';
            return html;
        }

        function toggleFolder(element, folderPath) {
            const childrenDiv = element.parentElement.querySelector('.children');
            const toggleIcon = element.querySelector('.toggle-icon');

            if (childrenDiv) {
                if (childrenDiv.style.display === 'none') {
                    childrenDiv.style.display = 'block';
                    toggleIcon.classList.remove('fa-chevron-right');
                    toggleIcon.classList.add('fa-chevron-down');
                } else {
                    childrenDiv.style.display = 'none';
                    toggleIcon.classList.remove('fa-chevron-down');
                    toggleIcon.classList.add('fa-chevron-right');
                }
            }

            // Load folder details
            loadFolderDetails(folderPath);
        }

        function selectFile(filePath) {
            selectedVideoFile = filePath;

            // Check if it's a video file
            const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'];
            const isVideo = videoExtensions.some(ext => filePath.toLowerCase().endsWith(ext));

            if (isVideo) {
                document.getElementById('videoPreviewContainer').innerHTML = `
                    <div class="text-center p-4 bg-light rounded">
                        <i class="fas fa-video fa-3x text-success mb-3"></i>
                        <p><strong>Selected Private Video:</strong></p>
                        <p class="small text-muted">${filePath}</p>
                        <button class="btn btn-primary" onclick="openSelectedInVlc()">
                            <i class="fas fa-external-link-alt me-2"></i>Open in VLC
                        </button>
                    </div>
                `;
            } else {
                document.getElementById('videoPreviewContainer').innerHTML = `
                    <div class="text-center p-4 bg-light rounded">
                        <i class="fas fa-file fa-3x text-info mb-3"></i>
                        <p><strong>Selected File:</strong></p>
                        <p class="small text-muted">${filePath}</p>
                        <p class="text-muted">Not a video file</p>
                    </div>
                `;
            }
        }

        async function loadFolderDetails(folderPath) {
            selectedFolderPath = folderPath;

            try {
                const response = await fetch(`/api/folder-details?path=${encodeURIComponent(folderPath)}`);
                const data = await response.json();

                if (data.success) {
                    const details = data.details;
                    document.getElementById('folderDetailsContainer').innerHTML = `
                        <h6><i class="fas fa-folder me-2"></i>${details.name} <span class="private-badge">PRIVATE</span></h6>
                        <hr>
                        <p><strong>Path:</strong> <small>${details.path}</small></p>
                        <p><strong>Files:</strong> ${details.file_count}</p>
                        <p><strong>Size:</strong> ${details.size}</p>
                        <p><strong>Video Files:</strong> ${details.video_files}</p>
                        <p><strong>Audio Files:</strong> ${details.audio_files}</p>

                        <div class="mt-3">
                            <button class="btn btn-sm btn-primary me-2" onclick="previewFolderInVlc('${folderPath}')">
                                <i class="fas fa-play me-1"></i>Preview Videos
                            </button>
                            <button class="btn btn-sm btn-success" onclick="processFolderFromTree('${folderPath}')">
                                <i class="fas fa-cogs me-1"></i>Process Folder
                            </button>
                        </div>
                    `;
                } else {
                    document.getElementById('folderDetailsContainer').innerHTML =
                        '<div class="alert alert-warning">Error loading folder details</div>';
                }
            } catch (error) {
                console.error('Error loading folder details:', error);
                document.getElementById('folderDetailsContainer').innerHTML =
                    '<div class="alert alert-danger">Failed to load folder details</div>';
            }
        }

        async function openSelectedInVlc() {
            if (!selectedVideoFile) {
                alert('No video file selected');
                return;
            }

            try {
                const response = await fetch('/api/open-vlc', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `file_path=${encodeURIComponent(selectedVideoFile)}`
                });

                const data = await response.json();
                if (data.success) {
                    updateNotification('Private video opened in VLC successfully', 'success');
                } else {
                    updateNotification('Error opening video: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error opening VLC:', error);
                updateNotification('Failed to open VLC', 'danger');
            }
        }

        async function previewFolderInVlc(folderPath) {
            try {
                const response = await fetch('/api/preview-folder-vlc', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ folder_path: folderPath })
                });

                const data = await response.json();
                if (data.success) {
                    updateNotification(`Opened ${data.video_count} private videos in VLC`, 'success');
                } else {
                    updateNotification('Error previewing folder: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error previewing folder:', error);
                updateNotification('Failed to preview folder', 'danger');
            }
        }

        function processFolderFromTree(folderPath) {
            // Switch to processing tab and pre-fill the folder
            document.getElementById('processing-tab').click();

            // Set the current file for processing
            currentFile = {
                folder_path: folderPath,
                folder_name: folderPath.split('\\').pop() || folderPath.split('/').pop()
            };

            updateNotification(`Ready to process private folder: ${currentFile.folder_name}`, 'info');

            // Scroll to metadata form
            document.getElementById('metadataForm').scrollIntoView({ behavior: 'smooth' });
        }

        // VLC integration functions for Executive Private
        function openExecutivePrivateFileInVLC(filePath) {
            fetch('/api/open-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `file_path=${encodeURIComponent(filePath)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotification('Private file opened in VLC successfully', 'success');
                } else {
                    updateNotification('Error opening private file: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error opening private file in VLC:', error);
                updateNotification('Failed to open private file in VLC', 'danger');
            });
        }

        function openExecutivePrivateFolderInVLC(folderPath) {
            fetch('/api/preview-folder-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ folder_path: folderPath })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotification(`Opened ${data.video_count} private videos in VLC`, 'success');
                } else {
                    updateNotification('Error previewing private folder: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error previewing private folder:', error);
                updateNotification('Failed to preview private folder', 'danger');
            });
        }

        // Process executive private folder
        function processExecutivePrivateFolder(folderPath) {
            // Switch to processing tab and pre-fill the folder
            document.getElementById('processing-tab').click();

            // Set the current file for processing
            currentFile = {
                folder_path: folderPath,
                folder_name: folderPath.split('\\').pop() || folderPath.split('/').pop()
            };

            updateNotification(`Ready to process private folder: ${currentFile.folder_name}`, 'info');

            // Scroll to metadata form
            document.getElementById('metadataForm').scrollIntoView({ behavior: 'smooth' });
        }

        // File/folder info functions
        function showExecutivePrivateFileInfo(filePath) {
            updateNotification('Showing private file info: ' + filePath, 'info');
            // Implement file info modal
        }

        function showExecutivePrivateFolderInfo(folderPath) {
            updateNotification('Showing private folder info: ' + folderPath, 'info');
            // Implement folder info modal
        }

        // Audio Extraction Functionality for Executive Private
        async function extractAudio() {
            const audioFileSelect = document.getElementById('audioFileSelect');
            const selectedFile = audioFileSelect.value;

            if (!selectedFile) {
                alert('Please select an audio file to extract');
                return;
            }

            if (!currentFile) {
                alert('No folder selected for processing');
                return;
            }

            const extractBtn = document.getElementById('extractAudioBtn');
            const progressContainer = document.getElementById('audioExtractionProgress');
            const progressBar = document.getElementById('audioProgressBar');
            const progressText = document.getElementById('audioProgressText');

            // Show progress and disable button
            extractBtn.disabled = true;
            extractBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Extracting...';
            progressContainer.style.display = 'block';

            try {
                const response = await fetch('/api/extract-audio', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        folder_path: currentFile.folder_path,
                        audio_file: selectedFile,
                        destination: 'T:\\To_Process\\Rough folder\\Folder to be cross checked',
                        access_level: 'private'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Simulate progress
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += 10;
                        progressBar.style.width = progress + '%';
                        progressText.textContent = progress + '%';

                        if (progress >= 100) {
                            clearInterval(progressInterval);
                            updateNotification('Private audio extracted successfully to cross-check folder', 'success');

                            // Update audio file name field
                            document.getElementById('audioFileName').value = data.extracted_file_name;
                        }
                    }, 200);
                } else {
                    updateNotification('Error extracting private audio: ' + data.error, 'danger');
                    progressContainer.style.display = 'none';
                }
            } catch (error) {
                console.error('Error extracting private audio:', error);
                updateNotification('Failed to extract private audio', 'danger');
                progressContainer.style.display = 'none';
            } finally {
                extractBtn.disabled = false;
                extractBtn.innerHTML = '<i class="fas fa-download me-2"></i>Extract Audio (.wav)';
            }
        }

        // Load audio files when a folder is selected for processing
        function loadAudioFiles(folderPath) {
            fetch(`/api/get-audio-files?folder_path=${encodeURIComponent(folderPath)}`)
                .then(response => response.json())
                .then(data => {
                    const audioSelect = document.getElementById('audioFileSelect');
                    audioSelect.innerHTML = '<option value="">Select audio file...</option>';

                    if (data.success && data.audio_files.length > 0) {
                        data.audio_files.forEach(file => {
                            const option = document.createElement('option');
                            option.value = file.path;
                            option.textContent = file.name;
                            audioSelect.appendChild(option);
                        });
                    } else {
                        audioSelect.innerHTML = '<option value="">No audio files found</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading private audio files:', error);
                    document.getElementById('audioFileSelect').innerHTML = '<option value="">Error loading files</option>';
                });
        }

        // Enhanced Private Audio File Selection & Extraction Functions
        let availablePrivateAudioFiles = [];

        // Load private audio files from current folder
        async function loadPrivateAudioFiles() {
            if (!currentFile || !currentFile.folder_path) {
                updateNotification('No folder selected. Please select a folder first.', 'warning');
                return;
            }

            try {
                updateNotification('Loading private audio files...', 'info');
                const response = await fetch('/api/get-audio-files', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ folder_path: currentFile.folder_path })
                });

                const data = await response.json();
                if (data.success) {
                    availablePrivateAudioFiles = data.audio_files;
                    populatePrivateAudioFileSelect(data.audio_files);
                    updateNotification(`Found ${data.audio_files.length} private audio files`, 'success');
                } else {
                    updateNotification('Error loading private audio files: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error loading private audio files:', error);
                updateNotification('Failed to load private audio files', 'danger');
            }
        }

        // Populate private audio file select dropdown
        function populatePrivateAudioFileSelect(audioFiles) {
            const select = document.getElementById('selectedPrivateAudioFile');
            select.innerHTML = '<option value="">Select a private audio file...</option>';

            audioFiles.forEach(file => {
                const option = document.createElement('option');
                option.value = file.path;
                option.textContent = `${file.name} (${file.size})`;
                select.appendChild(option);
            });

            // Enable/disable buttons based on availability
            const hasAudioFiles = audioFiles.length > 0;
            document.getElementById('previewPrivateAudioBtn').disabled = !hasAudioFiles;
            document.getElementById('extractPrivateAudioBtn').disabled = !hasAudioFiles;

            // Add change event listener
            select.addEventListener('change', function() {
                const isSelected = this.value !== '';
                document.getElementById('previewPrivateAudioBtn').disabled = !isSelected;
                document.getElementById('extractPrivateAudioBtn').disabled = !isSelected;
            });
        }

        // Preview selected private audio file
        function previewSelectedPrivateAudio() {
            const selectedPath = document.getElementById('selectedPrivateAudioFile').value;
            if (!selectedPath) {
                updateNotification('Please select a private audio file first', 'warning');
                return;
            }

            // Open private audio file in VLC
            fetch('/api/open-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `file_path=${encodeURIComponent(selectedPath)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotification('Private audio file opened in VLC for preview', 'success');
                } else {
                    updateNotification('Error opening private audio file: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error opening private audio file:', error);
                updateNotification('Failed to open private audio file', 'danger');
            });
        }

        // Extract and move private audio file to cross-check folder
        async function extractPrivateAudioFile() {
            const selectedPath = document.getElementById('selectedPrivateAudioFile').value;
            const audioFileName = document.getElementById('audioFileName').value;

            if (!selectedPath) {
                updateNotification('Please select a private audio file first', 'warning');
                return;
            }

            if (!audioFileName) {
                updateNotification('Please enter an audio file name', 'warning');
                return;
            }

            try {
                updateNotification('Extracting private audio file...', 'info');
                document.getElementById('privateAudioExtractionStatus').innerHTML =
                    '<div class="spinner-border spinner-border-sm me-2"></div>Extracting private audio...';

                const response = await fetch('/api/extract-audio', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        source_path: selectedPath,
                        audio_file_name: audioFileName,
                        folder_name: currentFile.folder_name,
                        is_private: true
                    })
                });

                const data = await response.json();
                if (data.success) {
                    document.getElementById('privateAudioExtractionStatus').innerHTML =
                        '<div class="alert alert-success mb-0"><i class="fas fa-check me-2"></i>Private audio extracted successfully!</div>';
                    updateNotification(`Private audio file extracted to: ${data.destination_path}`, 'success');

                    // Disable extract button to prevent duplicate extraction
                    document.getElementById('extractPrivateAudioBtn').disabled = true;
                    document.getElementById('extractPrivateAudioBtn').innerHTML =
                        '<i class="fas fa-check me-1"></i>Private Audio Extracted';
                } else {
                    document.getElementById('privateAudioExtractionStatus').innerHTML =
                        '<div class="alert alert-danger mb-0"><i class="fas fa-times me-2"></i>Private extraction failed!</div>';
                    updateNotification('Error extracting private audio: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error extracting private audio:', error);
                document.getElementById('privateAudioExtractionStatus').innerHTML =
                    '<div class="alert alert-danger mb-0"><i class="fas fa-times me-2"></i>Private extraction failed!</div>';
                updateNotification('Failed to extract private audio file', 'danger');
            }
        }

        // Auto-load private audio files when a file is processed
        function autoLoadPrivateAudioFiles() {
            if (currentFile && currentFile.folder_path) {
                loadPrivateAudioFiles();
            }
        }
    </script>
</body>
</html>
