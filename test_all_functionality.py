#!/usr/bin/env python3
"""
COMPREHENSIVE ARCHIVES SYSTEM TEST
Tests ALL functionality: <PERSON><PERSON>, <PERSON>hanced Batch, Old Batch, Path Fixing
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5001"

def test_all_functionality():
    """Test ALL Archives system functionality comprehensively"""
    print("🎯 COMPREHENSIVE ARCHIVES SYSTEM TEST")
    print("=" * 80)
    print("Testing: VLC Integration, Enhanced Batch, Old Batch, Path Fixing")
    print("=" * 80)
    
    # Create session for login
    session = requests.Session()
    
    # Login first
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = session.post(f"{BASE_URL}/login", data=login_data)
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Test cases with corrupted paths (as they come from the frontend)
    test_cases = [
        {
            "name": "Test 1: Cauvery Calling Core Video",
            "corrupted_path": "estored filesZ6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019OutputCauvery-Calling-core-video-2_5min-V2.mov",
            "category": "Social media single output with stems"
        },
        {
            "name": "Test 2: Daily Mystic Quote",
            "corrupted_path": "estored filesZ6472_Daily-Mystic-Quote_28-Aug-2019-And-29-Aug-2019_English_Consolidated_04-Oct-2019OutputsWithout-logo_DMQ_29-08-2019.mov",
            "category": "Social media outputs without stems"
        },
        {
            "name": "Test 3: Glimpses (EXACT FAILING ONE)",
            "corrupted_path": "estored filesZ6486_Glimpses_Cauvery-Calling_Media-Support_English_01Min-30Secs_Stems_04-Oct-2019OutputCaCa-Media-Glimpses-All.mov",
            "category": "Tamil files"
        },
        {
            "name": "Test 4: Output with Stems",
            "corrupted_path": "estored filesOutput with StemsZ5941_Promo_Isha-Yoga-Center-Updated_English_02Mins-55Secs_Premiere-Pro-Trimmed_12-Jun-2019OutputsPromo_IYC-Adiyogi.mov",
            "category": "Social media outputs with stems"
        }
    ]
    
    # Test results tracking
    vlc_results = []
    enhanced_batch_results = []
    old_batch_results = []
    
    print(f"\n🎬 TESTING VLC INTEGRATION ({len(test_cases)} files)")
    print("=" * 60)
    
    # Test 1: VLC Integration
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{test_case['name']}:")
        print(f"📥 Path: {test_case['corrupted_path'][:80]}...")
        
        # VLC endpoint expects form data, not JSON
        vlc_data = {
            'file_path': test_case['corrupted_path']
        }

        try:
            response = session.post(f"{BASE_URL}/api/open-vlc",
                                  data=vlc_data)  # Send as form data
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ VLC SUCCESS: {result.get('message', 'VLC launched')}")
                    vlc_results.append(True)
                else:
                    print(f"   ❌ VLC FAILED: {result.get('error', 'Unknown error')}")
                    vlc_results.append(False)
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                vlc_results.append(False)
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            vlc_results.append(False)
    
    print(f"\n🔧 TESTING ENHANCED BATCH PROCESSING ({len(test_cases)} files)")
    print("=" * 60)
    
    # Test 2: Enhanced Batch Processing
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{test_case['name']}:")
        
        enhanced_batch_data = {
            'file_paths': [test_case['corrupted_path']],
            'category': test_case['category'],
            'assign_to': 'Executor Public',
            'video_ids': f'TEST{i:03d}',
            'url': 'https://test.com',
            'remarks': f'Enhanced batch test {i}'
        }
        
        try:
            response = session.post(f"{BASE_URL}/api/enhanced-batch-assign",
                                  headers={'Content-Type': 'application/json'},
                                  data=json.dumps(enhanced_batch_data))
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    processed = result.get('processed', 0)
                    failed = result.get('failed', 0)
                    print(f"   ✅ ENHANCED BATCH SUCCESS: {processed} processed, {failed} failed")
                    enhanced_batch_results.append(processed > 0)
                else:
                    print(f"   ❌ ENHANCED BATCH FAILED: {result.get('error')}")
                    enhanced_batch_results.append(False)
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                enhanced_batch_results.append(False)
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            enhanced_batch_results.append(False)
    
    print(f"\n🔧 TESTING OLD BATCH PROCESSING ({len(test_cases)} files)")
    print("=" * 60)
    
    # Test 3: Old Batch Processing (the one causing issues)
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{test_case['name']}:")
        
        old_batch_data = {
            'files': [test_case['corrupted_path']],
            'category': test_case['category'],
            'user': 'Executor Public',
            'move_files': True
        }
        
        try:
            response = session.post(f"{BASE_URL}/api/batch-assign",
                                  headers={'Content-Type': 'application/json'},
                                  data=json.dumps(old_batch_data))
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    processed = result.get('processed', 0)
                    failed = result.get('failed', 0)
                    print(f"   ✅ OLD BATCH SUCCESS: {processed} processed, {failed} failed")
                    old_batch_results.append(processed > 0)
                else:
                    print(f"   ❌ OLD BATCH FAILED: {result.get('error')}")
                    old_batch_results.append(False)
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                old_batch_results.append(False)
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            old_batch_results.append(False)
    
    # Final Results Summary
    print("\n" + "=" * 80)
    print("🎯 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("=" * 80)
    
    vlc_success = sum(vlc_results)
    enhanced_success = sum(enhanced_batch_results)
    old_success = sum(old_batch_results)
    total_tests = len(test_cases)
    
    print(f"🎬 VLC Integration:        {vlc_success}/{total_tests} ({'✅ PASS' if vlc_success == total_tests else '❌ FAIL'})")
    print(f"🔧 Enhanced Batch:        {enhanced_success}/{total_tests} ({'✅ PASS' if enhanced_success == total_tests else '❌ FAIL'})")
    print(f"🔧 Old Batch:             {old_success}/{total_tests} ({'✅ PASS' if old_success == total_tests else '❌ FAIL'})")
    
    overall_success = vlc_success + enhanced_success + old_success
    overall_total = total_tests * 3
    
    print(f"\n🎉 OVERALL SYSTEM:        {overall_success}/{overall_total} ({'✅ FULLY WORKING' if overall_success == overall_total else '❌ NEEDS FIXES'})")
    
    if overall_success == overall_total:
        print("\n🎉 🎉 🎉 ALL FUNCTIONALITY IS WORKING PERFECTLY! 🎉 🎉 🎉")
        print("✅ VLC Integration: WORKING")
        print("✅ Enhanced Batch Processing: WORKING") 
        print("✅ Old Batch Processing: WORKING")
        print("✅ Path Fixing: WORKING")
        print("✅ Archives System: 100% FUNCTIONAL")
    else:
        print("\n❌ Some functionality needs attention:")
        if vlc_success < total_tests:
            print("❌ VLC Integration has issues")
        if enhanced_success < total_tests:
            print("❌ Enhanced Batch Processing has issues")
        if old_success < total_tests:
            print("❌ Old Batch Processing has issues")
    
    print("\n🔧 Check Flask logs for detailed path fixing information:")
    print("   - Look for '🔧 Original path:' and '🔧 Fixed path:' messages")
    print("   - Look for '✅ File exists:' and '🚀 Launching VLC:' messages")
    print("   - Look for 'Google Sheets logging successful' messages")

if __name__ == "__main__":
    test_all_functionality()
