#!/usr/bin/env python3
"""
Simulate exact browser experience for Preview button debugging
"""

import requests
import json
import os

def debug_browser_preview():
    print("🔍 SIMULATING BROWSER PREVIEW BUTTON CLICK")
    print("=" * 60)
    
    # Create session to maintain login state (like browser)
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # STEP 1: Simulate browser login
        print("1. 🔐 Simulating Browser Login...")
        login_data = {
            'username': 'executor_public',
            'password': 'Shiva@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return
        
        print("   ✅ Login successful!")
        
        # STEP 2: Simulate loading the Executive Public page
        print("\n2. 📄 Loading Executive Public Page...")
        page_response = session.get(f"{base_url}/executor-public")
        print(f"   Page Status: {page_response.status_code}")
        
        # STEP 3: Simulate JavaScript loading the queue (what happens on page load)
        print("\n3. 📋 Simulating JavaScript Queue Loading...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        print(f"   Queue API Status: {queue_response.status_code}")
        
        if queue_response.status_code != 200:
            print("   ❌ Queue loading failed!")
            return
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue API error: {queue_data.get('error')}")
            return
        
        files = queue_data.get('files', [])
        print(f"   ✅ Queue loaded: {len(files)} files")
        
        if not files:
            print("   ❌ No files to test!")
            return
        
        # STEP 4: Examine the first file data (what JavaScript receives)
        test_file = files[0]
        print(f"\n4. 🔍 Examining File Data (what JavaScript receives)...")
        print(f"   📁 Folder Name: {test_file.get('folder_name', 'MISSING')}")
        print(f"   📂 Folder Path: {test_file.get('folder_path', 'MISSING')}")
        print(f"   🆔 Queue Item ID: {test_file.get('queue_item_id', 'MISSING')}")
        print(f"   📊 File Count: {test_file.get('file_count', 'MISSING')}")
        
        folder_path = test_file.get('folder_path', '')
        
        # STEP 5: Check if the folder path exists (backend validation)
        print(f"\n5. 📁 Checking Folder Path Existence...")
        print(f"   Path to check: '{folder_path}'")
        
        if not folder_path:
            print("   ❌ PROBLEM FOUND: Folder path is empty!")
            return
        
        if os.path.exists(folder_path):
            print("   ✅ Folder exists on filesystem")
            
            # Count video files
            video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.f4v'}
            video_files = []
            
            for root, dirs, files_in_dir in os.walk(folder_path):
                for file_name in files_in_dir:
                    if any(file_name.lower().endswith(ext) for ext in video_extensions):
                        video_files.append(os.path.join(root, file_name))
            
            print(f"   🎥 Video files found: {len(video_files)}")
            if video_files:
                for i, video_file in enumerate(video_files[:3]):
                    print(f"      {i+1}. {os.path.basename(video_file)}")
                if len(video_files) > 3:
                    print(f"      ... and {len(video_files) - 3} more")
        else:
            print("   ❌ PROBLEM FOUND: Folder does not exist!")
            print(f"   Expected path: {folder_path}")
            
            # Check if parent directory exists
            parent_dir = os.path.dirname(folder_path)
            if os.path.exists(parent_dir):
                print(f"   📂 Parent directory exists: {parent_dir}")
                print("   📁 Contents of parent directory:")
                try:
                    contents = os.listdir(parent_dir)
                    for item in contents[:5]:
                        print(f"      • {item}")
                    if len(contents) > 5:
                        print(f"      ... and {len(contents) - 5} more items")
                except Exception as e:
                    print(f"      ❌ Cannot list contents: {e}")
            else:
                print(f"   ❌ Parent directory also doesn't exist: {parent_dir}")
            return
        
        # STEP 6: Simulate the exact Preview button click (JavaScript fetch)
        print(f"\n6. 🖱️ Simulating Preview Button Click...")
        print(f"   Sending folder_path: '{folder_path}'")
        
        # This is exactly what the JavaScript does
        preview_payload = {
            'folder_path': folder_path
        }
        
        print(f"   📤 Request payload: {json.dumps(preview_payload, indent=2)}")
        
        preview_response = session.post(
            f"{base_url}/api/preview-video",
            json=preview_payload,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   📥 Response Status: {preview_response.status_code}")
        
        if preview_response.status_code == 200:
            preview_data = preview_response.json()
            print(f"   📄 Response Data: {json.dumps(preview_data, indent=2)}")
            
            if preview_data.get('success'):
                print("   ✅ Preview API successful!")
                video_files_opened = preview_data.get('video_files', [])
                print(f"   🎬 Video files opened: {len(video_files_opened)}")
            else:
                print(f"   ❌ Preview API failed: {preview_data.get('error')}")
                
                # STEP 7: Debug the specific error
                error_msg = preview_data.get('error', '')
                if 'Folder not found' in error_msg:
                    print("\n7. 🔍 DEBUGGING 'Folder not found' ERROR...")
                    print("   This means the backend received the path but os.path.exists() returned False")
                    print("   Possible causes:")
                    print("   • Path encoding issues (special characters)")
                    print("   • Path separator issues (forward vs backward slashes)")
                    print("   • Permissions issues")
                    print("   • Path case sensitivity")
                    print("   • Network drive access issues")
        else:
            print(f"   ❌ HTTP Error: {preview_response.status_code}")
            try:
                error_data = preview_response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Raw response: {preview_response.text}")
        
        # STEP 8: Test all files to see if it's a pattern
        print(f"\n8. 🔍 Testing All Files to Identify Pattern...")
        
        for i, file in enumerate(files[:3], 1):  # Test first 3 files
            folder_path = file.get('folder_path', '')
            folder_name = file.get('folder_name', 'Unknown')
            
            print(f"\n   File {i}: {folder_name}")
            print(f"   Path: '{folder_path}'")
            print(f"   Exists: {os.path.exists(folder_path) if folder_path else 'NO PATH'}")
            
            if folder_path:
                # Test preview API for this file
                test_response = session.post(
                    f"{base_url}/api/preview-video",
                    json={'folder_path': folder_path},
                    headers={'Content-Type': 'application/json'}
                )
                
                if test_response.status_code == 200:
                    test_data = test_response.json()
                    if test_data.get('success'):
                        print(f"   Preview: ✅ Success")
                    else:
                        print(f"   Preview: ❌ {test_data.get('error')}")
                else:
                    print(f"   Preview: ❌ HTTP {test_response.status_code}")
        
        print("\n" + "=" * 60)
        print("🎯 DIAGNOSIS COMPLETE")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_browser_preview()
