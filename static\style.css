/* Archives Department File Management System - Custom Styles */

:root {
    --primary-color: #2196f3;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Button Enhancements */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1976d2;
    border-color: #1976d2;
    transform: translateY(-1px);
}

/* Progress Bar Enhancements */
.progress {
    height: 1rem;
    border-radius: 0.5rem;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 0.5rem;
    transition: width 0.6s ease;
}

/* Table Enhancements */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(33, 150, 243, 0.05);
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
}

/* Badge Enhancements */
.badge {
    font-weight: 500;
    border-radius: 0.375rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

/* Folder Tree Styles */
.folder-tree {
    max-height: 600px;
    overflow-y: auto;
}

.tree-item {
    padding: 0.5rem;
    margin: 0.25rem 0;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.tree-item:hover {
    background-color: var(--light-color);
    border-left-color: var(--primary-color);
}

.tree-item.selected {
    background-color: rgba(33, 150, 243, 0.1);
    border-left-color: var(--primary-color);
}

.folder-icon {
    color: #ffc107;
    margin-right: 0.5rem;
}

.file-icon {
    color: var(--secondary-color);
    margin-right: 0.5rem;
}

/* Video Player Enhancements */
.video-container {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: #000;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 1rem;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-success {
    background-color: var(--success-color);
}

.status-warning {
    background-color: var(--warning-color);
}

.status-danger {
    background-color: var(--danger-color);
}

.status-info {
    background-color: var(--info-color);
}

/* Dashboard Stats */
.stat-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    .card-header {
        background-color: #3d3d3d;
        border-bottom-color: #4d4d4d;
    }
    
    .table {
        color: #e0e0e0;
    }
    
    .table thead th {
        background-color: #3d3d3d;
        color: #e0e0e0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
