#!/usr/bin/env python3
"""
ADD TEST DATA AND R<PERSON><PERSON>DUCE DATABASE LOCK ERROR
Add test data to the database and then reproduce the database lock error
"""

import sqlite3
import requests
import json
import time
import threading
from datetime import datetime

def add_test_data_to_database():
    """Add test data directly to the database"""
    print("🔧 Adding test data to database...")
    
    try:
        conn = sqlite3.connect('archives_management.db')
        cursor = conn.cursor()
        
        # Add test queue items
        test_items = [
            {
                'folder_name': 'DB_Lock_Test_Folder_1',
                'folder_path': r'T:\To_Process\Rough folder\Internal video without stems\DB_Lock_Test_Folder_1',
                'original_path': r'T:\To_Process\Rough folder\restored files\DB_Lock_Test_Folder_1',
                'category': 'Internal video without stems',
                'assign_to': 'Executive Public',
                'video_ids': 'DB-LOCK-001',
                'url': 'https://test.com/db-lock-1',
                'remarks': 'Database lock test folder 1',
                'operation_type': 'move',
                'status': 'completed',
                'assigned_by': 1
            },
            {
                'folder_name': 'DB_Lock_Test_Folder_2',
                'folder_path': r'T:\To_Process\Rough folder\Miscellaneous\DB_Lock_Test_Folder_2',
                'original_path': r'T:\To_Process\Rough folder\restored files\DB_Lock_Test_Folder_2',
                'category': 'Miscellaneous',
                'assign_to': 'Executive Public',
                'video_ids': 'DB-LOCK-002',
                'url': 'https://test.com/db-lock-2',
                'remarks': 'Database lock test folder 2',
                'operation_type': 'move',
                'status': 'completed',
                'assigned_by': 1
            },
            {
                'folder_name': 'DB_Lock_Test_Folder_3',
                'folder_path': r'T:\To_Process\Rough folder\For Editing\DB_Lock_Test_Folder_3',
                'original_path': r'T:\To_Process\Rough folder\restored files\DB_Lock_Test_Folder_3',
                'category': 'For Editing',
                'assign_to': 'Executive Public',
                'video_ids': 'DB-LOCK-003',
                'url': 'https://test.com/db-lock-3',
                'remarks': 'Database lock test folder 3',
                'operation_type': 'move',
                'status': 'completed',
                'assigned_by': 1
            }
        ]
        
        for item in test_items:
            cursor.execute('''
                INSERT INTO queue_items (
                    folder_name, folder_path, original_path, category, assign_to,
                    video_ids, url, remarks, operation_type, status, assigned_by,
                    created_at, processed_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (
                item['folder_name'], item['folder_path'], item['original_path'],
                item['category'], item['assign_to'], item['video_ids'],
                item['url'], item['remarks'], item['operation_type'],
                item['status'], item['assigned_by']
            ))
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ Added {len(test_items)} test queue items")
        return True
        
    except Exception as e:
        print(f"   ❌ Error adding test data: {e}")
        return False

def reproduce_database_lock_with_real_data():
    """Reproduce database lock error with real test data"""
    print("🚨 REPRODUCING DATABASE LOCK ERROR WITH REAL DATA")
    print("=" * 80)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # STEP 1: Login
        print("1. 🔐 Login to Executor Public...")
        login_data = {
            'username': 'executor_public',
            'password': 'Shiva@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return False
        
        print("   ✅ Login successful!")
        
        # STEP 2: Get queue items
        print("\n2. 📋 Get queue items...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        if queue_response.status_code != 200:
            print("   ❌ Queue load failed!")
            return False
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue error: {queue_data.get('error')}")
            return False
        
        files = queue_data.get('files', [])
        print(f"   ✅ Queue loaded: {len(files)} files")
        
        if not files:
            print("   ⚠️ No files to test!")
            return False
        
        # STEP 3: Test single processing request first
        print("\n3. 🔍 Testing single processing request...")
        test_file = files[0]
        queue_item_id = test_file.get('queue_item_id')
        folder_name = test_file.get('folder_name', 'Unknown')
        
        print(f"   📂 Testing: {folder_name}")
        print(f"   🆔 Queue ID: {queue_item_id}")
        
        # Create comprehensive metadata for processing
        test_metadata = {
            'ocd_vp_number': 'DB-LOCK-SINGLE-2025-001',
            'edited_file_name': f'DB_Lock_Single_Test_{folder_name}',
            'language': 'English',
            'edited_year': 2025,
            'trim_closed_date': '2025-01-15',
            'total_duration': '12:30',
            'video_type': 'Talk',
            'edited_location': 'Ashram',
            'published_platforms': 'YouTube',
            'department_name': 'Archives DB Lock Single Test',
            'component': 'Sadhguru',
            'content_tags': 'Spirituality',
            'backup_type': 'Full Backup',
            'access_level': 'Public',
            'software_show_name': f'DB_LOCK_SINGLE_RENAMED_{folder_name}',
            'audio_code': 'DB-LOCK-SINGLE-AUD-2025-001',
            'audio_file_name': f'DB_Lock_Single_Audio_{folder_name}',
            'transcription_file_name': 'DB_Lock_Single_Transcription',
            'transcription_status': 'Pending',
            'published_date': '2025-01-15',
            'video_id': 'DB-LOCK-SINGLE-VID-2025-001',
            'social_media_title': 'DB Lock Single Test Video',
            'description': 'Database lock single test video description',
            'social_media_url': 'https://youtube.com/db-lock-single-test',
            'duration_category': '12 minutes 30 seconds',
            'processing_notes': 'Database lock single error reproduction test'
        }
        
        process_payload = {
            'queue_item_id': queue_item_id,
            'metadata': test_metadata
        }
        
        # Send single request
        print("   📤 Sending single processing request...")
        start_time = time.time()
        
        process_response = session.post(
            f"{base_url}/api/executive-public/process-metadata",
            json=process_payload,
            headers={'Content-Type': 'application/json'},
            timeout=120
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"   ⏱️ Processing time: {processing_time:.2f} seconds")
        print(f"   📥 Response Status: {process_response.status_code}")
        
        if process_response.status_code == 200:
            try:
                process_data = process_response.json()
                
                if process_data.get('success'):
                    print("   ✅ SINGLE REQUEST SUCCESSFUL!")
                    
                    processing_steps = process_data.get('processing_steps', {})
                    print("   📊 Processing Steps:")
                    print(f"      📊 Google Sheets: {processing_steps.get('google_sheets', False)}")
                    print(f"      📁 Folder Moved: {processing_steps.get('folder_moved', False)}")
                    print(f"      🔄 Folder Renamed: {processing_steps.get('folder_renamed', False)}")
                    print(f"      🎵 Audio Extracted: {processing_steps.get('audio_extracted', False)}")
                    
                    return True
                else:
                    error_msg = process_data.get('error', 'Unknown error')
                    print(f"   🚨 SINGLE REQUEST ERROR: {error_msg}")
                    
                    if 'database is locked' in error_msg.lower():
                        print("   🎯 DATABASE LOCK ERROR REPRODUCED IN SINGLE REQUEST!")
                        return False
                    else:
                        print(f"   ⚠️ Different error: {error_msg}")
                        return False
            except json.JSONDecodeError:
                print("   ❌ Invalid JSON response!")
                print(f"      Raw response: {process_response.text[:200]}...")
                return False
        else:
            print(f"   ❌ HTTP Error: {process_response.status_code}")
            try:
                error_data = process_response.json()
                print(f"      Error details: {error_data}")
                
                if 'database is locked' in str(error_data).lower():
                    print("   🎯 DATABASE LOCK ERROR IN HTTP RESPONSE!")
                    return False
            except:
                print(f"      Raw response: {process_response.text[:200]}...")
            return False
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚨 ADD TEST DATA AND REPRODUCE DATABASE LOCK ERROR")
    print("=" * 80)
    
    # Step 1: Add test data
    if not add_test_data_to_database():
        print("❌ Failed to add test data")
        return False
    
    # Step 2: Wait a moment for data to be available
    time.sleep(2)
    
    # Step 3: Try to reproduce the error
    success = reproduce_database_lock_with_real_data()
    
    print("\n" + "=" * 80)
    print("🎯 DATABASE LOCK ERROR REPRODUCTION RESULTS")
    print("=" * 80)
    
    if success:
        print("✅ PROCESSING SUCCESSFUL - NO DATABASE LOCK ERROR!")
        print("\n🔧 POSSIBLE REASONS:")
        print("   1. ✅ Database connection fixes are working")
        print("   2. ✅ Context manager is properly managing connections")
        print("   3. ✅ WAL mode is improving concurrency")
        print("   4. ✅ Retry logic is handling temporary locks")
        
        print("\n🎯 CONCLUSION:")
        print("   The database lock error may already be fixed!")
        print("   The implemented connection management is working correctly.")
        
    else:
        print("🎯 DATABASE LOCK ERROR REPRODUCED!")
        print("\n🔍 CONFIRMED ISSUES:")
        print("   ❌ Database connections are not properly managed")
        print("   ❌ Multiple operations accessing database simultaneously")
        print("   ❌ SQLite locking under concurrent load")
        
        print("\n🔧 FIXES NEEDED:")
        print("   1. Implement proper connection pooling")
        print("   2. Use database transactions for related operations")
        print("   3. Add retry logic with exponential backoff")
        print("   4. Consider switching to PostgreSQL for better concurrency")
    
    return success

if __name__ == "__main__":
    main()
