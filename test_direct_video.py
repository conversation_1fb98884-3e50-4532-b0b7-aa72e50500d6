#!/usr/bin/env python3
"""
Direct test of video serving with proper path
"""

import requests
import urllib.parse

# Test the exact video file
VIDEO_PATH = r"T:\To_Process\Rough folder\restored files\Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019\Output\Cauvery-Calling-core-video-2_5min-V2.mov"

def test_video_direct():
    """Test video serving directly"""
    print("🎥 Testing Direct Video Serving")
    print(f"File: {VIDEO_PATH}")
    
    # Login first
    session = requests.Session()
    login_data = {'username': 'assigner', 'password': '<PERSON>@123'}
    response = session.post("http://127.0.0.1:5001/login", data=login_data)
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    print("✅ Login successful")
    
    # Test video info
    print("\n📊 Testing video info...")
    encoded_path = urllib.parse.quote(VIDEO_PATH, safe='')
    info_url = f"http://127.0.0.1:5001/api/video-info?path={encoded_path}"
    
    response = session.get(info_url)
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            info = data['info']
            print(f"✅ Video info working")
            print(f"   Name: {info['name']}")
            print(f"   Size: {info['size_formatted']}")
        else:
            print(f"❌ Video info failed: {data.get('error')}")
    else:
        print(f"❌ Video info API failed: {response.status_code}")
    
    # Test video serving
    print("\n🎬 Testing video serving...")
    video_url = f"http://127.0.0.1:5001/api/serve-video?path={encoded_path}"
    
    # Test HEAD request
    response = session.head(video_url)
    print(f"HEAD request: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Video serving working!")
        print(f"Content-Length: {response.headers.get('Content-Length', 'Unknown')}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        
        # Test range request
        headers = {'Range': 'bytes=0-1023'}
        response = session.get(video_url, headers=headers)
        print(f"Range request: {response.status_code} ({len(response.content)} bytes)")
        
        if response.status_code in [200, 206]:
            print("✅ Video streaming working!")
            print(f"✅ URL: {video_url}")
            print("\n🎉 VIDEO SERVING IS WORKING PERFECTLY!")
            print("The issue is in the JavaScript path encoding, not the backend.")
        else:
            print(f"❌ Range request failed: {response.status_code}")
    else:
        print(f"❌ Video serving failed: {response.status_code}")

if __name__ == "__main__":
    test_video_direct()
