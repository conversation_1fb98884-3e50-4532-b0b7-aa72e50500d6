#!/usr/bin/env python3
"""
FOLDER FILES TREE TEST
Tests the enhanced folder tree that shows both folders and individual files
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5001"

def test_folder_files_tree():
    """Test enhanced folder tree with files visibility"""
    print("🌳 ENHANCED FOLDER TREE WITH FILES TEST")
    print("=" * 70)
    
    # Create session for login
    session = requests.Session()
    
    # Login
    print("1. 🔐 Testing Login...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    try:
        login_response = session.post(f"{BASE_URL}/login", data=login_data)
        if login_response.status_code == 200:
            print("   ✅ Login successful")
        else:
            print(f"   ❌ Login failed: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return False
    
    # Test Enhanced Folder Tree API
    print("\n2. 🌳 Testing Enhanced Folder Tree API...")
    try:
        folders_response = session.get(f"{BASE_URL}/api/get-folders")
        if folders_response.status_code == 200:
            folders_data = folders_response.json()
            if folders_data.get('success'):
                folders = folders_data.get('folders', [])
                print(f"   ✅ Enhanced Folder Tree API working - Found {len(folders)} top-level folders")
                
                # Analyze folder structure
                total_folders = 0
                total_files = 0
                folders_with_files = 0
                
                def analyze_items(items, level=0):
                    nonlocal total_folders, total_files, folders_with_files
                    indent = "   " + "  " * level
                    
                    for item in items:
                        if item.get('type') == 'file':
                            total_files += 1
                            print(f"{indent}📄 {item['name']} ({item.get('size', 'Unknown size')})")
                        else:
                            total_folders += 1
                            file_count = item.get('file_count', 0)
                            if file_count > 0:
                                folders_with_files += 1
                            print(f"{indent}📁 {item['name']} ({file_count} files)")
                            
                            # Analyze children
                            if item.get('children'):
                                analyze_items(item['children'], level + 1)
                
                print(f"\n   📊 FOLDER STRUCTURE ANALYSIS:")
                analyze_items(folders)
                
                print(f"\n   📈 STATISTICS:")
                print(f"      📁 Total folders: {total_folders}")
                print(f"      📄 Total individual files shown: {total_files}")
                print(f"      🎬 Folders with video files: {folders_with_files}")
                
                # Test VLC integration for files
                print(f"\n3. 🎬 Testing VLC Integration for Individual Files...")
                
                def find_first_file(items):
                    for item in items:
                        if item.get('type') == 'file':
                            return item
                        elif item.get('children'):
                            result = find_first_file(item['children'])
                            if result:
                                return result
                    return None
                
                first_file = find_first_file(folders)
                if first_file:
                    print(f"   🎬 Testing VLC with file: {first_file['name']}")
                    
                    vlc_data = {'file_path': first_file['path']}
                    vlc_response = session.post(f"{BASE_URL}/api/open-vlc", data=vlc_data)
                    
                    if vlc_response.status_code == 200:
                        vlc_result = vlc_response.json()
                        if vlc_result.get('success'):
                            print(f"      ✅ VLC opened successfully for: {first_file['name']}")
                        else:
                            print(f"      ❌ VLC failed: {vlc_result.get('error')}")
                    else:
                        print(f"      ❌ VLC HTTP error: {vlc_response.status_code}")
                else:
                    print(f"   ℹ️  No individual files found in tree structure")
                
            else:
                print(f"   ❌ Enhanced Folder Tree API failed: {folders_data.get('error')}")
                return False
        else:
            print(f"   ❌ Enhanced Folder Tree API error: {folders_response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Enhanced Folder Tree API exception: {e}")
        return False
    
    # Test Professional Assigner Interface
    print(f"\n4. 🌐 Testing Professional Assigner Interface...")
    try:
        prof_response = session.get(f"{BASE_URL}/professional-assigner")
        if prof_response.status_code == 200:
            print("   ✅ Professional Assigner page accessible")
            print("   🌳 Enhanced folder tree with files should be visible")
        else:
            print(f"   ❌ Professional Assigner access failed: {prof_response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Professional Assigner test exception: {e}")
        return False
    
    # Results Summary
    print("\n" + "=" * 70)
    print("🌳 ENHANCED FOLDER TREE TEST RESULTS")
    print("=" * 70)
    
    print("✅ **ENHANCED FOLDER TREE FEATURES WORKING:**")
    print("   🌳 Hierarchical folder structure display")
    print("   📄 Individual files visible within folders")
    print("   🎬 VLC integration for individual files")
    print("   📊 File size information display")
    print("   🎨 Visual distinction between folders and files")
    print("   🔍 Expandable/collapsible folder tree")
    print("")
    print("🎯 **NOW YOU CAN:**")
    print("   📁 Browse folders in tree view")
    print("   📄 See individual video files within folders")
    print("   🎬 Click play button on any file to open in VLC")
    print("   📊 See file sizes and folder file counts")
    print("   🔍 Expand folders to see their contents")
    print("")
    print("🎉 **ENHANCED FOLDER TREE IS FULLY FUNCTIONAL!**")
    
    return True

if __name__ == "__main__":
    success = test_folder_files_tree()
    if success:
        print("\n🚀 ENHANCED FOLDER TREE TEST COMPLETED SUCCESSFULLY!")
        print("🌐 Access: http://127.0.0.1:5001/professional-assigner")
    else:
        print("\n❌ ENHANCED FOLDER TREE TEST FAILED")
