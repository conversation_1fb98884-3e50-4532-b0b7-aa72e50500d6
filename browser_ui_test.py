#!/usr/bin/env python3
"""
BROWSER UI TEST SCRIPT
Test all UI components and user interactions in the browser
"""

import requests
import time

def test_assigner_interface():
    """Test Assigner interface in detail"""
    print("📋 TESTING ASSIGNER INTERFACE")
    print("=" * 60)
    
    session = requests.Session()
    session.post('http://127.0.0.1:5001/login', data={'username': 'assigner', 'password': 'Shiva@123'})
    
    # Test main interface
    response = session.get('http://127.0.0.1:5001/assigner')
    if response.status_code == 200:
        content = response.text
        
        # Check for key elements
        checks = {
            'Archives Assignment Console': 'Archives Assignment Console' in content,
            'Folder Tree View': 'folder-tree' in content or 'tree-view' in content,
            'Processing Queue': 'Processing Queue' in content,
            'Clear Completed Button': 'Clear Completed' in content,
            'Destination Categories': 'Internal with Project File' in content,
            'Assignment Dropdown': 'Executor Public' in content and 'Executor Private' in content
        }
        
        print("✅ Assigner Interface Elements:")
        for element, present in checks.items():
            status = "✅" if present else "❌"
            print(f"   {status} {element}")
        
        return all(checks.values())
    else:
        print(f"❌ Failed to load Assigner interface: {response.status_code}")
        return False

def test_executor_public_interface():
    """Test Executor Public interface"""
    print("\n🎬 TESTING EXECUTOR PUBLIC INTERFACE")
    print("=" * 60)
    
    session = requests.Session()
    session.post('http://127.0.0.1:5001/login', data={'username': 'executor_public', 'password': 'Shiva@123'})
    
    response = session.get('http://127.0.0.1:5001/executive-public')
    if response.status_code == 200:
        content = response.text
        
        checks = {
            'Executive Public Dashboard': 'Executive Public Dashboard' in content,
            'Folder Navigation Tab': 'Folder Navigation' in content,
            'Audio Extraction Section': 'Audio Extraction' in content,
            'Metadata Form': 'metadataForm' in content,
            'VLC Integration': 'Open in VLC' in content or 'vlc' in content.lower(),
            'Progress Bars': 'progress-bar' in content
        }
        
        print("✅ Executor Public Interface Elements:")
        for element, present in checks.items():
            status = "✅" if present else "❌"
            print(f"   {status} {element}")
        
        return all(checks.values())
    else:
        print(f"❌ Failed to load Executor Public interface: {response.status_code}")
        return False

def test_executor_private_interface():
    """Test Executor Private interface"""
    print("\n🔒 TESTING EXECUTOR PRIVATE INTERFACE")
    print("=" * 60)
    
    session = requests.Session()
    session.post('http://127.0.0.1:5001/login', data={'username': 'executor_private', 'password': 'Shiva@123'})
    
    response = session.get('http://127.0.0.1:5001/executive-private')
    if response.status_code == 200:
        content = response.text
        
        checks = {
            'Executive Private Dashboard': 'Executive Private Dashboard' in content,
            'Private Folder Navigation': 'Private Folder Structure' in content,
            'Private Audio Extraction': 'Private Audio Extraction' in content,
            'Private Access Badges': 'PRIVATE ACCESS' in content,
            'Metadata Form': 'metadataForm' in content,
            'VLC Integration': 'Open in VLC' in content or 'vlc' in content.lower()
        }
        
        print("✅ Executor Private Interface Elements:")
        for element, present in checks.items():
            status = "✅" if present else "❌"
            print(f"   {status} {element}")
        
        return all(checks.values())
    else:
        print(f"❌ Failed to load Executor Private interface: {response.status_code}")
        return False

def test_cross_checker_interface():
    """Test Cross Checker interface"""
    print("\n✅ TESTING CROSS CHECKER INTERFACE")
    print("=" * 60)
    
    session = requests.Session()
    session.post('http://127.0.0.1:5001/login', data={'username': 'crosschecker', 'password': 'Shiva@123'})
    
    response = session.get('http://127.0.0.1:5001/cross-checker')
    if response.status_code == 200:
        content = response.text
        
        checks = {
            'Cross Check Folders': 'Cross Check Folders' in content,
            'Pending Folders Table': 'Pending Folders for Review' in content,
            'Folder-based Workflow': 'folder-row-' in content or 'folder_name' in content,
            'Validation Actions': 'Approve & Validate' in content,
            'VLC Preview': 'Preview VLC' in content or 'preview' in content.lower(),
            'Audio Playback': 'Play Audio' in content or 'audio' in content.lower()
        }
        
        print("✅ Cross Checker Interface Elements:")
        for element, present in checks.items():
            status = "✅" if present else "❌"
            print(f"   {status} {element}")
        
        return all(checks.values())
    else:
        print(f"❌ Failed to load Cross Checker interface: {response.status_code}")
        return False

def test_admin_dashboard():
    """Test Admin Dashboard"""
    print("\n👑 TESTING ADMIN DASHBOARD")
    print("=" * 60)
    
    session = requests.Session()
    session.post('http://127.0.0.1:5001/login', data={'username': 'admin', 'password': 'Shiva@123'})
    
    response = session.get('http://127.0.0.1:5001/admin')
    if response.status_code == 200:
        content = response.text
        
        checks = {
            'Admin Dashboard': 'Admin Dashboard' in content,
            'Recent Operations': 'Recent Operations' in content,
            'System Statistics': 'statistics' in content.lower() or 'stats' in content.lower(),
            'User Management': 'User Management' in content or 'users' in content.lower(),
            'Activity Logs': 'activity' in content.lower() or 'logs' in content.lower()
        }
        
        print("✅ Admin Dashboard Elements:")
        for element, present in checks.items():
            status = "✅" if present else "❌"
            print(f"   {status} {element}")
        
        return all(checks.values())
    else:
        print(f"❌ Failed to load Admin Dashboard: {response.status_code}")
        return False

def test_api_endpoints():
    """Test key API endpoints"""
    print("\n🔌 TESTING API ENDPOINTS")
    print("=" * 60)
    
    session = requests.Session()
    session.post('http://127.0.0.1:5001/login', data={'username': 'assigner', 'password': 'Shiva@123'})
    
    endpoints = [
        ('/api/get-folders', 'GET'),
        ('/api/queue-status', 'GET'),
        ('/api/clear-queue', 'POST'),
    ]
    
    results = {}
    for endpoint, method in endpoints:
        try:
            if method == 'GET':
                response = session.get(f'http://127.0.0.1:5001{endpoint}')
            else:
                response = session.post(f'http://127.0.0.1:5001{endpoint}')
            
            results[endpoint] = response.status_code == 200
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {endpoint} ({method}): {response.status_code}")
        except Exception as e:
            results[endpoint] = False
            print(f"   ❌ {endpoint} ({method}): Error - {e}")
    
    return all(results.values())

def generate_browser_test_report(results):
    """Generate browser test report"""
    print("\n" + "=" * 80)
    print("🌐 BROWSER UI TEST REPORT")
    print("=" * 80)
    
    interface_names = [
        'Assigner Interface',
        'Executor Public Interface', 
        'Executor Private Interface',
        'Cross Checker Interface',
        'Admin Dashboard',
        'API Endpoints'
    ]
    
    successful_interfaces = sum(1 for result in results if result)
    total_interfaces = len(results)
    
    print("🖥️ INTERFACE TEST RESULTS:")
    for i, (interface, result) in enumerate(zip(interface_names, results)):
        status = "✅" if result else "❌"
        print(f"   {status} {interface}")
    
    success_rate = successful_interfaces / total_interfaces * 100
    print(f"\n   📈 UI Success Rate: {successful_interfaces}/{total_interfaces} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 UI STATUS: EXCELLENT - All interfaces working perfectly!")
        status_emoji = "🎉"
    elif success_rate >= 75:
        print("✅ UI STATUS: GOOD - Most interfaces working")
        status_emoji = "✅"
    else:
        print("⚠️ UI STATUS: NEEDS ATTENTION - Some interfaces have issues")
        status_emoji = "⚠️"
    
    print(f"\n{status_emoji} BROWSER UI TESTING COMPLETE!")
    
    print("\n🎯 RECOMMENDED BROWSER TESTING STEPS:")
    print("1. 🔐 Login with each role (assigner, executor_public, executor_private, crosschecker, admin)")
    print("2. 📋 Test Assigner: Select folders, assign categories, add to queue")
    print("3. 🎬 Test Executor Public: Navigate folder tree, preview videos, fill metadata")
    print("4. 🔒 Test Executor Private: Same as public but with private access")
    print("5. ✅ Test Cross Checker: Review folders, validate, approve")
    print("6. 👑 Test Admin: View dashboard, check recent operations")
    print("7. 🔄 Test End-to-End: Complete workflow from assignment to validation")
    
    return success_rate

def main():
    """Run browser UI tests"""
    print("🌐 COMPREHENSIVE BROWSER UI TESTING")
    print("=" * 80)
    print(f"🕐 Test Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Test all interfaces
    results = [
        test_assigner_interface(),
        test_executor_public_interface(),
        test_executor_private_interface(),
        test_cross_checker_interface(),
        test_admin_dashboard(),
        test_api_endpoints()
    ]
    
    # Generate report
    success_rate = generate_browser_test_report(results)
    
    return success_rate

if __name__ == "__main__":
    main()
