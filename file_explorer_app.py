#!/usr/bin/env python3
"""
COMPLETE BROWSER-BASED FILE EXPLORER
Recursive folder tree viewer with VLC integration
"""

from flask import Flask, render_template, jsonify, request
import os
import subprocess
import json
import platform
from pathlib import Path

app = Flask(__name__)

# Configuration
MEDIA_EXTENSIONS = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.m4a', '.aac', '.ogg'}
VLC_PATHS = {
    'Windows': [
        r'C:\Program Files\VideoLAN\VLC\vlc.exe',
        r'C:\Program Files (x86)\VideoLAN\VLC\vlc.exe'
    ],
    'Darwin': ['/Applications/VLC.app/Contents/MacOS/VLC'],
    'Linux': ['vlc', '/usr/bin/vlc', '/snap/bin/vlc']
}

def find_vlc_executable():
    """Find VLC executable on the system"""
    system = platform.system()
    paths = VLC_PATHS.get(system, [])
    
    for path in paths:
        if os.path.exists(path):
            return path
    
    # Try which/where command
    try:
        if system == 'Windows':
            result = subprocess.run(['where', 'vlc'], capture_output=True, text=True)
        else:
            result = subprocess.run(['which', 'vlc'], capture_output=True, text=True)
        
        if result.returncode == 0:
            return result.stdout.strip()
    except:
        pass
    
    return None

def get_file_size(file_path):
    """Get human-readable file size"""
    try:
        size = os.path.getsize(file_path)
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    except:
        return "Unknown"

def is_media_file(filename):
    """Check if file is a media file"""
    return Path(filename).suffix.lower() in MEDIA_EXTENSIONS

def scan_directory_recursive(directory_path, max_depth=10, current_depth=0):
    """
    Recursively scan directory and return tree structure
    """
    if current_depth >= max_depth:
        return []
    
    items = []
    
    try:
        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            return items
        
        # Get all items in directory
        all_items = []
        try:
            all_items = os.listdir(directory_path)
        except PermissionError:
            return items
        
        # Separate folders and files
        folders = []
        files = []
        
        for item in all_items:
            item_path = os.path.join(directory_path, item)
            
            try:
                if os.path.isdir(item_path):
                    folders.append(item)
                elif os.path.isfile(item_path):
                    files.append(item)
            except (PermissionError, OSError):
                continue
        
        # Process folders first
        for folder in sorted(folders, key=str.lower):
            folder_path = os.path.join(directory_path, folder)
            
            # Count media files in this folder (recursive)
            media_count = count_media_files(folder_path)
            
            # Get children recursively
            children = scan_directory_recursive(folder_path, max_depth, current_depth + 1)
            
            folder_item = {
                'name': folder,
                'path': folder_path,
                'type': 'folder',
                'media_count': media_count,
                'children': children,
                'expanded': False
            }
            items.append(folder_item)
        
        # Process files
        for file in sorted(files, key=str.lower):
            file_path = os.path.join(directory_path, file)
            
            file_item = {
                'name': file,
                'path': file_path,
                'type': 'file',
                'size': get_file_size(file_path),
                'is_media': is_media_file(file),
                'extension': Path(file).suffix.lower()
            }
            items.append(file_item)
            
    except Exception as e:
        print(f"Error scanning directory {directory_path}: {e}")
    
    return items

def count_media_files(directory_path):
    """Count media files in directory recursively"""
    count = 0
    try:
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if is_media_file(file):
                    count += 1
    except:
        pass
    return count

@app.route('/')
def index():
    """Main file explorer page"""
    return render_template('file_explorer.html')

@app.route('/api/scan-directory', methods=['POST'])
def scan_directory():
    """API endpoint to scan a directory"""
    try:
        data = request.get_json()
        directory_path = data.get('path', '')
        
        if not directory_path:
            return jsonify({
                'success': False,
                'error': 'No directory path provided'
            })
        
        if not os.path.exists(directory_path):
            return jsonify({
                'success': False,
                'error': f'Directory does not exist: {directory_path}'
            })
        
        if not os.path.isdir(directory_path):
            return jsonify({
                'success': False,
                'error': f'Path is not a directory: {directory_path}'
            })
        
        # Scan directory
        tree_data = scan_directory_recursive(directory_path)
        
        return jsonify({
            'success': True,
            'path': directory_path,
            'tree': tree_data,
            'total_items': len(tree_data)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/open-vlc', methods=['POST'])
def open_vlc():
    """API endpoint to open file in VLC"""
    try:
        data = request.get_json()
        file_path = data.get('path', '')
        
        if not file_path:
            return jsonify({
                'success': False,
                'error': 'No file path provided'
            })
        
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': f'File does not exist: {file_path}'
            })
        
        # Find VLC executable
        vlc_path = find_vlc_executable()
        if not vlc_path:
            return jsonify({
                'success': False,
                'error': 'VLC not found on system'
            })
        
        # Launch VLC with the file
        try:
            if platform.system() == 'Windows':
                subprocess.Popen([vlc_path, file_path], shell=False)
            else:
                subprocess.Popen([vlc_path, file_path])
            
            return jsonify({
                'success': True,
                'message': f'Opened {os.path.basename(file_path)} in VLC',
                'vlc_path': vlc_path,
                'file_path': file_path
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Failed to launch VLC: {str(e)}'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/get-drives', methods=['GET'])
def get_drives():
    """Get available drives (Windows) or root directories (Unix)"""
    try:
        drives = []
        
        if platform.system() == 'Windows':
            # Get Windows drives
            import string
            for letter in string.ascii_uppercase:
                drive = f"{letter}:\\"
                if os.path.exists(drive):
                    drives.append({
                        'name': f"Drive {letter}:",
                        'path': drive
                    })
        else:
            # Unix-like systems
            drives = [
                {'name': 'Root (/)', 'path': '/'},
                {'name': 'Home', 'path': os.path.expanduser('~')},
            ]
        
        return jsonify({
            'success': True,
            'drives': drives
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5002)
