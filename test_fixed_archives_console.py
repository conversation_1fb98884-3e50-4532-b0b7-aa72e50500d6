#!/usr/bin/env python3
"""
TEST FIXED ARCHIVES ASSIGNMENT CONSOLE
Test that the Archives Assignment Console now writes to columns A-F correctly
"""

import requests
import json
from datetime import datetime
import time

def test_fixed_archives_console():
    print("🎉 TESTING FIXED ARCHIVES ASSIGNMENT CONSOLE")
    print("="*70)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Step 1: Login as Assigner
    print("\n📋 STEP 1: Login as Assigner")
    print("-" * 40)
    
    login_data = {'username': 'assigner', 'password': '<PERSON>@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Assigner login failed: {login_response.status_code}")
        return
    
    print("✅ Logged in as Assigner")
    
    # Step 2: Test the fixed Google Sheets API directly
    print("\n🔧 STEP 2: Testing fixed Google Sheets API")
    print("-" * 40)
    
    timestamp = datetime.now().strftime("%H%M%S")
    test_data = {
        'folder_name': f'ARCHIVES_FIXED_{timestamp}',
        'date_processed': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'moved_to_folder': 'Internal video without stems',
        'social_media_url': f'https://archives-fixed-{timestamp}.com',
        'assigned_to': 'Executor Public',
        'remarks': f'ARCHIVES CONSOLE FIXED TEST - COLUMNS A-F - {timestamp}'
    }
    
    print(f"📝 Test data (should go to columns A-F):")
    print(f"   📊 Column A: {test_data['folder_name']}")
    print(f"   📊 Column B: {test_data['date_processed']}")
    print(f"   📊 Column C: {test_data['moved_to_folder']}")
    print(f"   📊 Column D: {test_data['social_media_url']}")
    print(f"   📊 Column E: {test_data['assigned_to']}")
    print(f"   📊 Column F: {test_data['remarks']}")
    
    # Test the fixed Google Sheets function
    response = session.post(f"{base_url}/api/test-google-sheets", json=test_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"\n✅ Fixed API Response: {result.get('success', False)}")
        print(f"✅ Message: {result.get('message', 'No message')}")
        
        if result.get('success'):
            row_number = result.get('row_number', 'Unknown')
            print(f"✅ Row Number: {row_number}")
            print(f"✅ Data should now be in columns A-F of row {row_number}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
    else:
        print(f"❌ API call failed: {response.status_code}")
        print(f"❌ Response: {response.text}")
        return
    
    # Step 3: Test the complete Archives Console workflow
    print(f"\n🎬 STEP 3: Testing complete Archives Console workflow")
    print("-" * 40)
    
    # Get folders
    folders_response = session.get(f"{base_url}/api/get-folders")
    if folders_response.status_code != 200:
        print(f"❌ Folders API failed: {folders_response.status_code}")
        return
    
    folders_data = folders_response.json()
    if not folders_data.get('success') or not folders_data.get('folders'):
        print("❌ No folders available")
        return
    
    # Use first folder
    test_folder = folders_data['folders'][0]
    folder_path = test_folder['path']
    folder_name = test_folder['name']
    
    print(f"✅ Using test folder: {folder_name}")
    
    # Add to queue
    workflow_timestamp = datetime.now().strftime("%H%M%S")
    queue_data = {
        'folder_paths': [folder_path],
        'category': 'Internal video without stems',
        'assign_to': 'Executor Public',
        'video_ids': f'WORKFLOW_FIXED_{workflow_timestamp}',
        'url': f'https://workflow-fixed-{workflow_timestamp}.com',
        'remarks': f'COMPLETE WORKFLOW TEST - FIXED COLUMNS A-F - {workflow_timestamp}'
    }
    
    print(f"📝 Workflow test data:")
    print(f"   📁 Folder: {folder_name}")
    print(f"   📂 Category: {queue_data['category']}")
    print(f"   👤 Assign to: {queue_data['assign_to']}")
    print(f"   🆔 Video IDs: {queue_data['video_ids']}")
    print(f"   🔗 URL: {queue_data['url']}")
    print(f"   📝 Remarks: {queue_data['remarks']}")
    
    # Add to queue
    add_response = session.post(f"{base_url}/api/add-to-queue", json=queue_data)
    if add_response.status_code != 200:
        print(f"❌ Add to queue failed: {add_response.status_code}")
        return
    
    add_result = add_response.json()
    print(f"✅ Added to queue: {add_result.get('success', False)}")
    print(f"✅ Added {add_result.get('added', 0)} items")
    
    # Process queue (this should now use the fixed Google Sheets function)
    print(f"\n🚀 STEP 4: Processing queue with fixed Google Sheets mapping")
    print("-" * 40)
    
    process_response = session.post(f"{base_url}/api/process-queue")
    
    if process_response.status_code == 200:
        process_result = process_response.json()
        print(f"✅ Queue processed: {process_result.get('success', False)}")
        print(f"✅ Processed {process_result.get('processed', 0)} items")
        print(f"❌ Failed {process_result.get('failed', 0)} items")
        
        if process_result.get('processed_items'):
            for item in process_result['processed_items']:
                print(f"   📁 Processed: {item.get('folder_name')}")
                print(f"   📂 Category: {item.get('category')}")
                print(f"   🔄 Operation: {item.get('operation')}")
    else:
        print(f"❌ Process queue failed: {process_response.status_code}")
        print(f"❌ Response: {process_response.text}")
    
    # Summary
    print(f"\n🎯 VERIFICATION STEPS:")
    print("="*70)
    print(f"1. Open Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print(f"2. Go to 'Records' sheet")
    print(f"3. Look for these test entries:")
    print(f"   📊 Direct API Test: 'ARCHIVES_FIXED_{timestamp}'")
    print(f"   📊 Workflow Test: '{folder_name}' with Video IDs 'WORKFLOW_FIXED_{workflow_timestamp}'")
    print(f"4. Verify data is in columns A-F:")
    print(f"   ✅ Column A: Folder name")
    print(f"   ✅ Column B: Date processed")
    print(f"   ✅ Column C: Category")
    print(f"   ✅ Column D: URL")
    print(f"   ✅ Column E: Assigned to")
    print(f"   ✅ Column F: Remarks with Video IDs")
    print(f"5. Verify NO data in columns M-R for these entries")
    
    print(f"\n🎉 EXPECTED RESULTS:")
    print("="*70)
    print(f"✅ Archives Assignment Console now writes to columns A-F")
    print(f"✅ NO more data appearing in columns M-R")
    print(f"✅ Fixed Google Sheets column mapping issue")
    print(f"✅ Complete workflow working correctly")
    
    print(f"\n🔧 TECHNICAL FIX SUMMARY:")
    print(f"   ❌ BEFORE: append_row() → Columns M-R")
    print(f"   ✅ AFTER: sheet.update(range='A:F') → Columns A-F")
    print(f"   🔧 ROOT CAUSE: Existing headers caused append_row to start from column M")
    print(f"   🔧 SOLUTION: Use specific range A:F with sheet.update()")
    
    print("="*70)
    print("🎉 ARCHIVES ASSIGNMENT CONSOLE - GOOGLE SHEETS MAPPING FIXED!")
    print("="*70)

if __name__ == "__main__":
    test_fixed_archives_console()
