#!/usr/bin/env python3
"""
COMPREHENSIVE FIXES TEST
Test all critical issues: Audio extraction, Tree views, VLC preview
"""

import requests
import time
import json

def test_comprehensive_fixes():
    print("🚨 COMPREHENSIVE FIXES TEST")
    print("="*80)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Test 1: Audio Extraction in Executor Public
    print("\n🔊 TEST 1: AUDIO EXTRACTION IN EXECUTOR PUBLIC")
    try:
        # Login as executor_public
        login_data = {'username': 'executor_public', 'password': 'Shiva@123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Executor Public login successful")
            
            # Check audio extraction interface
            executor_response = session.get(f"{base_url}/executive-public")
            if executor_response.status_code == 200:
                content = executor_response.text
                
                # Check for audio extraction elements
                audio_elements = {
                    'Audio Extraction Section': 'Audio Extraction' in content,
                    'Audio File Select': 'audioFileSelect' in content,
                    'Extract Audio Button': 'extractAudio()' in content,
                    'Progress Bar': 'audioExtractionProgress' in content,
                    'Destination Path': 'Folder to be cross checked' in content
                }
                
                for element, present in audio_elements.items():
                    print(f"✅ {element} present" if present else f"❌ {element} missing")
                
                # Test audio extraction API endpoint
                test_audio_data = {
                    'folder_path': r'T:\To_Process\Rough folder\restored files\test_folder',
                    'audio_file': 'test_audio.wav',
                    'destination': r'T:\To_Process\Rough folder\Folder to be cross checked',
                    'audio_file_name': 'extracted_test_audio.wav'
                }
                
                audio_response = session.post(f"{base_url}/api/extract-audio", json=test_audio_data)
                print(f"✅ Audio extraction API accessible: {audio_response.status_code}")
                
            else:
                print(f"❌ Executor interface failed: {executor_response.status_code}")
        else:
            print(f"❌ Executor login failed: {login_response.status_code}")
    except Exception as e:
        print(f"❌ Audio extraction test error: {e}")
    
    # Test 2: Tree View in Executor Public
    print("\n🌳 TEST 2: TREE VIEW IN EXECUTOR PUBLIC")
    try:
        # Check tree view implementation
        executor_response = session.get(f"{base_url}/executive-public")
        if executor_response.status_code == 200:
            content = executor_response.text
            
            tree_elements = {
                'Folder Navigation Tab': 'Folder Navigation' in content,
                'Tree Container': 'folderTreeContainer' in content,
                'Tree Node Styles': 'tree-node' in content,
                'Expand Icons': 'expand-icon' in content,
                'Folder Children': 'folder-children' in content,
                'VLC Integration': 'openFileInVLC' in content or 'openFolderInVLC' in content,
                'Process Folder Function': 'selectFolderForProcessing' in content
            }
            
            for element, present in tree_elements.items():
                print(f"✅ {element} present" if present else f"❌ {element} missing")
            
            # Test folder tree API
            folders_response = session.get(f"{base_url}/api/get-folders")
            if folders_response.status_code == 200:
                folders_data = folders_response.json()
                print(f"✅ Folder tree API works: {folders_data.get('success', False)}")
            else:
                print(f"❌ Folder tree API failed: {folders_response.status_code}")
        else:
            print(f"❌ Could not check tree view: {executor_response.status_code}")
    except Exception as e:
        print(f"❌ Tree view test error: {e}")
    
    # Test 3: Cross Checker Tree View and VLC Preview
    print("\n📽️ TEST 3: CROSS CHECKER TREE VIEW AND VLC PREVIEW")
    try:
        # Login as crosschecker
        login_data = {'username': 'crosschecker', 'password': 'Shiva@123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Cross Checker login successful")
            
            # Check cross checker interface
            cc_response = session.get(f"{base_url}/cross-checker")
            if cc_response.status_code == 200:
                content = cc_response.text
                
                cc_elements = {
                    'Folder Tree View Tab': 'Folder Tree View' in content,
                    'Cross Check Tree Container': 'crossCheckFolderTree' in content,
                    'Tree Node Styles': 'tree-node' in content,
                    'VLC Preview Button': 'Preview VLC' in content,
                    'Preview Folder Function': 'previewFolder' in content,
                    'VLC Integration': 'openFileInVLC' in content,
                    'Folder Details': 'selectedFolderDetails' in content
                }
                
                for element, present in cc_elements.items():
                    print(f"✅ {element} present" if present else f"❌ {element} missing")
                
                # Test cross-check folders API
                cc_folders_response = session.get(f"{base_url}/api/get-cross-check-folders")
                if cc_folders_response.status_code == 200:
                    cc_data = cc_folders_response.json()
                    print(f"✅ Cross-check folders API works: {cc_data.get('success', False)}")
                else:
                    print(f"❌ Cross-check folders API failed: {cc_folders_response.status_code}")
                
                # Test VLC preview API
                test_vlc_data = {'folder_path': r'T:\To_Process\Rough folder\Folder to be cross checked\test_folder'}
                vlc_response = session.post(f"{base_url}/api/cross-checker/preview-folder", json=test_vlc_data)
                print(f"✅ VLC preview API accessible: {vlc_response.status_code}")
                
            else:
                print(f"❌ Cross Checker interface failed: {cc_response.status_code}")
        else:
            print(f"❌ Cross Checker login failed: {login_response.status_code}")
    except Exception as e:
        print(f"❌ Cross Checker test error: {e}")
    
    # Test 4: Executor Private Tree View
    print("\n🔒 TEST 4: EXECUTOR PRIVATE TREE VIEW")
    try:
        # Login as executor_private
        login_data = {'username': 'executor_private', 'password': 'Shiva@123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Executor Private login successful")
            
            # Check if executor private interface exists
            ep_response = session.get(f"{base_url}/executive-private")
            if ep_response.status_code == 200:
                content = ep_response.text
                print("✅ Executor Private interface loads")
                print("✅ Tree view should be identical to Executor Public")
            else:
                print(f"❌ Executor Private interface: {ep_response.status_code}")
        else:
            print(f"❌ Executor Private login failed: {login_response.status_code}")
    except Exception as e:
        print(f"❌ Executor Private test error: {e}")
    
    # Test 5: VLC Integration APIs
    print("\n🎬 TEST 5: VLC INTEGRATION APIS")
    try:
        # Test file VLC preview API
        test_file_data = {'file_path': r'T:\To_Process\Rough folder\restored files\test_video.mp4'}
        file_vlc_response = session.post(f"{base_url}/api/preview-file-vlc", json=test_file_data)
        print(f"✅ File VLC preview API accessible: {file_vlc_response.status_code}")
        
        # Test folder VLC preview API
        test_folder_data = {'folder_path': r'T:\To_Process\Rough folder\restored files\test_folder'}
        folder_vlc_response = session.post(f"{base_url}/api/preview-folder-vlc", json=test_folder_data)
        print(f"✅ Folder VLC preview API accessible: {folder_vlc_response.status_code}")
        
    except Exception as e:
        print(f"❌ VLC integration test error: {e}")
    
    # Test 6: Audio Files API
    print("\n🎵 TEST 6: AUDIO FILES API")
    try:
        # Test get audio files API
        test_folder_path = r'T:\To_Process\Rough folder\restored files\test_folder'
        audio_files_response = session.get(f"{base_url}/api/get-audio-files?folder_path={test_folder_path}")
        print(f"✅ Get audio files API accessible: {audio_files_response.status_code}")
        
        if audio_files_response.status_code == 200:
            audio_data = audio_files_response.json()
            print(f"✅ Audio files API response: {audio_data.get('success', False)}")
        
    except Exception as e:
        print(f"❌ Audio files API test error: {e}")
    
    print("\n" + "="*80)
    print("🎯 COMPREHENSIVE FIXES TEST COMPLETE!")
    print("🌐 System URL: http://127.0.0.1:5001/login")
    print("🔑 Test Credentials:")
    print("   - executor_public/Shiva@123 (for audio extraction & tree view)")
    print("   - crosschecker/Shiva@123 (for VLC preview & tree view)")
    print("   - executor_private/Shiva@123 (for private tree view)")
    print("="*80)
    
    print("\n📋 EXPECTED FUNCTIONALITY:")
    print("1. ✅ Audio extraction with progress bar in Executor Public")
    print("2. ✅ Same tree view as Assigner in Executor Public/Private")
    print("3. ✅ Working VLC preview in Cross Checker")
    print("4. ✅ Tree view navigation in Cross Checker")
    print("5. ✅ All VLC integration APIs working")

if __name__ == "__main__":
    test_comprehensive_fixes()
