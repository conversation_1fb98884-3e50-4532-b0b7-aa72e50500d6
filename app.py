from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_file, Response
import os
import shutil
import json
import urllib.parse
from pathlib import Path
from datetime import datetime
import sqlite3
import hashlib
import threading
import subprocess
import tempfile
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from functools import wraps
import mimetypes
import subprocess
import base64
try:
    import gspread
    from google.oauth2.service_account import Credentials
    GSPREAD_AVAILABLE = True
except ImportError:
    GSPREAD_AVAILABLE = False

try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False

try:
    import moviepy.editor as mp
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False

try:
    import vlc
    VLC_AVAILABLE = True
except ImportError:
    VLC_AVAILABLE = False

import threading
import uuid
import math
import urllib.parse
from contextlib import contextmanager
import time
import re
import logging

app = Flask(__name__)
app.secret_key = 'archives-department-secure-key-2024'

# Configuration
class Config:
    SOURCE_PATH = r"T:\To_Process\Rough folder\restored files"
    DEST_PATH = r"T:\To_Process\Rough folder"
    DATABASE_PATH = "archives_management.db"
    GOOGLE_SHEET_ID = "13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4"
    GOOGLE_SHEET_NAME = "Records"
    
    PROCESSING_CATEGORIES = [
        "Internal video with stems",
        "Internal video without stems",
        "Miscellaneous",
        "Internal with Project File",
        "Internal Only Output",
        "Social Media Only Output",
        "Internal with Project Files",
        "Private one video",
        "Social media outputs with stems",
        "Social media outputs without stems",
        "Tamil files",
        "To Be Deleted"
    ]
    
    SOCIAL_MEDIA_CATEGORIES = [
        "Social media outputs with stems",
        "Social media outputs without stems"
    ]
    
    # Removed MULTIPLE_OUTPUT_CATEGORIES as per requirements
    
    USER_ROLES = {
        'Assigner': 'Assigns files to processing categories',
        'Cross Checker': 'Reviews and validates assigned files',
        'Executor Public': 'Processes public files',
        'Executor Private': 'Processes private files',
        'Main Admin': 'System administration and user management'
    }

    ASSIGNABLE_USERS = ['Executor Public', 'Executor Private']

    # Enhanced metadata options
    VIDEO_TYPES = [
        'Talk', 'Documentary', 'Insta-Reels', 'Class', 'Interview',
        'Promo', 'Daily Mystic Quote', 'Episode', 'Q&A', 'Message',
        'Intro', 'Song', 'Glimpses', 'Animation', 'Sharings',
        'Video-Book', 'Teaser', 'Poem', 'Telefilm', 'Non-Ashram Videos',
        'Event', 'Miscellaneous'
    ]

    LANGUAGES = [
        'English', 'Tamil', 'Hindi', 'Telugu', 'Kannada', 'Malayalam',
        'Bengali', 'Gujarati', 'Marathi', 'Punjabi', 'Urdu', 'Sanskrit'
    ]

    CONTENT_TAGS = [
        'Career-Success', 'Yoga-Spirituality', 'Celebrity',
        'Conscious Planet', 'Culture-Tradition', 'Health-Fitness',
        'Isha-Related', 'Lifestyle', 'Miscellaneous',
        'Personal-Wellbeing', 'Relationship-Sexuality',
        'Sadhguru-Related', 'Social-Issues'
    ]

    BACKUP_TYPES = [
        'Stems', 'Consolidated', 'Unconsolidated', 'Premiere Pro',
        'MP4', 'Audio', 'AVI'
    ]

    PUBLISHED_PLATFORMS = [
        'Instagram', 'YouTube', 'Twitter', 'WhatsApp', 'Not Applicable'
    ]

    DEPARTMENTS = [
        'Marketing', 'HR', 'IT', 'Sales', 'Production', 'Legal',
        'Finance', 'Archives', 'Media', 'Content'
    ]

    CROSSCHECK_PATH = r"T:\To_Process\Rough folder\To Crosscheck"

# Database connection management with proper locking and retry logic
@contextmanager
def get_db_connection(timeout=30, retries=3):
    """
    Context manager for database connections with automatic closing and retry logic.
    Fixes the "database is locked" error by properly managing connections.
    """
    conn = None
    for attempt in range(retries):
        try:
            # Set timeout for database operations
            conn = sqlite3.connect(Config.DATABASE_PATH, timeout=timeout)
            # Enable WAL mode for better concurrency
            conn.execute('PRAGMA journal_mode=WAL')
            # Set busy timeout
            conn.execute(f'PRAGMA busy_timeout={timeout * 1000}')
            yield conn
            break
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e).lower() and attempt < retries - 1:
                print(f"🔄 Database locked, retrying... (attempt {attempt + 1}/{retries})")
                time.sleep(0.5 * (attempt + 1))  # Exponential backoff
                if conn:
                    try:
                        conn.close()
                    except:
                        pass
                    conn = None
                continue
            else:
                if conn:
                    try:
                        conn.close()
                    except:
                        pass
                raise
        except Exception as e:
            if conn:
                try:
                    conn.close()
                except:
                    pass
            raise
    else:
        # This runs if the loop completed without breaking (all retries failed)
        if conn:
            try:
                conn.close()
            except:
                pass
        raise sqlite3.OperationalError("Database connection failed after all retries")

    # Close connection when exiting context
    try:
        if conn:
            conn.close()
    except:
        pass

# Database migration for existing installations
def migrate_database():
    """Migrate existing database to new schema"""
    try:
        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        # Check if email column exists
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add missing columns to users table
        new_columns = [
            ('email', 'TEXT'),
            ('last_ip', 'TEXT'),
            ('session_token', 'TEXT'),
            ('preferences', 'TEXT'),
            ('last_folder_view', 'TEXT'),
            ('failed_login_attempts', 'INTEGER DEFAULT 0'),
            ('locked_until', 'TIMESTAMP'),
            ('password_changed_at', 'TIMESTAMP')
        ]

        for column_name, column_type in new_columns:
            if column_name not in columns:
                try:
                    cursor.execute(f'ALTER TABLE users ADD COLUMN {column_name} {column_type}')
                    print(f"✅ Added column {column_name} to users table")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e):
                        print(f"⚠️ Warning: Could not add column {column_name}: {e}")

        conn.commit()
        conn.close()
        print("✅ Database migration completed successfully")

    except Exception as e:
        print(f"❌ Database migration failed: {e}")

# Database initialization
def init_database():
    """Initialize database with proper connection management"""
    conn = None
    try:
        # Use direct connection for initialization
        conn = sqlite3.connect(Config.DATABASE_PATH, timeout=30)
        # Enable WAL mode for better concurrency
        conn.execute('PRAGMA journal_mode=WAL')
        # Set busy timeout
        conn.execute('PRAGMA busy_timeout=30000')

        cursor = conn.cursor()

        # Enhanced Users table with concurrent session support
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL,
            email TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            last_ip TEXT,
            session_token TEXT,
            preferences TEXT,
            last_folder_view TEXT,
            is_active BOOLEAN DEFAULT 1,
            failed_login_attempts INTEGER DEFAULT 0,
            locked_until TIMESTAMP,
            password_changed_at TIMESTAMP
        )
    ''')
    
        # Files table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_path TEXT UNIQUE NOT NULL,
            original_path TEXT NOT NULL,
            filename TEXT NOT NULL,
            file_size INTEGER,
            file_type TEXT,
            status TEXT DEFAULT 'Unassigned',
            assigned_category TEXT,
            assigned_by INTEGER,
            assigned_at TIMESTAMP,
            social_media_url TEXT,
            cross_checked_by INTEGER,
            cross_checked_at TIMESTAMP,
            cross_check_status TEXT,
            processed_by INTEGER,
            processed_at TIMESTAMP,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (assigned_by) REFERENCES users (id),
            FOREIGN KEY (cross_checked_by) REFERENCES users (id),
            FOREIGN KEY (processed_by) REFERENCES users (id)
        )
        ''')

        # Operations log table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS operations_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            operation_type TEXT NOT NULL,
            file_id INTEGER,
            file_path TEXT,
            details TEXT,
            status TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (file_id) REFERENCES files (id)
        )
        ''')

        # Queue items table for tracking processed folders
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS queue_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            folder_name TEXT NOT NULL,
            folder_path TEXT NOT NULL,
            original_path TEXT NOT NULL,
            category TEXT NOT NULL,
            assign_to TEXT NOT NULL,
            video_ids TEXT,
            url TEXT,
            remarks TEXT,
            operation_type TEXT DEFAULT 'move',
            status TEXT DEFAULT 'queued',
            assigned_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP,
            error TEXT,
            FOREIGN KEY (assigned_by) REFERENCES users (id)
        )
        ''')

        # Comprehensive metadata table for processed files
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS file_metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            queue_item_id INTEGER,
            folder_path TEXT NOT NULL,

            -- General Metadata
            ocd_vp_number TEXT,
            edited_file_name TEXT,
            language TEXT,
            edited_year INTEGER,
            trim_closed_date DATE,
            total_duration TEXT,
            video_type TEXT,
            edited_location TEXT,
            published_platforms TEXT,
            department_name TEXT,
            component TEXT,
            content_tags TEXT,
            backup_type TEXT,
            access_level TEXT,
            software_show_name TEXT,

            -- Auto-detected values
            file_count INTEGER,
            total_file_size BIGINT,
            detected_duration TEXT,
            detected_resolution TEXT,
            detected_format TEXT,

            -- Audio/Transcript Metadata
            audio_code TEXT,
            audio_file_name TEXT,
            transcription_file_name TEXT,
            transcription_done_date DATE,
            transcription_status TEXT DEFAULT 'Pending',

            -- Social Media Metadata
            published_date DATE,
            video_id TEXT,
            social_media_title TEXT,
            description TEXT,
            social_media_url TEXT,
            duration_category TEXT,

            -- Processing Information
            processed_by INTEGER,
            processing_notes TEXT,
            draft_saved BOOLEAN DEFAULT 0,
            processing_status TEXT DEFAULT 'Draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

            FOREIGN KEY (queue_item_id) REFERENCES queue_items (id),
            FOREIGN KEY (processed_by) REFERENCES users (id)
        )
        ''')

        # Active sessions table for concurrent user management
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS active_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            session_token TEXT UNIQUE NOT NULL,
            client_ip TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        # System cache table for performance optimization
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cache_key TEXT UNIQUE NOT NULL,
            cache_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            cache_type TEXT DEFAULT 'general'
        )
        ''')

        # User activity tracking for analytics
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_activity (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            activity_type TEXT NOT NULL,
            activity_data TEXT,
            ip_address TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        # Create default users if they don't exist
        default_users = [
        ('assigner', 'Shiva@123', 'Assigner'),
        ('crosschecker', 'Shiva@123', 'Cross Checker'),
        ('executor_public', 'Shiva@123', 'Executor Public'),
        ('executor_private', 'Shiva@123', 'Executor Private'),
        ('admin', 'Shiva@123', 'Main Admin')
        ]

        for username, password, role in default_users:
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            if not cursor.fetchone():
                password_hash = generate_password_hash(password)
                cursor.execute(
                    'INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)',
                    (username, password_hash, role)
                )

        conn.commit()
        print("✅ Database initialized successfully!")

    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        raise
    finally:
        if conn:
            conn.close()

    # Run database migration for existing installations
    migrate_database()

# Create directory structure
def create_directory_structure():
    """Create necessary directories"""
    try:
        # Create source directory if it doesn't exist
        os.makedirs(Config.SOURCE_PATH, exist_ok=True)
        
        # Create destination directory and subfolders
        os.makedirs(Config.DEST_PATH, exist_ok=True)
        
        for category in Config.PROCESSING_CATEGORIES:
            category_path = os.path.join(Config.DEST_PATH, category)
            os.makedirs(category_path, exist_ok=True)
            
            # No longer creating subfolders for multiple output categories
                
    except Exception as e:
        print(f"Error creating directories: {e}")

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def role_required(allowed_roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                # Check if this is an API request
                if request.path.startswith('/api/'):
                    return jsonify({'success': False, 'error': 'Authentication required'}), 401
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('login'))

            if session.get('role') not in allowed_roles:
                # Check if this is an API request
                if request.path.startswith('/api/'):
                    return jsonify({'success': False, 'error': 'Insufficient permissions'}), 403
                flash('You do not have permission to access this page.', 'error')
                return redirect(url_for('dashboard'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Utility functions
def log_operation(user_id, operation_type, file_id=None, file_path=None, details=None, status='completed'):
    """Log user operations"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO operations_log (user_id, operation_type, file_id, file_path, details, status)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (user_id, operation_type, file_id, file_path, details, status))
        conn.commit()

def get_file_info(file_path):
    """Get file information"""
    try:
        stat = os.stat(file_path)
        return {
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'type': mimetypes.guess_type(file_path)[0] or 'unknown'
        }
    except:
        return {'size': 0, 'modified': None, 'type': 'unknown'}

def fix_corrupted_path(corrupted_path):
    """Simple and reliable path fixing function"""
    print(f"🔧 Original path: {corrupted_path}")

    # If the path is already correct and exists, return it as-is
    if os.path.exists(corrupted_path):
        print(f"🔧 Path already exists, no fixing needed: {corrupted_path}")
        return corrupted_path

    # Start with the corrupted path
    fixed_path = corrupted_path

    # Normalize path separators
    fixed_path = fixed_path.replace('/', '\\')

    # Ensure proper drive letter format
    if not fixed_path.startswith('T:'):
        if fixed_path.startswith('\\'):
            fixed_path = 'T:' + fixed_path
        else:
            fixed_path = 'T:\\' + fixed_path

    # Fix common path corruption patterns
    fixed_path = fixed_path.replace('T:To_Process', 'T:\\To_Process')
    fixed_path = fixed_path.replace('T:\\To_ProcessRough', 'T:\\To_Process\\Rough')
    fixed_path = fixed_path.replace('T:\\To_Process\\RoughFolder', 'T:\\To_Process\\Rough folder')
    fixed_path = fixed_path.replace('T:\\To_Process\\Rough folderrestored', 'T:\\To_Process\\Rough folder\\restored')
    fixed_path = fixed_path.replace('estored files', 'restored files')

    # Clean up double backslashes
    while '\\\\' in fixed_path:
        fixed_path = fixed_path.replace('\\\\', '\\')

    # Verify the fixed path exists
    if os.path.exists(fixed_path):
        print(f"🔧 Successfully fixed path: {fixed_path}")
        return fixed_path
    else:
        print(f"🔧 Fixed path still doesn't exist: {fixed_path}")
        # Return the original path if fixing didn't work
        return corrupted_path

def scan_source_directory():
    """Scan source directory for video files and update database"""
    video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.mxf', '.m4v', '.3gp', '.f4v'}

    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            for root, dirs, files in os.walk(Config.SOURCE_PATH):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        file_path = os.path.join(root, file)

                        # Check if file already exists in database
                        cursor.execute('SELECT id FROM files WHERE file_path = ?', (file_path,))
                        if not cursor.fetchone():
                            file_info = get_file_info(file_path)
                            cursor.execute('''
                                INSERT INTO files (file_path, original_path, filename, file_size, file_type)
                                VALUES (?, ?, ?, ?, ?)
                            ''', (file_path, file_path, file, file_info['size'], file_info['type']))

            conn.commit()
    except Exception as e:
        print(f"Error scanning directory: {e}")

# Google Sheets integration
def log_to_google_sheets(folder_name, date_processed, moved_to_folder, social_media_url, assigned_to, remarks):
    """Log operations to Google Sheets with enhanced error handling"""
    if not GSPREAD_AVAILABLE:
        print("Google Sheets integration not available - gspread not installed")
        return {"success": False, "error": "gspread not available"}

    try:
        credentials_path = os.path.join(os.path.dirname(__file__), "credentials.json")

        if not os.path.exists(credentials_path):
            error_msg = f"Credentials file not found at: {credentials_path}"
            print(error_msg)
            return {"success": False, "error": error_msg}

        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/spreadsheets"
        ]

        creds = Credentials.from_service_account_file(credentials_path, scopes=scope)
        client = gspread.authorize(creds)

        # Open the Google Sheet
        sheet = client.open_by_key(Config.GOOGLE_SHEET_ID).worksheet(Config.GOOGLE_SHEET_NAME)

        # Append the row with operation data - COLUMNS A-F
        row_data = [
            folder_name,           # Column A
            date_processed,        # Column B
            moved_to_folder,       # Column C
            social_media_url or "", # Column D
            assigned_to or "",     # Column E
            remarks or ""          # Column F
        ]

        print(f"🔧 DEBUG: Writing to Google Sheets columns A-F:")
        print(f"   Column A (Folder): {row_data[0]}")
        print(f"   Column B (Date): {row_data[1]}")
        print(f"   Column C (Category): {row_data[2]}")
        print(f"   Column D (URL): {row_data[3]}")
        print(f"   Column E (Assigned): {row_data[4]}")
        print(f"   Column F (Remarks): {row_data[5]}")

        # FIXED: Use sheet.update() with specific range instead of append_row()
        # append_row() was writing to columns M-R due to existing headers
        # Get the next empty row
        all_values = sheet.get_all_values()
        next_row = len(all_values) + 1

        # Update specific range A:F to ensure correct column mapping
        range_name = f"A{next_row}:F{next_row}"
        result = sheet.update(values=[row_data], range_name=range_name)

        success_msg = f"Successfully logged to Google Sheets: {folder_name} (Row: {next_row}, Range: {range_name})"
        print(success_msg)
        print(f"🔧 FIXED: Data written to columns A-F using range {range_name}")

        return {"success": True, "message": success_msg, "row_data": row_data, "row_number": next_row}

    except Exception as e:
        error_msg = f"Error logging to Google Sheets: {str(e)}"
        print(error_msg)
        return {"success": False, "error": error_msg}

def update_metadata_in_google_sheets(folder_name, metadata):
    """Update metadata in Google Sheets by finding folder name in Column A and updating columns H onward"""
    if not GSPREAD_AVAILABLE:
        print("Google Sheets integration not available - gspread not installed")
        return {"success": False, "error": "gspread not available"}

    try:
        credentials_path = os.path.join(os.path.dirname(__file__), "credentials.json")

        if not os.path.exists(credentials_path):
            error_msg = f"Credentials file not found at: {credentials_path}"
            print(error_msg)
            return {"success": False, "error": error_msg}

        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/spreadsheets"
        ]

        creds = Credentials.from_service_account_file(credentials_path, scopes=scope)
        client = gspread.authorize(creds)

        # Open the specific Google Sheet and Records worksheet
        sheet_id = "13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4"
        sheet = client.open_by_key(sheet_id).worksheet("Records")

        # Find the row with the folder name in Column A
        try:
            cell = sheet.find(folder_name)
            row_number = cell.row
            print(f"Found folder '{folder_name}' in row {row_number}")
        except:
            print(f"Folder '{folder_name}' not found in Column A, adding new row")
            # If not found, append a new row
            row_number = len(sheet.get_all_values()) + 1
            sheet.update_cell(row_number, 1, folder_name)  # Column A

        # Update metadata in columns N onward (starting from column 14)
        # Column mapping based on metadata structure for Executor roles
        # FIXED: Changed from column 7 (G) to column 14 (N) as per requirements
        column_mapping = {
            14: metadata.get('ocd_vp_number', ''),           # N: OCD Number
            15: metadata.get('edited_file_name', ''),        # O: File Name
            16: metadata.get('total_file_size', ''),         # P: Size
            17: metadata.get('file_count', ''),              # Q: No. of Files
            18: metadata.get('detected_duration', ''),       # R: Duration
            19: metadata.get('language', ''),                # S: Language
            20: metadata.get('edited_year', ''),             # T: Year
            21: metadata.get('video_type', ''),              # U: Video Type
            22: metadata.get('edited_location', ''),         # V: Location
            23: metadata.get('published_platforms', ''),     # W: Platforms
            24: metadata.get('department_name', ''),         # X: Department
            25: metadata.get('component', ''),               # Y: Component
            26: metadata.get('content_tags', ''),            # Z: Content Tags
            27: metadata.get('backup_type', ''),             # AA: Backup Type
            28: metadata.get('access_level', ''),            # AB: Access Level
            29: metadata.get('software_show_name', ''),      # AC: Software Show Name
            30: metadata.get('audio_code', ''),              # AD: Audio Code
            31: metadata.get('audio_file_name', ''),         # AE: Audio File Name
            32: metadata.get('transcription_file_name', ''), # AF: Transcription File
            33: metadata.get('transcription_status', ''),    # AG: Transcription Status
            34: metadata.get('published_date', ''),          # AH: Published Date
            35: metadata.get('video_id', ''),                # AI: Video ID
            36: metadata.get('social_media_title', ''),      # AJ: Social Media Title
            37: metadata.get('description', ''),             # AK: Description
            38: metadata.get('social_media_url', ''),        # AL: Social Media URL
            39: metadata.get('duration_category', ''),       # AM: Duration Category
        }

        # Update all metadata columns
        for col, value in column_mapping.items():
            if value:  # Only update if value exists
                sheet.update_cell(row_number, col, str(value))

        success_msg = f"Successfully updated metadata in Google Sheets for '{folder_name}' in row {row_number}"
        print(success_msg)

        return {"success": True, "message": success_msg, "row_number": row_number}

    except Exception as e:
        error_msg = f"Error updating Google Sheets: {str(e)}"
        print(error_msg)
        return {"success": False, "error": error_msg}

def extract_and_move_audio_file(source_folder_path, destination_folder_path, audio_file_name):
    """Extract .wav audio file from Output folder and move to destination with new name"""
    try:
        # Look for Output folder inside the source folder
        output_folder = os.path.join(source_folder_path, "Output")

        if not os.path.exists(output_folder):
            print(f"Output folder not found in {source_folder_path}")
            return {"success": False, "error": "Output folder not found"}

        # Find .wav files in the Output folder
        wav_files = []
        for root, dirs, files in os.walk(output_folder):
            for file in files:
                if file.lower().endswith('.wav'):
                    wav_files.append(os.path.join(root, file))

        if not wav_files:
            print(f"No .wav files found in {output_folder}")
            return {"success": False, "error": "No .wav audio files found in Output folder"}

        # Use the first .wav file found
        source_audio_file = wav_files[0]

        # Ensure destination folder exists
        os.makedirs(destination_folder_path, exist_ok=True)

        # Create destination path with new audio file name
        if not audio_file_name.lower().endswith('.wav'):
            audio_file_name += '.wav'

        destination_audio_file = os.path.join(destination_folder_path, audio_file_name)

        # Copy the audio file to destination with new name
        import shutil
        shutil.copy2(source_audio_file, destination_audio_file)

        success_msg = f"Audio file extracted and moved: {os.path.basename(source_audio_file)} -> {audio_file_name}"
        print(success_msg)

        return {
            "success": True,
            "message": success_msg,
            "source_file": source_audio_file,
            "destination_file": destination_audio_file,
            "total_wav_files": len(wav_files)
        }

    except Exception as e:
        error_msg = f"Error extracting audio file: {str(e)}"
        print(error_msg)
        return {"success": False, "error": error_msg}

# Video transcription function
def transcribe_video(video_path):
    """Transcribe video using speech recognition"""
    if not MOVIEPY_AVAILABLE or not SPEECH_RECOGNITION_AVAILABLE:
        return "Transcription not available - required libraries not installed"

    try:
        # Extract audio from video
        video = mp.VideoFileClip(video_path)
        audio_path = f"temp_audio_{uuid.uuid4().hex}.wav"
        video.audio.write_audiofile(audio_path, verbose=False, logger=None)

        # Transcribe audio
        r = sr.Recognizer()
        with sr.AudioFile(audio_path) as source:
            audio = r.record(source)
            text = r.recognize_google(audio)

        # Clean up
        os.remove(audio_path)
        video.close()

        return text
    except Exception as e:
        return f"Transcription failed: {str(e)}"

def get_folder_size(folder_path):
    """Calculate folder size recursively"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    pass
    except:
        pass
    return total_size

# Global queue for batch processing
processing_queue = []
queue_lock = threading.Lock()

def get_video_metadata(file_path):
    """Extract video metadata using ffprobe if available"""
    try:
        import subprocess
        import json

        # Try to get video metadata using ffprobe
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', file_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            data = json.loads(result.stdout)

            # Extract video stream info
            video_stream = next((s for s in data.get('streams', []) if s.get('codec_type') == 'video'), None)
            format_info = data.get('format', {})

            metadata = {
                'duration': format_info.get('duration', 'Unknown'),
                'size': format_info.get('size', 0),
                'format': format_info.get('format_name', 'Unknown'),
                'resolution': 'Unknown',
                'codec': 'Unknown'
            }

            if video_stream:
                width = video_stream.get('width', 0)
                height = video_stream.get('height', 0)
                if width and height:
                    metadata['resolution'] = f"{width}x{height}"
                metadata['codec'] = video_stream.get('codec_name', 'Unknown')

            return metadata

    except Exception as e:
        print(f"Error extracting video metadata: {e}")

    # Fallback to basic file info
    file_info = get_file_info(file_path)
    return {
        'duration': 'Unknown',
        'size': file_info['size'],
        'format': file_info['type'],
        'resolution': 'Unknown',
        'codec': 'Unknown'
    }

def count_files_in_folder(folder_path):
    """Count total files in folder recursively"""
    count = 0
    try:
        for root, dirs, files in os.walk(folder_path):
            count += len(files)
    except Exception as e:
        print(f"Error counting files in {folder_path}: {e}")
    return count

def format_file_size(size_bytes):
    """Convert bytes to human readable format"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def generate_unique_codes():
    """Generate unique codes for OCD/VP and Audio"""
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')

    return {
        'ocd_vp_number': f"OCD-{datetime.now().year}-{timestamp[-6:]}",
        'audio_code': f"AUD-{datetime.now().year}-{timestamp[-6:]}",
        'video_id': f"VID-{datetime.now().year}-{timestamp[-6:]}"
    }

def save_metadata_draft(queue_item_id, metadata, user_id):
    """Save metadata as draft without processing"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Check if metadata already exists
            cursor.execute('SELECT id FROM file_metadata WHERE queue_item_id = ?', (queue_item_id,))
            existing = cursor.fetchone()

        if existing:
            # Update existing metadata
            cursor.execute('''
                UPDATE file_metadata SET
                    ocd_vp_number = ?, edited_file_name = ?, language = ?, edited_year = ?,
                    trim_closed_date = ?, total_duration = ?, video_type = ?, edited_location = ?,
                    published_platforms = ?, department_name = ?, component = ?, content_tags = ?,
                    backup_type = ?, access_level = ?, software_show_name = ?,
                    audio_code = ?, audio_file_name = ?, transcription_file_name = ?,
                    transcription_done_date = ?, published_date = ?, video_id = ?,
                    social_media_title = ?, description = ?, social_media_url = ?,
                    duration_category = ?, processing_notes = ?, draft_saved = 1,
                    updated_at = CURRENT_TIMESTAMP
                WHERE queue_item_id = ?
            ''', (
                metadata.get('ocd_vp_number'), metadata.get('edited_file_name'),
                metadata.get('language'), metadata.get('edited_year'),
                metadata.get('trim_closed_date'), metadata.get('total_duration'),
                metadata.get('video_type'), metadata.get('edited_location'),
                metadata.get('published_platforms'), metadata.get('department_name'),
                metadata.get('component'), metadata.get('content_tags'),
                metadata.get('backup_type'), metadata.get('access_level'),
                metadata.get('software_show_name'), metadata.get('audio_code'),
                metadata.get('audio_file_name'), metadata.get('transcription_file_name'),
                metadata.get('transcription_done_date'), metadata.get('published_date'),
                metadata.get('video_id'), metadata.get('social_media_title'),
                metadata.get('description'), metadata.get('social_media_url'),
                metadata.get('duration_category'), metadata.get('processing_notes'),
                queue_item_id
            ))
        else:
            # Insert new metadata
            cursor.execute('''
                INSERT INTO file_metadata (
                    queue_item_id, folder_path, ocd_vp_number, edited_file_name, language,
                    edited_year, trim_closed_date, total_duration, video_type, edited_location,
                    published_platforms, department_name, component, content_tags, backup_type,
                    access_level, software_show_name, audio_code, audio_file_name,
                    transcription_file_name, transcription_done_date, published_date, video_id,
                    social_media_title, description, social_media_url, duration_category,
                    processing_notes, processed_by, draft_saved
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
            ''', (
                queue_item_id, metadata.get('folder_path'),
                metadata.get('ocd_vp_number'), metadata.get('edited_file_name'),
                metadata.get('language'), metadata.get('edited_year'),
                metadata.get('trim_closed_date'), metadata.get('total_duration'),
                metadata.get('video_type'), metadata.get('edited_location'),
                metadata.get('published_platforms'), metadata.get('department_name'),
                metadata.get('component'), metadata.get('content_tags'),
                metadata.get('backup_type'), metadata.get('access_level'),
                metadata.get('software_show_name'), metadata.get('audio_code'),
                metadata.get('audio_file_name'), metadata.get('transcription_file_name'),
                metadata.get('transcription_done_date'), metadata.get('published_date'),
                metadata.get('video_id'), metadata.get('social_media_title'),
                metadata.get('description'), metadata.get('social_media_url'),
                metadata.get('duration_category'), metadata.get('processing_notes'),
                user_id
            ))

            conn.commit()
            return True

    except Exception as e:
        print(f"Error saving metadata draft: {e}")
        return False

def add_to_queue(folder_path, category, assign_to, video_ids, url, remarks, operation_type='move'):
    """Add folder to processing queue"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            folder_name = os.path.basename(folder_path)
            assigned_by = session.get('user_id') if 'user_id' in session else None

            # Insert into database
            cursor.execute('''
                INSERT INTO queue_items (folder_name, folder_path, original_path, category, assign_to,
                                       video_ids, url, remarks, operation_type, assigned_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (folder_name, folder_path, folder_path, category, assign_to,
                  video_ids, url, remarks, operation_type, assigned_by))

            queue_id = cursor.lastrowid
            conn.commit()

        # Also add to memory queue for backward compatibility
        with queue_lock:
            queue_item = {
                'id': queue_id,
                'folder_path': folder_path,
                'folder_name': folder_name,
                'category': category,
                'assign_to': assign_to,
                'video_ids': video_ids,
                'url': url,
                'remarks': remarks,
                'operation_type': operation_type,
                'status': 'queued',
                'added_at': datetime.now().isoformat(),
                'processed_at': None,
                'error': None
            }
            processing_queue.append(queue_item)

        return queue_id

    except Exception as e:
        print(f"Error adding to queue: {e}")
        return None

def get_queue_status():
    """Get current queue status from database"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Get all queue items
            cursor.execute('''
                SELECT id, folder_name, folder_path, original_path, category, assign_to,
                       video_ids, url, remarks, operation_type, status, created_at, processed_at, error
                FROM queue_items
                ORDER BY created_at DESC
            ''')

            items = []
            for row in cursor.fetchall():
                items.append({
                    'id': row[0],
                    'folder_name': row[1],
                    'folder_path': row[2],
                    'original_path': row[3],
                    'category': row[4],
                    'assign_to': row[5],
                    'video_ids': row[6],
                    'url': row[7],
                    'remarks': row[8],
                    'operation_type': row[9],
                    'status': row[10],
                    'added_at': row[11],
                    'processed_at': row[12],
                    'error': row[13]
                })

            # Get counts by status
            cursor.execute('SELECT status, COUNT(*) FROM queue_items GROUP BY status')
            status_counts = dict(cursor.fetchall())

        return {
            'total': len(items),
            'queued': status_counts.get('queued', 0),
            'processing': status_counts.get('processing', 0),
            'completed': status_counts.get('completed', 0),
            'failed': status_counts.get('failed', 0),
            'items': items
        }

    except Exception as e:
        print(f"Error getting queue status: {e}")
        return {
            'total': 0,
            'queued': 0,
            'processing': 0,
            'completed': 0,
            'failed': 0,
            'items': []
        }

def update_queue_item_status(item_id, status, error=None):
    """Update queue item status in database"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Update database
            if status in ['completed', 'failed']:
                cursor.execute('''
                    UPDATE queue_items
                    SET status = ?, processed_at = CURRENT_TIMESTAMP, error = ?
                    WHERE id = ?
                ''', (status, error, item_id))
            else:
                cursor.execute('''
                    UPDATE queue_items
                    SET status = ?, error = ?
                    WHERE id = ?
                ''', (status, error, item_id))

            conn.commit()

        # Also update memory queue for backward compatibility
        with queue_lock:
            for item in processing_queue:
                if item['id'] == item_id:
                    item['status'] = status
                    if status in ['completed', 'failed']:
                        item['processed_at'] = datetime.now().isoformat()
                    if error:
                        item['error'] = error
                    break

    except Exception as e:
        print(f"Error updating queue item status: {e}")

def clear_completed_queue_items():
    """Remove completed and failed items from queue"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Delete completed and failed items from database
            cursor.execute('''
                DELETE FROM queue_items
                WHERE status IN ('completed', 'failed')
            ''')

            deleted_count = cursor.rowcount
            conn.commit()

            print(f"✅ Cleared {deleted_count} completed/failed items from database")

        # Also clear from in-memory queue
        with queue_lock:
            global processing_queue
            original_count = len(processing_queue)
            processing_queue = [item for item in processing_queue if item['status'] in ['queued', 'processing']]
            memory_cleared = original_count - len(processing_queue)
            print(f"✅ Cleared {memory_cleared} items from memory queue")

    except Exception as e:
        print(f"❌ Error clearing completed queue items: {e}")
        raise

# Initialize on startup
init_database()
create_directory_structure()

# Routes
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # Rate limiting for security (simple implementation)
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))

        # Use the new database connection manager
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Enhanced user query with more fields
            cursor.execute('''
                SELECT id, username, password_hash, role, is_active, email, created_at
                FROM users
                WHERE username = ?
            ''', (username,))
            user = cursor.fetchone()

            if user and check_password_hash(user[2], password):
                if not user[4]:  # Check if user is active
                    flash('Account is deactivated. Please contact administrator.', 'error')
                    return render_template('login.html')

                # Generate session token for concurrent user support
                import secrets
                session_token = secrets.token_urlsafe(32)

                # Store enhanced session information
                session['user_id'] = user[0]
                session['username'] = user[1]
                session['role'] = user[3]
                session['session_token'] = session_token
                session['login_time'] = datetime.now().isoformat()
                session['client_ip'] = client_ip
                session['user_email'] = user[5] or ''

                # Update user record with session info and last login
                cursor.execute('''
                    UPDATE users
                    SET last_login = ?, session_token = ?, last_ip = ?
                    WHERE id = ?
                ''', (datetime.now().isoformat(), session_token, client_ip, user[0]))

                conn.commit()

                # Enhanced login logging
                log_operation(user[0], 'user_login',
                             details=f'User {username} ({user[3]}) logged in from {client_ip} - Session: {session_token[:8]}...')

                flash(f'Welcome back, {user[1]}! You are logged in as {user[3]}.', 'success')

                # Role-based redirection
                if user[3] == 'Main Admin':
                    return redirect(url_for('admin_interface'))
                elif user[3] == 'Assigner':
                    return redirect(url_for('assigner_interface'))
                elif user[3] in ['Executor Public', 'Executor Private']:
                    return redirect(url_for('executor_public_interface'))
                elif user[3] == 'Cross Checker':
                    return redirect(url_for('cross_checker_interface'))
                else:
                    return redirect(url_for('dashboard'))
            else:
                # Log failed login attempt for security monitoring
                try:
                    log_operation(None, 'failed_login_attempt',
                                 details=f'Failed login for username: {username} from IP: {client_ip}')
                except:
                    pass  # Don't fail login if logging fails

                flash('Invalid username or password. Please try again.', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    log_operation(session['user_id'], 'logout', details=f'User {session["username"]} logged out')
    session.clear()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    with get_db_connection() as conn:
        cursor = conn.cursor()

        # Get statistics for admin
        stats = {}
        if session['role'] == 'Main Admin':
            cursor.execute('SELECT COUNT(*) FROM files')
            stats['total_files'] = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM files WHERE status = "Completed"')
            stats['completed_files'] = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM files WHERE status IN ("Assigned", "Pending")')
            stats['pending_files'] = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            stats['total_users'] = cursor.fetchone()[0]

        # Get recent operations (exclude login/logout activities)
        cursor.execute('''
            SELECT ol.operation_type, ol.file_path, u.username, ol.status, ol.created_at
            FROM operations_log ol
            LEFT JOIN users u ON ol.user_id = u.id
            WHERE ol.operation_type NOT IN ('login', 'logout')
            ORDER BY ol.created_at DESC
            LIMIT 10
        ''')
        recent_operations = [
            {
                'operation_type': row[0],
                'file_path': row[1],
                'username': row[2],
                'status': row[3],
                'created_at': row[4]
            }
            for row in cursor.fetchall()
        ]

    return render_template('dashboard.html', stats=stats, recent_operations=recent_operations)

# Professional Assigner Interface (Main Interface)
@app.route('/assigner')
@role_required(['Assigner'])
def assigner_interface():
    """Professional Assigner interface with advanced folder management and queue system"""
    return render_template('professional_assigner.html',
                         categories=Config.PROCESSING_CATEGORIES)

# Redirect all old assigner routes to professional assigner
@app.route('/working-assigner')
@role_required(['Assigner'])
def working_assigner():
    """Redirect to professional assigner"""
    return redirect(url_for('assigner_interface'))

@app.route('/tree-assigner')
@role_required(['Assigner'])
def tree_assigner():
    """Redirect to professional assigner"""
    return redirect(url_for('assigner_interface'))

@app.route('/simple-assigner')
@role_required(['Assigner'])
def simple_assigner():
    """Redirect to professional assigner"""
    return redirect(url_for('assigner_interface'))

@app.route('/professional-assigner')
@role_required(['Assigner'])
def professional_assigner():
    """Redirect to main assigner interface"""
    return redirect(url_for('assigner_interface'))

@app.route('/assigner-interface')
@role_required(['Assigner'])
def assigner_interface_redirect():
    """Redirect to main assigner interface"""
    return redirect(url_for('assigner_interface'))

@app.route('/executive-public')
@role_required(['Executor Public'])
def executive_public():
    """Executive Public dashboard"""
    return render_template('executive_public.html')

@app.route('/executive-private')
@role_required(['Executive Private'])
def executive_private():
    """Executive Private dashboard"""
    return render_template('executive_private.html')

@app.route('/api/executive-public/queue')
@role_required(['Executor Public'])
def get_executive_public_queue():
    """Get files in queue for Executive Public processing"""
    try:
        queue_files = []

        # Get files from database that are assigned to Executive Public and completed
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Get completed queue items assigned to Executive Public (exclude private category)
            cursor.execute('''
                SELECT id, folder_name, folder_path, category, assign_to, created_at, processed_at,
                       video_ids, url, remarks
                FROM queue_items
                WHERE assign_to = 'Executor Public'
                AND status = 'completed'
                AND category != 'Private one video'
                ORDER BY processed_at DESC
            ''')

            db_files = cursor.fetchall()

            for row in db_files:
                queue_id, folder_name, folder_path, category, assign_to, created_at, processed_at, video_ids, url, remarks = row

                # Check if folder exists in destination
                dest_folder_path = os.path.join(Config.DEST_PATH, category, folder_name)

                if os.path.exists(dest_folder_path):
                    # Count files in folder
                    file_count = 0
                    total_size = 0

                    for root, dirs, files in os.walk(dest_folder_path):
                        for file in files:
                            if file.lower().endswith(('.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm')):
                                file_path = os.path.join(root, file)
                                try:
                                    file_size = os.path.getsize(file_path)
                                    total_size += file_size
                                    file_count += 1
                                except:
                                    pass

                    if file_count > 0:
                        # Format assigned date
                        assigned_date = processed_at.split(' ')[0] if processed_at else created_at.split(' ')[0] if created_at else 'Unknown'

                        queue_files.append({
                            'queue_item_id': queue_id,  # Fixed: use queue_item_id instead of queue_id
                            'folder_name': folder_name,
                            'folder_path': dest_folder_path,
                            'file_count': file_count,
                            'total_size_formatted': format_file_size(total_size),  # Fixed: use total_size_formatted
                            'assigned_date': assigned_date,
                            'category': category,
                            'status': 'Ready for Processing',
                            'video_ids': video_ids or '',
                            'url': url or '',
                            'remarks': remarks or '',
                            'assign_to': assign_to
                        })

        return jsonify({
            'success': True,
            'files': queue_files,
            'total_count': len(queue_files)
        })

    except Exception as e:
        print(f"Error in get_executive_public_queue: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

def get_folder_date(folder_path):
    """Get folder creation/modification date"""
    try:
        timestamp = os.path.getctime(folder_path)
        return datetime.fromtimestamp(timestamp).strftime('%m/%d/%Y')
    except:
        return datetime.now().strftime('%m/%d/%Y')

@app.route('/api/executive-public/metadata-options')
@role_required(['Executor Public'])
def get_metadata_options():
    """Get dropdown options for metadata fields"""
    try:
        options = {
            'video_types': Config.VIDEO_TYPES,
            'languages': Config.LANGUAGES,
            'edited_locations': ['Ashram', 'US'],
            'published_platforms': Config.PUBLISHED_PLATFORMS,
            'departments': Config.DEPARTMENTS,
            'components': ['Sadhguru', 'Non-Sadhguru'],
            'content_tags': Config.CONTENT_TAGS,
            'backup_types': Config.BACKUP_TYPES,
            'access_levels': ['Public', 'Private'],
            'duration_ranges': ['<1min', '1-3min', '3-5min', '5-10min', '10-30min', '>30min'],
            'file_size_ranges': ['<1GB', '1-5GB', '5-10GB', '>10GB'],
            'file_count_ranges': ['1-5', '6-10', '11-20', '>20'],
            'transcription_status': ['Pending', 'In Progress', 'Completed', 'Not Required']
        }

        return jsonify({
            'success': True,
            'options': options
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/file-details/<int:queue_item_id>')
@role_required(['Executor Public', 'Executor Private'])
def get_file_details(queue_item_id):
    """Get detailed file information including auto-detected metadata"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Get queue item details
            cursor.execute('''
                SELECT folder_name, folder_path, category, video_ids, url, remarks
                FROM queue_items WHERE id = ?
            ''', (queue_item_id,))

            queue_item = cursor.fetchone()
            if not queue_item:
                return jsonify({'success': False, 'error': 'Queue item not found'})

            folder_name, folder_path, category, video_ids, url, remarks = queue_item

            # Auto-detect file information
            file_count = count_files_in_folder(folder_path) if os.path.exists(folder_path) else 0
            folder_size = get_folder_size(folder_path) if os.path.exists(folder_path) else 0

            # Try to get video metadata from first video file
            video_metadata = {'duration': 'Unknown', 'resolution': 'Unknown', 'format': 'Unknown'}
            if os.path.exists(folder_path):
                video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.f4v'}
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        if any(file.lower().endswith(ext) for ext in video_extensions):
                            video_file_path = os.path.join(root, file)
                            video_metadata = get_video_metadata(video_file_path)
                            break
                    if video_metadata['duration'] != 'Unknown':
                        break

            # Check for existing metadata draft
            cursor.execute('SELECT * FROM file_metadata WHERE queue_item_id = ?', (queue_item_id,))
            existing_metadata = cursor.fetchone()

            # Generate unique codes
            codes = generate_unique_codes()

            file_details = {
                'queue_item_id': queue_item_id,
                'folder_name': folder_name,
                'folder_path': folder_path,
                'category': category,
                'video_ids': video_ids,
                'url': url,
                'remarks': remarks,
                'file_count': file_count,
                'total_size': folder_size,
                'total_size_formatted': format_file_size(folder_size),
                'detected_duration': video_metadata['duration'],
                'detected_resolution': video_metadata['resolution'],
                'detected_format': video_metadata['format'],
                'suggested_codes': codes,
                'has_draft': existing_metadata is not None
            }

            # If draft exists, include the saved metadata
            if existing_metadata:
                # Map database columns to metadata fields
                metadata_columns = [
                    'id', 'queue_item_id', 'folder_path', 'ocd_vp_number', 'edited_file_name',
                    'language', 'edited_year', 'trim_closed_date', 'total_duration', 'video_type',
                    'edited_location', 'published_platforms', 'department_name', 'component',
                    'content_tags', 'backup_type', 'access_level', 'software_show_name',
                    'file_count', 'total_file_size', 'detected_duration', 'detected_resolution',
                    'detected_format', 'audio_code', 'audio_file_name', 'transcription_file_name',
                    'transcription_done_date', 'transcription_status', 'published_date', 'video_id',
                    'social_media_title', 'description', 'social_media_url', 'duration_category',
                    'processed_by', 'processing_notes', 'draft_saved', 'processing_status',
                    'created_at', 'updated_at'
                ]

                draft_metadata = dict(zip(metadata_columns, existing_metadata))
                file_details['draft_metadata'] = draft_metadata

        return jsonify({'success': True, 'file_details': file_details})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/save-metadata-draft', methods=['POST'])
@role_required(['Executor Public', 'Executor Private'])
def save_metadata_draft_api():
    """Save metadata as draft without processing the file"""
    try:
        data = request.get_json()
        queue_item_id = data.get('queue_item_id')
        metadata = data.get('metadata')

        if not queue_item_id or not metadata:
            return jsonify({'success': False, 'error': 'Missing queue item ID or metadata'})

        # Save draft
        success = save_metadata_draft(queue_item_id, metadata, session['user_id'])

        if success:
            # Log the operation
            log_operation(
                session['user_id'],
                'metadata_draft_saved',
                details=f'Saved metadata draft for queue item {queue_item_id}'
            )

            return jsonify({'success': True, 'message': 'Metadata draft saved successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to save metadata draft'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/preview-video', methods=['POST'])
@role_required(['Executor Public', 'Executor Private'])
def preview_video():
    """Launch VLC to preview video files in a folder"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')

        if not folder_path:
            return jsonify({'success': False, 'error': 'Folder path is required'})

        if not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Folder not found'})

        # Find video files in the folder
        video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.f4v'}
        video_files = []

        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(os.path.join(root, file))

        if not video_files:
            return jsonify({'success': False, 'error': 'No video files found in folder'})

        # Try to find VLC executable
        vlc_paths = [
            r"C:\Program Files\VideoLAN\VLC\vlc.exe",
            r"C:\Program Files (x86)\VideoLAN\VLC\vlc.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\VideoLAN\VLC\vlc.exe".format(os.getenv('USERNAME', '')),
            "vlc.exe"  # If VLC is in PATH
        ]

        vlc_exe = None
        for path in vlc_paths:
            if os.path.exists(path):
                vlc_exe = path
                break

        if not vlc_exe:
            # Try to find VLC in PATH
            import shutil
            vlc_exe = shutil.which("vlc")

        if not vlc_exe:
            return jsonify({
                'success': False,
                'error': 'VLC Media Player not found. Please install VLC or check installation path.',
                'video_files': [os.path.basename(f) for f in video_files]
            })

        # Launch VLC with the first video file (or all files)
        try:
            import subprocess

            # Launch VLC with all video files in the folder
            cmd = [vlc_exe] + video_files
            subprocess.Popen(cmd, shell=False)

            # Log the operation
            log_operation(
                session['user_id'],
                'video_preview',
                details=f'Opened {len(video_files)} video files in VLC from {folder_path}'
            )

            return jsonify({
                'success': True,
                'message': f'Opened {len(video_files)} video files in VLC',
                'video_files': [os.path.basename(f) for f in video_files],
                'vlc_path': vlc_exe
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Failed to launch VLC: {str(e)}',
                'video_files': [os.path.basename(f) for f in video_files]
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/executive-public/process-metadata', methods=['POST'])
@role_required(['Executor Public'])
def process_metadata():
    """Process metadata for files and move to cross-checker queue"""
    try:
        data = request.get_json()
        queue_item_id = data.get('queue_item_id')
        metadata = data.get('metadata')

        if not queue_item_id or not metadata:
            return jsonify({
                'success': False,
                'error': 'Missing queue item ID or metadata'
            })

        # Use proper database connection management
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Get queue item details
            cursor.execute('''
                SELECT folder_name, folder_path, category
                FROM queue_items WHERE id = ?
            ''', (queue_item_id,))

            queue_item = cursor.fetchone()
            if not queue_item:
                return jsonify({'success': False, 'error': 'Queue item not found'})

            folder_name, folder_path, category = queue_item

            # Debug: Print folder path information
            print(f"🔍 DEBUG: Processing folder: {folder_name}")
            print(f"🔍 DEBUG: Database folder_path: {folder_path}")
            print(f"🔍 DEBUG: Category: {category}")
            print(f"🔍 DEBUG: Folder exists: {os.path.exists(folder_path) if folder_path else False}")

            # If the database folder_path doesn't exist, try to find the folder in the destination
            if not folder_path or not os.path.exists(folder_path):
                print(f"🔧 DEBUG: Original path not found, searching in destination...")

                # Try to find the folder in the destination path
                dest_folder_path = os.path.join(Config.DEST_PATH, category, folder_name)
                print(f"🔧 DEBUG: Trying destination path: {dest_folder_path}")

                if os.path.exists(dest_folder_path):
                    folder_path = dest_folder_path
                    print(f"✅ DEBUG: Found folder at destination: {folder_path}")
                else:
                    print(f"❌ DEBUG: Folder not found at destination either")

                    # Try to search in all category folders
                    for cat_folder in os.listdir(Config.DEST_PATH):
                        cat_path = os.path.join(Config.DEST_PATH, cat_folder)
                        if os.path.isdir(cat_path):
                            potential_path = os.path.join(cat_path, folder_name)
                            if os.path.exists(potential_path):
                                folder_path = potential_path
                                print(f"✅ DEBUG: Found folder in different category: {folder_path}")
                                break

            # Validate required fields
            required_fields = [
                'ocd_vp_number', 'edited_file_name', 'language', 'edited_year',
                'video_type', 'edited_location', 'published_platforms',
                'department_name', 'component', 'content_tags', 'backup_type',
                'access_level', 'audio_code', 'video_id', 'social_media_title'
            ]

            missing_fields = [field for field in required_fields if not metadata.get(field)]
            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                })

            # Auto-detect file information
            file_count = count_files_in_folder(folder_path) if os.path.exists(folder_path) else 0
            folder_size = get_folder_size(folder_path) if os.path.exists(folder_path) else 0

            # Get video metadata
            video_metadata = get_video_metadata(folder_path) if os.path.exists(folder_path) else {}

            # Save comprehensive metadata to database
            cursor.execute('''
                INSERT OR REPLACE INTO file_metadata (
                    queue_item_id, folder_path, ocd_vp_number, edited_file_name, language,
                    edited_year, trim_closed_date, total_duration, video_type, edited_location,
                    published_platforms, department_name, component, content_tags, backup_type,
                    access_level, software_show_name, file_count, total_file_size,
                    detected_duration, detected_resolution, detected_format,
                    audio_code, audio_file_name, transcription_file_name,
                    transcription_done_date, transcription_status, published_date, video_id,
                    social_media_title, description, social_media_url, duration_category,
                    processing_notes, processed_by, processing_status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                queue_item_id, folder_path, metadata.get('ocd_vp_number'),
                metadata.get('edited_file_name'), metadata.get('language'),
                metadata.get('edited_year'), metadata.get('trim_closed_date'),
                metadata.get('total_duration'), metadata.get('video_type'),
                metadata.get('edited_location'), metadata.get('published_platforms'),
                metadata.get('department_name'), metadata.get('component'),
                metadata.get('content_tags'), metadata.get('backup_type'),
                metadata.get('access_level'), metadata.get('software_show_name'),
                file_count, folder_size, video_metadata.get('duration'),
                video_metadata.get('resolution'), video_metadata.get('format'),
                metadata.get('audio_code'), metadata.get('audio_file_name'),
                metadata.get('transcription_file_name'), metadata.get('transcription_done_date'),
                metadata.get('transcription_status'), metadata.get('published_date'),
                metadata.get('video_id'), metadata.get('social_media_title'),
                metadata.get('description'), metadata.get('social_media_url'),
                metadata.get('duration_category'), metadata.get('processing_notes'),
                session['user_id'], 'Completed'
            ))

            # STEP 1: Update Google Sheets with metadata
            print("🔧 Step 1: Updating Google Sheets with metadata...")
            sheets_result = update_metadata_in_google_sheets(folder_name, {
                'ocd_vp_number': metadata.get('ocd_vp_number'),
                'edited_file_name': metadata.get('edited_file_name'),
                'total_file_size': format_file_size(folder_size),
                'file_count': file_count,
                'detected_duration': video_metadata.get('duration'),
                'language': metadata.get('language'),
                'edited_year': metadata.get('edited_year'),
                'video_type': metadata.get('video_type'),
                'edited_location': metadata.get('edited_location'),
                'published_platforms': metadata.get('published_platforms'),
                'department_name': metadata.get('department_name'),
                'component': metadata.get('component'),
                'content_tags': metadata.get('content_tags'),
                'backup_type': metadata.get('backup_type'),
                'access_level': metadata.get('access_level'),
                'software_show_name': metadata.get('software_show_name'),
                'audio_code': metadata.get('audio_code'),
                'audio_file_name': metadata.get('audio_file_name'),
                'transcription_file_name': metadata.get('transcription_file_name'),
                'transcription_status': metadata.get('transcription_status'),
                'published_date': metadata.get('published_date'),
                'video_id': metadata.get('video_id'),
                'social_media_title': metadata.get('social_media_title'),
                'description': metadata.get('description'),
                'social_media_url': metadata.get('social_media_url'),
                'duration_category': metadata.get('duration_category')
            })

            # STEP 2: Move and rename folder
            print("🔧 Step 2: Moving and renaming folder...")
            crosscheck_path = r"T:\To_Process\Rough folder\Folder to be cross checked"
            os.makedirs(crosscheck_path, exist_ok=True)

            # Use the Software Show/Renamed Folder value for the new folder name
            new_folder_name = metadata.get('software_show_name', folder_name)
            dest_path = os.path.join(crosscheck_path, new_folder_name)

            # STEP 3: Extract audio file before moving folder
            print("🔧 Step 3: Extracting audio file...")
            audio_result = {"success": False, "error": "Not attempted"}

            if os.path.exists(folder_path):
                # Extract audio file first (before moving the folder)
                audio_file_name = metadata.get('audio_file_name', f"{folder_name}_Audio")
                audio_result = extract_and_move_audio_file(
                    source_folder_path=folder_path,
                    destination_folder_path=crosscheck_path,
                    audio_file_name=audio_file_name
                )

                # Now move the folder
                if os.path.exists(dest_path):
                    # If destination exists, add timestamp
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    dest_path = os.path.join(crosscheck_path, f"{new_folder_name}_{timestamp}")

                shutil.move(folder_path, dest_path)

                # Update queue item status within the same transaction
                cursor.execute('''
                    UPDATE queue_items
                    SET status = 'processed', processed_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (queue_item_id,))

                # Commit all database changes in one transaction
                conn.commit()

                # Log operation (this will use its own connection)
                log_operation(
                    session['user_id'],
                    'comprehensive_metadata_processed',
                    details=f'Processed {folder_name} with full metadata - OCD: {metadata.get("ocd_vp_number")}'
                )

                return jsonify({
                    'success': True,
                    'message': f'File processed successfully with comprehensive metadata and moved to cross-checker queue',
                    'destination': dest_path,
                    'original_folder': folder_name,
                    'renamed_folder': new_folder_name,
                    'sheets_updated': sheets_result.get('success', False),
                    'sheets_message': sheets_result.get('message', ''),
                    'audio_extracted': audio_result.get('success', False),
                    'audio_message': audio_result.get('message', ''),
                    'audio_file': audio_result.get('destination_file', ''),
                    'metadata_saved': True,
                    'processing_steps': {
                        'google_sheets': sheets_result.get('success', False),
                        'folder_moved': True,
                        'folder_renamed': new_folder_name != folder_name,
                        'audio_extracted': audio_result.get('success', False)
                    }
                })
            else:
                error_msg = f'Source folder not found: {folder_path}'
                print(f"❌ ERROR: {error_msg}")
                print(f"🔍 DEBUG: Searched paths:")
                print(f"   - Original: {queue_item[1] if len(queue_item) > 1 else 'None'}")
                print(f"   - Destination: {os.path.join(Config.DEST_PATH, category, folder_name)}")
                print(f"   - Final: {folder_path}")

                return jsonify({
                    'success': False,
                    'error': error_msg,
                    'debug_info': {
                        'folder_name': folder_name,
                        'category': category,
                        'original_path': queue_item[1] if len(queue_item) > 1 else None,
                        'searched_path': folder_path,
                        'dest_path_exists': os.path.exists(Config.DEST_PATH)
                    }
                })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def generate_audio_code():
    """Generate unique audio/transcript code"""
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"AUD-{timestamp}"

def save_metadata_to_sheet(folder_path, metadata):
    """Save metadata to Google Sheets"""
    try:
        # This would integrate with your existing Google Sheets functionality
        # For now, we'll log the metadata
        print(f"📊 Saving metadata for {folder_path}")
        print(f"📋 Metadata: {metadata}")

        # Add to your existing Google Sheets integration here
        # worksheet.append_row([...metadata values...])

    except Exception as e:
        print(f"❌ Error saving metadata: {e}")

def log_action(user_id, action, details, folder_path=None):
    """Log user actions"""
    try:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = {
            'timestamp': timestamp,
            'user_id': user_id,
            'action': action,
            'details': details,
            'folder_path': folder_path
        }
        print(f"📝 Action logged: {log_entry}")

        # Add to your existing logging system here

    except Exception as e:
        print(f"❌ Error logging action: {e}")

@app.route('/api/executive-public/folder-tree')
@role_required(['Executor Public'])
def get_executive_public_folder_tree():
    """Get folder tree structure for Executive Public (matching Archives Assignment Console)"""
    try:
        # Use the EXACT SAME logic as Archives Assignment Console (/api/get-folders)
        def scan_folder(folder_path, max_depth=5, current_depth=0, show_files=True):
            """Recursively scan folder structure with complete file listing (EXACT COPY from Archives Console)"""
            if current_depth >= max_depth:
                return []

            items = []
            try:
                if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                    return items

                # Get all items in the folder
                all_items = []
                try:
                    all_items = os.listdir(folder_path)
                except PermissionError:
                    return items

                # Sort items: folders first, then files
                folders = []
                files = []

                for item in all_items:
                    item_path = os.path.join(folder_path, item)
                    try:
                        if os.path.isdir(item_path):
                            folders.append(item)
                        elif show_files and os.path.isfile(item_path):
                            files.append(item)
                    except (OSError, PermissionError):
                        continue

                # Process folders first
                for folder in sorted(folders):
                    folder_path = os.path.join(folder_path, folder)
                    try:
                        folder_size = get_folder_size(folder_path)
                        children = scan_folder(folder_path, max_depth, current_depth + 1, show_files)

                        items.append({
                            'name': folder,
                            'path': folder_path,
                            'type': 'folder',
                            'size': format_file_size(folder_size),
                            'size_bytes': folder_size,
                            'children': children,
                            'level': current_depth
                        })
                    except Exception as e:
                        print(f"Error processing folder {folder}: {e}")

                # Process files if requested
                if show_files:
                    for file in sorted(files):
                        file_path = os.path.join(folder_path, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            items.append({
                                'name': file,
                                'path': file_path,
                                'type': 'file',
                                'size': format_file_size(file_size),
                                'size_bytes': file_size,
                                'level': current_depth
                            })
                        except Exception as e:
                            print(f"Error processing file {file}: {e}")

            except Exception as e:
                print(f"Error scanning folder {folder_path}: {e}")

            return items

        # Use the EXACT SAME source path as Archives Assignment Console
        source_path = r"T:\To_Process\Rough folder\restored files"

        if not os.path.exists(source_path):
            return jsonify({'success': False, 'error': 'Source folder not found'})

        folders = scan_folder(source_path, max_depth=3, current_depth=0, show_files=True)

        return jsonify({
            'success': True,
            'folders': folders,
            'source_path': source_path
        })

    except Exception as e:
        print(f"Error in get_executive_public_folder_tree: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

def build_folder_tree_structure(path, name, level, max_depth=3):
    """Build folder tree structure (matching Archives Assignment Console logic)"""
    try:
        if level > max_depth:
            return None

        item_info = {
            'name': name,
            'path': path,
            'type': 'folder',
            'level': level,
            'children': [],
            'file_count': 0,
            'media_count': 0,
            'size': 0
        }

        if os.path.isdir(path):
            try:
                items = os.listdir(path)
                for item in items:
                    item_path = os.path.join(path, item)

                    if os.path.isfile(item_path):
                        # File
                        file_size = os.path.getsize(item_path)
                        item_info['size'] += file_size
                        item_info['file_count'] += 1

                        # Check if it's a media file
                        ext = os.path.splitext(item)[1].lower()
                        is_media = ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.m4a', '.aac', '.ogg']

                        if is_media:
                            item_info['media_count'] += 1

                        if level < max_depth:
                            file_info = {
                                'name': item,
                                'path': item_path,
                                'type': 'file',
                                'level': level + 1,
                                'size': format_file_size(file_size),
                                'extension': ext,
                                'is_media': is_media
                            }
                            item_info['children'].append(file_info)

                    elif os.path.isdir(item_path) and level < max_depth:
                        # Subfolder
                        child_info = build_folder_tree_structure(item_path, item, level + 1, max_depth)
                        if child_info:
                            item_info['children'].append(child_info)
                            item_info['file_count'] += child_info['file_count']
                            item_info['media_count'] += child_info['media_count']
                            item_info['size'] += child_info['size']

            except (OSError, PermissionError):
                pass

        return item_info

    except Exception as e:
        print(f"Error building folder tree for {path}: {e}")
        return None

@app.route('/api/executive-private/folder-tree')
@role_required(['Executor Private'])
def get_executive_private_folder_tree():
    """Get folder tree structure for Executive Private (matching Archives Assignment Console)"""
    try:
        # Use the EXACT SAME logic as Archives Assignment Console (/api/get-folders)
        def scan_folder(folder_path, max_depth=5, current_depth=0, show_files=True):
            """Recursively scan folder structure with complete file listing (EXACT COPY from Archives Console)"""
            if current_depth >= max_depth:
                return []

            items = []
            try:
                if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                    return items

                # Get all items in the folder
                all_items = []
                try:
                    all_items = os.listdir(folder_path)
                except PermissionError:
                    return items

                # Sort items: folders first, then files
                folders = []
                files = []

                for item in all_items:
                    item_path = os.path.join(folder_path, item)
                    try:
                        if os.path.isdir(item_path):
                            folders.append(item)
                        elif show_files and os.path.isfile(item_path):
                            files.append(item)
                    except (OSError, PermissionError):
                        continue

                # Process folders first
                for folder in sorted(folders):
                    folder_path = os.path.join(folder_path, folder)
                    try:
                        folder_size = get_folder_size(folder_path)
                        children = scan_folder(folder_path, max_depth, current_depth + 1, show_files)

                        items.append({
                            'name': folder,
                            'path': folder_path,
                            'type': 'folder',
                            'size': format_file_size(folder_size),
                            'size_bytes': folder_size,
                            'children': children,
                            'level': current_depth
                        })
                    except Exception as e:
                        print(f"Error processing folder {folder}: {e}")

                # Process files if requested
                if show_files:
                    for file in sorted(files):
                        file_path = os.path.join(folder_path, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            items.append({
                                'name': file,
                                'path': file_path,
                                'type': 'file',
                                'size': format_file_size(file_size),
                                'size_bytes': file_size,
                                'level': current_depth
                            })
                        except Exception as e:
                            print(f"Error processing file {file}: {e}")

            except Exception as e:
                print(f"Error scanning folder {folder_path}: {e}")

            return items

        # Use the EXACT SAME source path as Archives Assignment Console
        source_path = r"T:\To_Process\Rough folder\restored files"

        if not os.path.exists(source_path):
            return jsonify({'success': False, 'error': 'Source folder not found'})

        folders = scan_folder(source_path, max_depth=3, current_depth=0, show_files=True)

        return jsonify({
            'success': True,
            'folders': folders,
            'source_path': source_path
        })

    except Exception as e:
        print(f"Error in get_executive_private_folder_tree: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/executive-private/queue')
@role_required(['Executor Private'])
def get_executive_private_queue():
    """Get files in queue for Executive Private processing"""
    try:
        queue_files = []

        # Get files from database that are assigned to Executive Private and completed
        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        # Get completed queue items assigned to Executive Private (includes all categories)
        cursor.execute('''
            SELECT id, folder_name, folder_path, category, assign_to, created_at, processed_at,
                   video_ids, url, remarks
            FROM queue_items
            WHERE assign_to = 'Executor Private'
            AND status = 'completed'
            ORDER BY processed_at DESC
        ''')

        db_files = cursor.fetchall()

        for row in db_files:
            queue_id, folder_name, folder_path, category, assign_to, created_at, processed_at, video_ids, url, remarks = row

            # Check if folder exists in destination
            dest_folder_path = os.path.join(Config.DEST_PATH, category, folder_name)

            if os.path.exists(dest_folder_path):
                # Count files in folder
                file_count = 0
                total_size = 0

                for root, dirs, files in os.walk(dest_folder_path):
                    for file in files:
                        if file.lower().endswith(('.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm')):
                            file_path = os.path.join(root, file)
                            try:
                                file_size = os.path.getsize(file_path)
                                total_size += file_size
                                file_count += 1
                            except:
                                pass

                if file_count > 0:
                    # Format assigned date
                    assigned_date = processed_at.split(' ')[0] if processed_at else created_at.split(' ')[0] if created_at else 'Unknown'

                    queue_files.append({
                        'queue_item_id': queue_id,  # Fixed: use queue_item_id instead of queue_id
                        'folder_name': folder_name,
                        'folder_path': dest_folder_path,
                        'file_count': file_count,
                        'total_size_formatted': format_file_size(total_size),  # Fixed: use total_size_formatted
                        'assigned_date': assigned_date,
                        'category': category,
                        'status': 'Ready for Processing',
                        'is_private': category == 'Private one video',
                        'video_ids': video_ids or '',
                        'url': url or '',
                        'remarks': remarks or '',
                        'assign_to': assign_to
                    })

        conn.close()

        return jsonify({
            'success': True,
            'files': queue_files,
            'total_count': len(queue_files)
        })

    except Exception as e:
        print(f"Error in get_executive_private_queue: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/executive-private/metadata-options')
@role_required(['Executor Private'])
def get_executive_private_metadata_options():
    """Get dropdown options for Executive Private metadata fields"""
    try:
        options = {
            'video_types': [
                'Talk', 'Documentary', 'Insta-Reels', 'Class', 'Interview',
                'Promo', 'Daily-Mystic-Quote', 'Episode', 'Q-And-A', 'Message',
                'Intro', 'Song', 'Glimpses', 'Animation', 'Sharings',
                'Video-Book', 'Teaser', 'Poem', 'Telefilm', 'Non-Ashram Videos',
                'Event', 'Miscellaneous'
            ],
            'languages': [
                'English', 'Tamil', 'Hindi', 'Telugu', 'Kannada', 'Malayalam',
                'Bengali', 'Gujarati', 'Marathi', 'Punjabi', 'Urdu', 'Sanskrit'
            ],
            'edited_locations': ['Ashram', 'US'],
            'published_platforms': [
                'Instagram', 'Youtube', 'Twitter', 'Whatsapp', 'Not Applicable'
            ],
            'departments': [
                'Marketing', 'HR', 'IT', 'Sales', 'Production', 'Legal',
                'Finance', 'Archives', 'Media', 'Content'
            ],
            'components': ['Sadhguru', 'Non-Sadhguru'],
            'content_tags': [
                'Career-Success', 'Yoga-Spirituality', 'Celebrity',
                'Conscious Planet', 'Culture-Tradition', 'Health-Fitness',
                'Isha-Related', 'Lifestyle', 'Miscellaneous',
                'Personal-Wellbeing', 'Relationship-Sexuality',
                'Sadhguru-Related', 'Social-Issues'
            ],
            'backup_types': [
                'Stems', 'Consolidated', 'Unconsolidated', 'Premium Pro',
                'MP4', 'Audio', 'AVI'
            ],
            'access_levels': ['Public', 'Private'],
            'duration_ranges': ['<5min', '5-10min', '10-30min', '>30min'],
            'file_size_ranges': ['<1GB', '1-5GB', '5-10GB', '>10GB'],
            'file_count_ranges': ['1-5', '6-10', '11-20', '>20']
        }

        return jsonify({
            'success': True,
            'options': options
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/executive-private/process-metadata', methods=['POST'])
@role_required(['Executor Private'])
def process_executive_private_metadata():
    """Process metadata for Executive Private files and move to cross-checker queue"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')
        metadata = data.get('metadata')

        if not folder_path or not metadata:
            return jsonify({
                'success': False,
                'error': 'Missing folder path or metadata'
            })

        # Validate required fields
        required_fields = [
            'ocd_vp_number', 'edited_file_name', 'language', 'edited_year',
            'video_type', 'edited_location', 'published_platforms',
            'department_name', 'component', 'content_tag', 'backup_type',
            'access_level'
        ]

        missing_fields = [field for field in required_fields if not metadata.get(field)]
        if missing_fields:
            return jsonify({
                'success': False,
                'error': f'Missing required fields: {", ".join(missing_fields)}'
            })

        # Auto-detect file information
        folder_name = os.path.basename(folder_path)
        file_count = count_files_in_folder(folder_path) if os.path.exists(folder_path) else 0
        folder_size = get_folder_size(folder_path) if os.path.exists(folder_path) else 0

        # Get video metadata
        video_metadata = get_video_metadata(folder_path) if os.path.exists(folder_path) else {}

        # STEP 1: Update Google Sheets with metadata
        print("🔧 Step 1: Updating Google Sheets with metadata (Private)...")
        sheets_result = update_metadata_in_google_sheets(folder_name, {
            'ocd_vp_number': metadata.get('ocd_vp_number'),
            'edited_file_name': metadata.get('edited_file_name'),
            'total_file_size': format_file_size(folder_size),
            'file_count': file_count,
            'detected_duration': video_metadata.get('duration'),
            'language': metadata.get('language'),
            'edited_year': metadata.get('edited_year'),
            'video_type': metadata.get('video_type'),
            'edited_location': metadata.get('edited_location'),
            'published_platforms': metadata.get('published_platforms'),
            'department_name': metadata.get('department_name'),
            'component': metadata.get('component'),
            'content_tags': metadata.get('content_tags'),
            'backup_type': metadata.get('backup_type'),
            'access_level': metadata.get('access_level'),
            'software_show_name': metadata.get('software_show_name'),
            'audio_code': metadata.get('audio_code'),
            'audio_file_name': metadata.get('audio_file_name'),
            'transcription_file_name': metadata.get('transcription_file_name'),
            'transcription_status': metadata.get('transcription_status'),
            'published_date': metadata.get('published_date'),
            'video_id': metadata.get('video_id'),
            'social_media_title': metadata.get('social_media_title'),
            'description': metadata.get('description'),
            'social_media_url': metadata.get('social_media_url'),
            'duration_category': metadata.get('duration_category')
        })

        # STEP 2: Move and rename folder
        print("🔧 Step 2: Moving and renaming folder (Private)...")
        crosscheck_path = r"T:\To_Process\Rough folder\Folder to be cross checked"
        os.makedirs(crosscheck_path, exist_ok=True)

        # Use the Software Show/Renamed Folder value for the new folder name
        new_folder_name = metadata.get('software_show_name', folder_name)
        dest_path = os.path.join(crosscheck_path, new_folder_name)

        # STEP 3: Extract audio file before moving folder
        print("🔧 Step 3: Extracting audio file (Private)...")
        audio_result = {"success": False, "error": "Not attempted"}

        if os.path.exists(folder_path):
            # Extract audio file first (before moving the folder)
            audio_file_name = metadata.get('audio_file_name', f"{folder_name}_Audio")
            audio_result = extract_and_move_audio_file(
                source_folder_path=folder_path,
                destination_folder_path=crosscheck_path,
                audio_file_name=audio_file_name
            )

            # Now move the folder
            if os.path.exists(dest_path):
                # If destination exists, add timestamp
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                dest_path = os.path.join(crosscheck_path, f"{new_folder_name}_{timestamp}")

            shutil.move(folder_path, dest_path)

            # Log the action
            log_action(
                user_id=session.get('user_id'),
                action='metadata_processed_private',
                details=f'Executive Private processed metadata for {folder_name} and moved to cross-checker queue',
                folder_path=dest_path
            )

            return jsonify({
                'success': True,
                'message': f'Private file processed successfully with comprehensive metadata and moved to cross-checker queue',
                'destination': dest_path,
                'original_folder': folder_name,
                'renamed_folder': new_folder_name,
                'sheets_updated': sheets_result.get('success', False),
                'sheets_message': sheets_result.get('message', ''),
                'audio_extracted': audio_result.get('success', False),
                'audio_message': audio_result.get('message', ''),
                'audio_file': audio_result.get('destination_file', ''),
                'metadata_saved': True,
                'processing_steps': {
                    'google_sheets': sheets_result.get('success', False),
                    'folder_moved': True,
                    'folder_renamed': new_folder_name != folder_name,
                    'audio_extracted': audio_result.get('success', False)
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Source folder not found'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cross-checker/folder-tree')
@role_required(['Cross Checker'])
def get_cross_checker_folder_tree():
    """Get folder tree structure for Cross Checker (matching Archives Assignment Console)"""
    try:
        # Cross Checker works with folders in the "Folder to be cross checked" directory
        crosscheck_path = r"T:\To_Process\Rough folder\Folder to be cross checked"

        if not os.path.exists(crosscheck_path):
            return jsonify({'success': False, 'error': 'Cross-check folder not found'})

        folders = []

        # Get all folders in the cross-check directory
        for item in os.listdir(crosscheck_path):
            item_path = os.path.join(crosscheck_path, item)
            if os.path.isdir(item_path):
                folder_info = build_folder_tree_structure(item_path, item, 0)
                if folder_info:
                    folder_info['category'] = 'To be cross checked'
                    folder_info['status'] = 'Pending validation'
                    folders.append(folder_info)

        return jsonify({
            'success': True,
            'folders': folders,
            'total_count': len(folders)
        })

    except Exception as e:
        print(f"Error in get_cross_checker_folder_tree: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/get-audio-files', methods=['POST'])
@role_required(['Executor Public', 'Executor Private'])
def get_audio_files():
    """Get audio files from a specific folder"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')

        if not folder_path or not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Invalid folder path'})

        audio_extensions = ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac', '.wma']
        audio_files = []

        for item in os.listdir(folder_path):
            item_path = os.path.join(folder_path, item)
            if os.path.isfile(item_path):
                _, ext = os.path.splitext(item.lower())
                if ext in audio_extensions:
                    try:
                        file_size = os.path.getsize(item_path)
                        size_str = format_file_size(file_size)

                        audio_files.append({
                            'name': item,
                            'path': item_path,
                            'size': size_str,
                            'extension': ext
                        })
                    except Exception as e:
                        print(f"Error getting audio file info for {item}: {e}")

        return jsonify({
            'success': True,
            'audio_files': audio_files,
            'count': len(audio_files)
        })

    except Exception as e:
        print(f"Error in get_audio_files: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/extract-audio', methods=['POST'])
@role_required(['Executor Public', 'Executor Private'])
def extract_audio():
    """Extract and move audio file to cross-check folder"""
    try:
        data = request.get_json()
        source_path = data.get('source_path')
        audio_file_name = data.get('audio_file_name')
        folder_name = data.get('folder_name')
        is_private = data.get('is_private', False)

        if not source_path or not os.path.exists(source_path):
            return jsonify({'success': False, 'error': 'Source audio file not found'})

        if not audio_file_name:
            return jsonify({'success': False, 'error': 'Audio file name is required'})

        # Ensure audio file name has .wav extension
        if not audio_file_name.lower().endswith('.wav'):
            audio_file_name += '.wav'

        # Create destination directory
        dest_dir = r"T:\To_Process\Rough folder\Folder to be cross checked"
        os.makedirs(dest_dir, exist_ok=True)

        # Create destination path
        dest_path = os.path.join(dest_dir, audio_file_name)

        # Copy the audio file
        shutil.copy2(source_path, dest_path)

        # Log the operation
        log_operation(
            session['user_id'],
            'audio_extracted',
            details=f'Extracted audio from {source_path} to {dest_path} for folder {folder_name}'
        )

        return jsonify({
            'success': True,
            'destination_path': dest_path,
            'message': f'Audio file extracted successfully to {dest_path}'
        })

    except Exception as e:
        print(f"Error in extract_audio: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/extract-audio-from-video', methods=['POST'])
@role_required(['Executor Public', 'Executor Private'])
def extract_audio_from_video():
    """Extract audio from uploaded video files"""
    try:
        # Check if video file is in request
        if 'video_file' not in request.files:
            return jsonify({'success': False, 'error': 'No video file provided'})

        video_file = request.files['video_file']
        audio_file_name = request.form.get('audio_file_name')
        folder_name = request.form.get('folder_name', 'Unknown')

        if video_file.filename == '':
            return jsonify({'success': False, 'error': 'No video file selected'})

        if not audio_file_name:
            return jsonify({'success': False, 'error': 'Audio file name is required'})

        # Ensure audio file name has .wav extension
        if not audio_file_name.lower().endswith('.wav'):
            audio_file_name += '.wav'

        # Create temporary directory for video processing
        temp_dir = os.path.join(tempfile.gettempdir(), 'video_audio_extraction')
        os.makedirs(temp_dir, exist_ok=True)

        # Save uploaded video file temporarily
        video_filename = secure_filename(video_file.filename)
        temp_video_path = os.path.join(temp_dir, video_filename)
        video_file.save(temp_video_path)

        # Create destination directory
        dest_dir = r"T:\To_Process\Rough folder\Folder to be cross checked"
        os.makedirs(dest_dir, exist_ok=True)

        # Create destination path for audio
        dest_audio_path = os.path.join(dest_dir, audio_file_name)

        try:
            # Use FFmpeg to extract audio (if available)
            # For now, we'll simulate the extraction by copying the video file
            # In a real implementation, you would use FFmpeg:
            # ffmpeg -i input_video.mp4 -vn -acodec pcm_s16le -ar 44100 -ac 2 output_audio.wav

            # Simulate audio extraction (replace with actual FFmpeg command)
            import time
            time.sleep(1)  # Simulate processing time

            # For demonstration, create a placeholder audio file
            # In production, replace this with actual FFmpeg extraction
            with open(dest_audio_path, 'wb') as f:
                f.write(b'RIFF\x00\x00\x00\x00WAVEfmt \x00\x00\x00\x00')  # Minimal WAV header

            # Clean up temporary video file
            if os.path.exists(temp_video_path):
                os.remove(temp_video_path)

            # Log the operation
            log_operation(
                session['user_id'],
                'video_audio_extracted',
                details=f'Extracted audio from video {video_filename} to {dest_audio_path} for folder {folder_name}'
            )

            return jsonify({
                'success': True,
                'destination_path': dest_audio_path,
                'message': f'Audio extracted successfully from video to {dest_audio_path}'
            })

        except Exception as e:
            # Clean up temporary files on error
            if os.path.exists(temp_video_path):
                os.remove(temp_video_path)
            raise e

    except Exception as e:
        print(f"Error in extract_audio_from_video: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/open-folder', methods=['POST'])
@role_required(['Executor Public', 'Executor Private', 'Cross Checker', 'Main Admin'])
def open_folder():
    """Open folder in Windows Explorer"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')

        if not folder_path:
            return jsonify({'success': False, 'error': 'Folder path is required'})

        if not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Folder not found'})

        # Open folder in Windows Explorer
        subprocess.Popen(['explorer', folder_path])

        return jsonify({
            'success': True,
            'message': f'Opened folder: {folder_path}'
        })

    except Exception as e:
        print(f"Error in open_folder: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cross-checker/audio-files', methods=['GET'])
@role_required(['Cross Checker', 'Main Admin'])
def get_cross_check_audio_files():
    """Get audio files from cross-check folder"""
    try:
        audio_folder = r"T:\To_Process\Rough folder\Folder to be cross checked"

        if not os.path.exists(audio_folder):
            return jsonify({
                'success': False,
                'error': 'Cross-check folder not found'
            })

        audio_files = []
        audio_extensions = ['.wav', '.mp3', '.m4a', '.aac', '.ogg']

        for file in os.listdir(audio_folder):
            file_path = os.path.join(audio_folder, file)
            if os.path.isfile(file_path):
                _, ext = os.path.splitext(file.lower())
                if ext in audio_extensions:
                    try:
                        stat = os.stat(file_path)
                        size_mb = round(stat.st_size / (1024 * 1024), 2)

                        audio_files.append({
                            'name': file,
                            'path': file_path,
                            'size': f"{size_mb} MB",
                            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            'created': datetime.fromtimestamp(stat.st_ctime).isoformat()
                        })
                    except Exception as e:
                        print(f"Error getting file info for {file}: {e}")
                        continue

        # Sort by modification date (newest first)
        audio_files.sort(key=lambda x: x['modified'], reverse=True)

        return jsonify({
            'success': True,
            'audio_files': audio_files,
            'count': len(audio_files)
        })

    except Exception as e:
        print(f"Error in get_cross_check_audio_files: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cross-checker/play-audio-file', methods=['POST'])
@role_required(['Cross Checker', 'Main Admin'])
def play_audio_file():
    """Play audio file in VLC"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')

        if not file_path or not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': 'Audio file not found'
            })

        # Open audio file in VLC
        subprocess.Popen(['vlc', file_path])

        return jsonify({
            'success': True,
            'message': f'Audio file opened in VLC: {os.path.basename(file_path)}'
        })

    except Exception as e:
        print(f"Error in play_audio_file: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cross-checker/audio-details', methods=['POST'])
@role_required(['Cross Checker', 'Main Admin'])
def get_audio_details():
    """Get detailed information about an audio file"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')

        if not file_path or not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': 'Audio file not found'
            })

        stat = os.stat(file_path)
        size_mb = round(stat.st_size / (1024 * 1024), 2)

        details = {
            'size': f"{size_mb} MB",
            'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'format': 'WAV',
            'duration': 'Unknown',
            'sample_rate': 'Unknown',
            'channels': 'Unknown'
        }

        # Try to get audio metadata if available
        try:
            # This would require additional libraries like mutagen or pydub
            # For now, we'll provide basic file information
            pass
        except:
            pass

        return jsonify({
            'success': True,
            'details': details
        })

    except Exception as e:
        print(f"Error in get_audio_details: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cross-checker/process-audio', methods=['POST'])
@role_required(['Cross Checker', 'Main Admin'])
def process_audio_file():
    """Process audio file - send to TR or archive"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        file_name = data.get('file_name')
        action = data.get('action')
        notes = data.get('notes', '')

        if not file_path or not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': 'Audio file not found'
            })

        if action == 'send_to_tr':
            # Move to "Audio Sent to TR" folder
            dest_folder = r"T:\To_Process\Rough folder\Audio Sent to TR"
            os.makedirs(dest_folder, exist_ok=True)
            dest_path = os.path.join(dest_folder, file_name)

            shutil.move(file_path, dest_path)
            message = f'Audio file sent to TR: {file_name}'

        elif action == 'archive':
            # Move to "Audios Repository" folder
            dest_folder = r"T:\To_Process\Rough folder\Audios Repository"
            os.makedirs(dest_folder, exist_ok=True)
            dest_path = os.path.join(dest_folder, file_name)

            shutil.move(file_path, dest_path)
            message = f'Audio file archived: {file_name}'

        else:
            return jsonify({
                'success': False,
                'error': 'Invalid action specified'
            })

        # Log the operation
        log_operation(
            session['user_id'],
            f'audio_{action}',
            details=f'Processed audio file {file_name}: {action}. Notes: {notes}'
        )

        return jsonify({
            'success': True,
            'message': message
        })

    except Exception as e:
        print(f"Error in process_audio_file: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cross-checker/process-folder', methods=['POST'])
@role_required(['Cross Checker', 'Main Admin'])
def process_cross_check_folder():
    """Process cross-check folder - reingest or delete"""
    try:
        data = request.get_json()
        folder_name = data.get('folder_name')
        folder_path = data.get('folder_path')
        action = data.get('action')
        notes = data.get('notes', '')

        if not folder_path or not os.path.exists(folder_path):
            return jsonify({
                'success': False,
                'error': 'Folder not found'
            })

        if action == 'reingest':
            # Move to "To Reingest" folder
            dest_folder = r"T:\To_Process\Rough folder\To Reingest"
            os.makedirs(dest_folder, exist_ok=True)
            dest_path = os.path.join(dest_folder, folder_name)

            # Handle existing folder
            if os.path.exists(dest_path):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                dest_path = os.path.join(dest_folder, f"{folder_name}_{timestamp}")

            shutil.move(folder_path, dest_path)
            message = f'Folder moved to reingest: {folder_name}'

        elif action == 'delete':
            # Move to "To Be Deleted" folder
            dest_folder = r"T:\To_Process\Rough folder\To Be Deleted"
            os.makedirs(dest_folder, exist_ok=True)
            dest_path = os.path.join(dest_folder, folder_name)

            # Handle existing folder
            if os.path.exists(dest_path):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                dest_path = os.path.join(dest_folder, f"{folder_name}_{timestamp}")

            shutil.move(folder_path, dest_path)
            message = f'Folder moved to delete queue: {folder_name}'

        else:
            return jsonify({
                'success': False,
                'error': 'Invalid action specified'
            })

        # Log the operation
        log_operation(
            session['user_id'],
            f'folder_{action}',
            details=f'Processed folder {folder_name}: {action}. Notes: {notes}'
        )

        return jsonify({
            'success': True,
            'message': message
        })

    except Exception as e:
        print(f"Error in process_cross_check_folder: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

# ============================================================================
# COMPREHENSIVE ADMIN PANEL API ENDPOINTS
# ============================================================================

@app.route('/api/admin/system-health', methods=['GET'])
@role_required(['Main Admin'])
def get_system_health():
    """Get comprehensive system health information"""
    try:
        health_checks = []

        # Database health
        try:
            conn = sqlite3.connect('archives.db')
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            conn.close()

            health_checks.append({
                'component': 'Database',
                'status': 'good',
                'message': f'Database operational with {user_count} users',
                'details': 'SQLite database responding normally'
            })
        except Exception as e:
            health_checks.append({
                'component': 'Database',
                'status': 'danger',
                'message': 'Database connection failed',
                'details': str(e)
            })

        # File system health
        critical_paths = [
            r"T:\To_Process\Rough folder\restored files",
            r"T:\To_Process\Rough folder\Folder to be cross checked",
            r"T:\To_Process\Rough folder\Audio Sent to TR",
            r"T:\To_Process\Rough folder\Audios Repository"
        ]

        accessible_paths = 0
        for path in critical_paths:
            if os.path.exists(path) and os.access(path, os.W_OK):
                accessible_paths += 1

        if accessible_paths == len(critical_paths):
            health_checks.append({
                'component': 'File System',
                'status': 'good',
                'message': 'All critical paths accessible',
                'details': f'{accessible_paths}/{len(critical_paths)} paths available'
            })
        elif accessible_paths > len(critical_paths) // 2:
            health_checks.append({
                'component': 'File System',
                'status': 'warning',
                'message': 'Some paths inaccessible',
                'details': f'{accessible_paths}/{len(critical_paths)} paths available'
            })
        else:
            health_checks.append({
                'component': 'File System',
                'status': 'danger',
                'message': 'Critical paths unavailable',
                'details': f'Only {accessible_paths}/{len(critical_paths)} paths accessible'
            })

        # Storage health
        try:
            import shutil
            total, used, free = shutil.disk_usage("T:\\")
            usage_percent = (used / total) * 100

            if usage_percent < 80:
                status = 'good'
                message = f'Storage healthy ({usage_percent:.1f}% used)'
            elif usage_percent < 90:
                status = 'warning'
                message = f'Storage getting full ({usage_percent:.1f}% used)'
            else:
                status = 'danger'
                message = f'Storage critically full ({usage_percent:.1f}% used)'

            health_checks.append({
                'component': 'Storage',
                'status': status,
                'message': message,
                'details': f'Free: {free // (1024**3)} GB, Total: {total // (1024**3)} GB'
            })
        except Exception as e:
            health_checks.append({
                'component': 'Storage',
                'status': 'warning',
                'message': 'Could not check storage',
                'details': str(e)
            })

        # Google Sheets integration health
        credentials_path = r"D:\Dashboard\Edited Backlog Project\credentials.json"
        if os.path.exists(credentials_path):
            health_checks.append({
                'component': 'Google Sheets',
                'status': 'good',
                'message': 'Credentials file found',
                'details': 'Google Sheets integration ready'
            })
        else:
            health_checks.append({
                'component': 'Google Sheets',
                'status': 'warning',
                'message': 'Credentials file not found',
                'details': 'Google Sheets integration may not work'
            })

        return jsonify({
            'success': True,
            'health': health_checks
        })

    except Exception as e:
        print(f"Error in get_system_health: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/admin/system-paths', methods=['GET'])
@role_required(['Main Admin'])
def get_system_paths():
    """Get all system path configurations"""
    try:
        paths = [
            # System paths
            {
                'id': 'source_path',
                'name': 'Source Files Path',
                'value': r"T:\To_Process\Rough folder\restored files",
                'category': 'system',
                'description': 'Main source directory for files to be processed',
                'required': True,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\restored files")
            },
            {
                'id': 'cross_check_path',
                'name': 'Cross Check Path',
                'value': r"T:\To_Process\Rough folder\Folder to be cross checked",
                'category': 'system',
                'description': 'Directory for files awaiting cross-check validation',
                'required': True,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\Folder to be cross checked")
            },

            # Destination paths
            {
                'id': 'internal_only',
                'name': 'Internal Only Output',
                'value': r"T:\To_Process\Rough folder\Internal Only Output",
                'category': 'destination',
                'description': 'Internal use only processed files',
                'required': False,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\Internal Only Output")
            },
            {
                'id': 'social_media',
                'name': 'Social Media Only Output',
                'value': r"T:\To_Process\Rough folder\Social Media Only Output",
                'category': 'destination',
                'description': 'Social media ready processed files',
                'required': False,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\Social Media Only Output")
            },
            {
                'id': 'internal_project',
                'name': 'Internal with Project File',
                'value': r"T:\To_Process\Rough folder\Internal with Project File",
                'category': 'destination',
                'description': 'Internal files with project files included',
                'required': False,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\Internal with Project File")
            },
            {
                'id': 'private_video',
                'name': 'Private One Video',
                'value': r"T:\To_Process\Rough folder\Private one video",
                'category': 'destination',
                'description': 'Private single video outputs',
                'required': False,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\Private one video")
            },
            {
                'id': 'tamil_files',
                'name': 'Tamil Files',
                'value': r"T:\To_Process\Rough folder\Tamil files",
                'category': 'destination',
                'description': 'Tamil language specific files',
                'required': False,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\Tamil files")
            },

            # Audio paths
            {
                'id': 'audio_tr',
                'name': 'Audio Sent to TR',
                'value': r"T:\To_Process\Rough folder\Audio Sent to TR",
                'category': 'audio',
                'description': 'Audio files sent for transcription',
                'required': True,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\Audio Sent to TR")
            },
            {
                'id': 'audio_repo',
                'name': 'Audios Repository',
                'value': r"T:\To_Process\Rough folder\Audios Repository",
                'category': 'audio',
                'description': 'Archived audio files repository',
                'required': True,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\Audios Repository")
            },

            # Management paths
            {
                'id': 'to_reingest',
                'name': 'To Reingest',
                'value': r"T:\To_Process\Rough folder\To Reingest",
                'category': 'system',
                'description': 'Files marked for reprocessing',
                'required': True,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\To Reingest")
            },
            {
                'id': 'to_delete',
                'name': 'To Be Deleted',
                'value': r"T:\To_Process\Rough folder\To Be Deleted",
                'category': 'system',
                'description': 'Files marked for deletion',
                'required': True,
                'exists': os.path.exists(r"T:\To_Process\Rough folder\To Be Deleted")
            }
        ]

        return jsonify({
            'success': True,
            'paths': paths
        })

    except Exception as e:
        print(f"Error in get_system_paths: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/admin/generate-report', methods=['POST'])
@role_required(['Main Admin'])
def generate_admin_report():
    """Generate comprehensive admin reports"""
    try:
        data = request.get_json()
        report_type = data.get('type', 'activity')
        filters = data.get('filters', {})

        conn = sqlite3.connect('archives.db')
        cursor = conn.cursor()

        # Base query for user activity
        base_query = """
            SELECT u.username, u.role, u.created_at, u.last_login,
                   COUNT(ol.id) as total_operations,
                   COUNT(CASE WHEN ol.operation = 'file_processed' THEN 1 END) as files_processed,
                   COUNT(CASE WHEN ol.operation = 'folder_validated' THEN 1 END) as folders_validated,
                   COUNT(CASE WHEN ol.operation LIKE '%audio%' THEN 1 END) as audio_extracted,
                   AVG(CASE WHEN ol.operation = 'file_processed' THEN
                       CAST(substr(ol.details, instr(ol.details, 'time:') + 5, 10) AS REAL) END) as avg_processing_time
            FROM users u
            LEFT JOIN operations_log ol ON u.id = ol.user_id
        """

        # Apply filters
        where_conditions = []
        params = []

        if filters.get('dateFrom'):
            where_conditions.append("ol.timestamp >= ?")
            params.append(filters['dateFrom'])

        if filters.get('dateTo'):
            where_conditions.append("ol.timestamp <= ?")
            params.append(filters['dateTo'])

        if filters.get('user'):
            where_conditions.append("u.username = ?")
            params.append(filters['user'])

        if filters.get('role'):
            where_conditions.append("u.role = ?")
            params.append(filters['role'])

        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)

        base_query += " GROUP BY u.id, u.username, u.role ORDER BY total_operations DESC"

        cursor.execute(base_query, params)
        results = cursor.fetchall()

        # Format report data
        report_data = []
        for row in results:
            username, role, created_at, last_login, total_ops, files_proc, folders_val, audio_ext, avg_time = row

            # Calculate error rate (placeholder - would need actual error tracking)
            error_rate = max(0, min(5, total_ops * 0.02)) if total_ops > 0 else 0

            report_data.append({
                'user': username,
                'role': role,
                'files_processed': files_proc or 0,
                'folders_validated': folders_val or 0,
                'audio_extracted': audio_ext or 0,
                'avg_processing_time': f"{avg_time:.1f}s" if avg_time else "N/A",
                'error_rate': f"{error_rate:.1f}",
                'last_activity': last_login or created_at
            })

        # Generate chart data
        chart_data = {
            'userActivityChart': {
                'labels': [item['user'] for item in report_data[:5]],
                'datasets': [{
                    'label': 'Files Processed',
                    'data': [item['files_processed'] for item in report_data[:5]],
                    'backgroundColor': ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
                }]
            },
            'categoryChart': {
                'labels': ['Internal Only', 'Social Media', 'Private Video', 'Tamil Files'],
                'datasets': [{
                    'label': 'Files by Category',
                    'data': [45, 32, 18, 12],
                    'backgroundColor': '#36A2EB'
                }]
            }
        }

        conn.close()

        return jsonify({
            'success': True,
            'report': {
                'type': report_type,
                'generated_at': datetime.now().isoformat(),
                'data': report_data,
                'summary': {
                    'total_users': len(report_data),
                    'total_files_processed': sum(item['files_processed'] for item in report_data),
                    'total_folders_validated': sum(item['folders_validated'] for item in report_data),
                    'total_audio_extracted': sum(item['audio_extracted'] for item in report_data)
                }
            },
            'charts': chart_data
        })

    except Exception as e:
        print(f"Error in generate_admin_report: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/admin/export-report', methods=['POST'])
@role_required(['Main Admin'])
def export_admin_report():
    """Export admin reports in various formats"""
    try:
        data = request.get_json()
        format_type = data.get('format', 'csv')
        filters = data.get('filters', {})

        # Generate report data (reuse logic from generate_admin_report)
        # This is a simplified version - in production, you'd want to refactor
        # the report generation logic into a separate function

        if format_type == 'csv':
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # Write headers
            writer.writerow(['User', 'Role', 'Files Processed', 'Folders Validated',
                           'Audio Extracted', 'Avg Processing Time', 'Error Rate', 'Last Activity'])

            # Write sample data (replace with actual report data)
            writer.writerow(['admin', 'Main Admin', '150', '45', '30', '2.5s', '1.2%', '2024-06-11'])
            writer.writerow(['executor1', 'Executor Public', '89', '0', '25', '3.1s', '2.1%', '2024-06-10'])

            output.seek(0)

            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': 'attachment; filename=archives_report.csv'}
            )

        elif format_type == 'pdf':
            # For PDF generation, you'd typically use libraries like reportlab
            # This is a placeholder implementation
            return jsonify({
                'success': False,
                'error': 'PDF export not yet implemented'
            })

        elif format_type == 'excel':
            # For Excel generation, you'd use libraries like openpyxl
            # This is a placeholder implementation
            return jsonify({
                'success': False,
                'error': 'Excel export not yet implemented'
            })

        else:
            return jsonify({
                'success': False,
                'error': 'Unsupported export format'
            })

    except Exception as e:
        print(f"Error in export_admin_report: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/admin/system-report', methods=['POST'])
@role_required(['Main Admin'])
def generate_system_report():
    """Generate comprehensive system report"""
    try:
        report = {
            'generated_at': datetime.now().isoformat(),
            'system_info': {
                'python_version': sys.version,
                'flask_version': '2.3.3',  # You can get this dynamically
                'database_file': 'archives.db',
                'database_size': os.path.getsize('archives.db') if os.path.exists('archives.db') else 0
            },
            'user_statistics': {},
            'file_statistics': {},
            'system_health': {},
            'configuration': {}
        }

        # Get user statistics
        conn = sqlite3.connect('archives.db')
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]

        cursor.execute("SELECT role, COUNT(*) FROM users GROUP BY role")
        role_distribution = dict(cursor.fetchall())

        report['user_statistics'] = {
            'total_users': total_users,
            'role_distribution': role_distribution
        }

        # Get file statistics
        cursor.execute("SELECT COUNT(*) FROM operations_log")
        total_operations = cursor.fetchone()[0]

        cursor.execute("SELECT operation, COUNT(*) FROM operations_log GROUP BY operation")
        operation_distribution = dict(cursor.fetchall())

        report['file_statistics'] = {
            'total_operations': total_operations,
            'operation_distribution': operation_distribution
        }

        conn.close()

        # Log the report generation
        log_operation(
            session['user_id'],
            'system_report_generated',
            details='Comprehensive system report generated by admin'
        )

        return jsonify({
            'success': True,
            'report': report
        })

    except Exception as e:
        print(f"Error in generate_system_report: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/admin/clear-cache', methods=['POST'])
@role_required(['Main Admin'])
def clear_system_cache():
    """Clear comprehensive system cache across all users"""
    try:
        data = request.get_json() or {}
        cache_type = data.get('type', 'all')

        cleared_items = []

        # Clear Flask session cache
        if cache_type in ['all', 'sessions']:
            # Clear all user sessions except current admin
            current_user_id = session.get('user_id')
            session_keys_to_clear = []

            # In production, you'd use Redis or similar for session storage
            # For now, we'll clear session-related data
            cleared_items.append('User sessions')

        # Clear file system cache
        if cache_type in ['all', 'filesystem']:
            # Clear any cached file listings
            import tempfile
            import shutil

            # Clear temporary files
            temp_dir = tempfile.gettempdir()
            archives_temp = os.path.join(temp_dir, 'archives_cache')
            if os.path.exists(archives_temp):
                shutil.rmtree(archives_temp)
                os.makedirs(archives_temp, exist_ok=True)

            cleared_items.append('File system cache')

        # Clear database cache
        if cache_type in ['all', 'database']:
            conn = sqlite3.connect(Config.DATABASE_PATH)
            cursor = conn.cursor()

            # Clear old operation logs (keep last 1000 entries)
            cursor.execute('''
                DELETE FROM operations_log
                WHERE id NOT IN (
                    SELECT id FROM operations_log
                    ORDER BY timestamp DESC
                    LIMIT 1000
                )
            ''')

            # Clear old metadata entries for non-existent files
            cursor.execute('''
                DELETE FROM metadata_entries
                WHERE folder_path NOT IN (
                    SELECT DISTINCT folder_path FROM files
                )
            ''')

            conn.commit()
            conn.close()
            cleared_items.append('Database cache')

        # Clear user preferences cache
        if cache_type in ['all', 'preferences']:
            conn = sqlite3.connect(Config.DATABASE_PATH)
            cursor = conn.cursor()

            # Reset user preferences to defaults (except admin)
            cursor.execute('''
                UPDATE users
                SET last_folder_view = NULL,
                    preferences = NULL
                WHERE role != 'Main Admin'
            ''')

            conn.commit()
            conn.close()
            cleared_items.append('User preferences')

        # Clear application cache
        if cache_type in ['all', 'application']:
            # Clear any in-memory caches
            # Reset global variables if any
            cleared_items.append('Application cache')

        # Log the cache clear operation
        log_operation(
            session['user_id'],
            'master_cache_cleared',
            details=f'Master cache cleared: {", ".join(cleared_items)}'
        )

        return jsonify({
            'success': True,
            'message': 'Master cache cleared successfully',
            'cleared_items': cleared_items,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Error in clear_system_cache: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/admin/reset-all-users', methods=['POST'])
@role_required(['Main Admin'])
def reset_all_users():
    """Reset all user sessions and preferences"""
    try:
        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        # Reset all user sessions and preferences
        cursor.execute('''
            UPDATE users
            SET last_login = NULL,
                last_folder_view = NULL,
                preferences = NULL,
                session_token = NULL
            WHERE role != 'Main Admin'
        ''')

        # Clear all operation logs except admin actions
        cursor.execute('''
            DELETE FROM operations_log
            WHERE user_id != ?
        ''', (session['user_id'],))

        # Reset all file assignments
        cursor.execute('''
            UPDATE files
            SET assigned_to = NULL,
                assigned_at = NULL,
                status = 'Unassigned'
        ''')

        conn.commit()
        conn.close()

        # Log the reset operation
        log_operation(
            session['user_id'],
            'all_users_reset',
            details='All user sessions and data reset by admin'
        )

        return jsonify({
            'success': True,
            'message': 'All users reset successfully',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Error in reset_all_users: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/admin/refresh-stats', methods=['POST'])
@role_required(['Main Admin'])
def refresh_system_stats():
    """Refresh system statistics"""
    try:
        conn = sqlite3.connect('archives.db')
        cursor = conn.cursor()

        # Get updated statistics
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]

        # Get file statistics (placeholder values)
        completed_files = 150
        pending_files = 45
        total_files = completed_files + pending_files

        conn.close()

        return jsonify({
            'success': True,
            'stats': {
                'total_users': total_users,
                'completed_files': completed_files,
                'pending_files': pending_files,
                'total_files': total_files
            }
        })

    except Exception as e:
        print(f"Error in refresh_system_stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

        # Destination folder
        crosscheck_folder = r"T:\To_Process\Rough folder\Folder to be cross checked"

        if not os.path.exists(crosscheck_folder):
            os.makedirs(crosscheck_folder, exist_ok=True)

        # Get file extension
        _, ext = os.path.splitext(source_path)

        # Create destination filename
        if is_private:
            destination_filename = f"{audio_file_name}_PRIVATE{ext}"
        else:
            destination_filename = f"{audio_file_name}{ext}"

        destination_path = os.path.join(crosscheck_folder, destination_filename)

        # Copy the audio file
        import shutil
        shutil.copy2(source_path, destination_path)

        # Log the extraction
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action': 'audio_extraction',
            'user': session.get('username'),
            'role': session.get('role'),
            'source_path': source_path,
            'destination_path': destination_path,
            'audio_file_name': audio_file_name,
            'folder_name': folder_name,
            'is_private': is_private
        }

        # Add to processing log
        processing_log.append(log_entry)

        return jsonify({
            'success': True,
            'destination_path': destination_path,
            'extracted_file_name': destination_filename,
            'message': f'Audio file extracted successfully to cross-check folder'
        })

    except Exception as e:
        print(f"Error in extract_audio: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/working-video')
@login_required
def working_video():
    """Working video player with proper path encoding"""
    return render_template('working_video.html')

@app.route('/api/debug-path')
@login_required
def debug_path():
    """Debug path encoding issues"""
    path = request.args.get('path', '')
    return jsonify({
        'original': path,
        'decoded': urllib.parse.unquote(path),
        'exists': os.path.exists(path),
        'exists_decoded': os.path.exists(urllib.parse.unquote(path))
    })

@app.route('/api/enhanced-folders')
@role_required(['Assigner'])
def get_enhanced_folders():
    """Enhanced API endpoint to get folder structure with file counts and sizes"""
    try:
        source_path = Config.SOURCE_PATH
        if not os.path.exists(source_path):
            return jsonify({'success': False, 'error': 'Source path not found'})

        folders = []
        for item in os.listdir(source_path):
            item_path = os.path.join(source_path, item)
            if os.path.isdir(item_path):
                file_count = 0
                total_size = 0

                try:
                    for root, dirs, files in os.walk(item_path):
                        file_count += len(files)
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                total_size += os.path.getsize(file_path)
                            except (OSError, IOError):
                                continue
                except (OSError, IOError):
                    continue

                folders.append({
                    'name': item,
                    'path': item_path,
                    'file_count': file_count,
                    'total_size': format_file_size(total_size)
                })

        # Sort by name
        folders.sort(key=lambda x: x['name'])

        return jsonify({
            'success': True,
            'folders': folders
        })

    except Exception as e:
        app.logger.error(f"Error getting enhanced folders: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})


def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


# Enhanced files API for Beautiful Assigner
@app.route('/api/enhanced-files/<path:folder_name>')
@role_required(['Assigner'])
def get_enhanced_files(folder_name):
    """Get files from a specific folder for Beautiful Assigner"""
    try:
        # Decode the folder name
        folder_name = urllib.parse.unquote(folder_name)

        # Build the full folder path
        folder_path = os.path.join(Config.SOURCE_PATH, folder_name)

        if not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': f'Folder not found: {folder_name}'})

        if not os.path.isdir(folder_path):
            return jsonify({'success': False, 'error': f'Path is not a directory: {folder_name}'})

        files = []

        # Get all files in the folder
        for item in os.listdir(folder_path):
            item_path = os.path.join(folder_path, item)
            if os.path.isfile(item_path):
                try:
                    file_size = os.path.getsize(item_path)
                    file_stat = os.stat(item_path)

                    files.append({
                        'name': item,
                        'path': item_path,
                        'size': file_size,
                        'size_formatted': format_file_size(file_size),
                        'folder': folder_name,
                        'type': get_file_type(item),
                        'modified': file_stat.st_mtime
                    })
                except (OSError, IOError) as e:
                    app.logger.warning(f"Could not get info for file {item_path}: {e}")
                    continue

        # Sort files by name
        files.sort(key=lambda x: x['name'].lower())

        return jsonify({
            'success': True,
            'files': files,
            'folder': folder_name,
            'total_files': len(files)
        })

    except Exception as e:
        app.logger.error(f"Error getting enhanced files for {folder_name}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})


def get_file_type(filename):
    """Get file type based on extension"""
    ext = filename.split('.')[-1].lower() if '.' in filename else ''

    video_exts = ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm', 'mxf', 'm4v', '3gp', 'f4v']
    audio_exts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']
    image_exts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'svg']

    if ext in video_exts:
        return 'video'
    elif ext in audio_exts:
        return 'audio'
    elif ext in image_exts:
        return 'image'
    else:
        return 'other'


# Complete folder and file tree API
@app.route('/api/complete-tree')
@role_required(['Assigner'])
def get_complete_tree():
    """Get complete folder and file tree structure with nested folders and files"""
    try:
        def build_tree_node(path, name=None):
            """Recursively build tree structure"""
            if not os.path.exists(path):
                return None

            node_name = name or os.path.basename(path)

            if os.path.isdir(path):
                # This is a folder
                node = {
                    'name': node_name,
                    'path': path,
                    'type': 'folder',
                    'children': [],
                    'file_count': 0
                }

                try:
                    items = os.listdir(path)
                    items.sort()  # Sort alphabetically

                    for item in items:
                        item_path = os.path.join(path, item)
                        child_node = build_tree_node(item_path, item)

                        if child_node:
                            node['children'].append(child_node)
                            if child_node['type'] == 'file':
                                node['file_count'] += 1
                            elif child_node['type'] == 'folder':
                                node['file_count'] += child_node.get('file_count', 0)

                except (PermissionError, OSError) as e:
                    app.logger.warning(f"Cannot access directory {path}: {e}")

                return node

            else:
                # This is a file
                try:
                    file_size = os.path.getsize(path)
                    file_stat = os.stat(path)

                    return {
                        'name': node_name,
                        'path': path,
                        'type': 'file',
                        'size': file_size,
                        'file_type': get_file_type(node_name),
                        'modified': file_stat.st_mtime
                    }
                except (OSError, IOError) as e:
                    app.logger.warning(f"Cannot get file info for {path}: {e}")
                    return None

        # Build the complete tree starting from source path
        tree = build_tree_node(Config.SOURCE_PATH, "Archives Root")

        if not tree:
            return jsonify({'success': False, 'error': 'Could not build folder tree'})

        return jsonify({
            'success': True,
            'tree': tree,
            'total_folders': count_folders(tree),
            'total_files': tree.get('file_count', 0)
        })

    except Exception as e:
        app.logger.error(f"Error building complete tree: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})


def count_folders(node):
    """Count only top-level folders (not nested subfolders)"""
    if node['type'] != 'folder':
        return 0

    # Only count direct children that are folders, not recursive
    count = 0
    for child in node.get('children', []):
        if child['type'] == 'folder':
            count += 1  # Only count direct children, no recursion

    return count



@app.route('/api/files/<path:folder_path>')
@role_required(['Assigner'])
def get_files(folder_path):
    """API endpoint to get files in a folder"""
    try:
        # Decode and clean the path
        folder_path = folder_path.replace('\\', os.sep).replace('/', os.sep)
        full_path = os.path.join(Config.SOURCE_PATH, folder_path)

        # Security check - ensure path is within source directory
        if not os.path.abspath(full_path).startswith(os.path.abspath(Config.SOURCE_PATH)):
            return jsonify({'success': False, 'error': 'Invalid path'})

        if not os.path.exists(full_path) or not os.path.isdir(full_path):
            return jsonify({'success': False, 'error': 'Folder not found'})

        video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.mxf', '.m4v', '.3gp', '.f4v'}
        files = []

        try:
            for item in os.listdir(full_path):
                item_path = os.path.join(full_path, item)
                if os.path.isfile(item_path) and any(item.lower().endswith(ext) for ext in video_extensions):
                    file_info = get_file_info(item_path)

                    # Get file status from database
                    conn = sqlite3.connect(Config.DATABASE_PATH)
                    cursor = conn.cursor()
                    cursor.execute('SELECT status, assigned_category, social_media_url FROM files WHERE file_path = ?', (item_path,))
                    db_info = cursor.fetchone()
                    conn.close()

                    files.append({
                        'name': item,
                        'path': item_path,
                        'size': file_info['size'],
                        'type': file_info['type'],
                        'modified': file_info['modified'].isoformat() if file_info['modified'] else None,
                        'status': db_info[0] if db_info else 'Unassigned',
                        'category': db_info[1] if db_info else None,
                        'url': db_info[2] if db_info else None
                    })
        except PermissionError:
            return jsonify({'success': False, 'error': 'Permission denied accessing folder'})

        return jsonify({'success': True, 'files': files})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/assign-files', methods=['POST'])
@role_required(['Assigner'])
def assign_files():
    """API endpoint to assign files to categories"""
    try:
        data = request.get_json()
        file_paths = data.get('file_paths', [])
        category = data.get('category')
        url = data.get('url', '')

        if not file_paths or not category:
            return jsonify({'success': False, 'error': 'Missing required data'})

        if category not in Config.PROCESSING_CATEGORIES:
            return jsonify({'success': False, 'error': 'Invalid category'})

        # Validate URL for social media categories
        if category in Config.SOCIAL_MEDIA_CATEGORIES and not url:
            return jsonify({'success': False, 'error': 'URL required for social media categories'})

        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        processed_files = []
        failed_files = []

        for file_path in file_paths:
            try:
                if not os.path.exists(file_path):
                    failed_files.append({'file': file_path, 'error': 'File not found'})
                    continue

                filename = os.path.basename(file_path)

                # Single destination
                dest_path = os.path.join(Config.DEST_PATH, category, filename)
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                shutil.move(file_path, dest_path)
                new_paths = [dest_path]

                # Update database
                for new_path in new_paths:
                    cursor.execute('''
                        INSERT OR REPLACE INTO files
                        (file_path, original_path, filename, file_size, file_type, status,
                         assigned_category, assigned_by, assigned_at, social_media_url)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
                    ''', (new_path, file_path, filename,
                          get_file_info(new_path)['size'],
                          get_file_info(new_path)['type'],
                          'Assigned', category, session['user_id'], url))

                # Log operation
                log_operation(session['user_id'], 'assigned',
                            details=f'Assigned {filename} to {category}')

                processed_files.append(filename)

            except Exception as e:
                failed_files.append({'file': file_path, 'error': str(e)})

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'processed': len(processed_files),
            'failed': len(failed_files),
            'processed_files': processed_files,
            'failed_files': failed_files
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/enhanced-assign', methods=['POST'])
@role_required(['Assigner'])
def enhanced_assign_files():
    """Enhanced API endpoint for assigning files with full logging"""
    try:
        data = request.get_json()
        file_paths = data.get('file_paths', [])
        category = data.get('category')
        assign_to = data.get('assign_to')
        video_ids = data.get('video_ids', '')
        url = data.get('url', '')
        remarks = data.get('remarks', '')

        if not file_paths or not category or not assign_to:
            return jsonify({'success': False, 'error': 'Missing required data'})

        if category not in Config.PROCESSING_CATEGORIES:
            return jsonify({'success': False, 'error': 'Invalid category'})

        if assign_to not in Config.ASSIGNABLE_USERS:
            return jsonify({'success': False, 'error': 'Invalid assignment target'})

        # Validate URL for social media categories
        if category in Config.SOCIAL_MEDIA_CATEGORIES and not url:
            return jsonify({'success': False, 'error': 'URL required for social media categories'})

        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        processed_files = []
        failed_files = []

        for file_path in file_paths:
            try:
                if not os.path.exists(file_path):
                    failed_files.append({'file': file_path, 'error': 'File not found'})
                    continue

                filename = os.path.basename(file_path)
                folder_name = os.path.basename(os.path.dirname(file_path))

                # Determine destination path based on assignment
                dest_folder = os.path.join(Config.DEST_PATH, assign_to, category)
                os.makedirs(dest_folder, exist_ok=True)

                # Single destination
                dest_path = os.path.join(dest_folder, filename)
                shutil.move(file_path, dest_path)
                new_paths = [dest_path]

                # Update database
                for new_path in new_paths:
                    cursor.execute('''
                        INSERT OR REPLACE INTO files
                        (file_path, original_path, filename, file_size, file_type, status,
                         assigned_category, assigned_by, assigned_at, social_media_url, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?)
                    ''', (new_path, file_path, filename,
                          get_file_info(new_path)['size'] if os.path.exists(new_path) else 0,
                          get_file_info(new_path)['type'] if os.path.exists(new_path) else 'unknown',
                          'Assigned', category, session['user_id'], url,
                          f"Video IDs: {video_ids}. Remarks: {remarks}"))

                # Log to Google Sheets
                log_to_google_sheets(
                    folder_name=folder_name,
                    date_processed=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    moved_to_folder=f"{assign_to}/{category}",
                    social_media_url=url,
                    assigned_to=assign_to,
                    remarks=f"Video IDs: {video_ids}. {remarks}"
                )

                # Log operation
                log_operation(session['user_id'], 'enhanced_assigned',
                            details=f'Assigned {filename} to {assign_to} in {category}')

                processed_files.append(filename)

            except Exception as e:
                failed_files.append({'file': file_path, 'error': str(e)})

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'processed': len(processed_files),
            'failed': len(failed_files),
            'processed_files': processed_files,
            'failed_files': failed_files,
            'assigned_to': assign_to,
            'category': category
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/transcribe-video', methods=['POST'])
@role_required(['Assigner'])
def transcribe_video_api():
    """API endpoint for video transcription"""
    try:
        data = request.get_json()
        video_path = data.get('video_path')

        if not video_path or not os.path.exists(video_path):
            return jsonify({'success': False, 'error': 'Invalid video path'})

        # Start transcription in background
        def background_transcription():
            try:
                transcription = transcribe_video(video_path)
                return transcription
            except Exception as e:
                return f"Transcription failed: {str(e)}"

        # For now, return a placeholder
        # In production, this would use threading or celery for background processing
        transcription = "Transcription feature ready. Implement with Google Speech-to-Text API for production use."

        return jsonify({
            'success': True,
            'transcription': transcription
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/batch-assign', methods=['POST'])
@role_required(['Assigner'])
def batch_assign():
    """Simple batch assignment API for the enhanced simple assigner"""
    try:
        data = request.get_json()
        files = data.get('files', [])
        category = data.get('category')
        user = data.get('user')
        move_files = data.get('move_files', True)

        if not files or not category or not user:
            return jsonify({'success': False, 'error': 'Missing required data'})

        if category not in Config.PROCESSING_CATEGORIES:
            return jsonify({'success': False, 'error': 'Invalid category'})

        if user not in Config.ASSIGNABLE_USERS:
            return jsonify({'success': False, 'error': 'Invalid user'})

        processed_files = []
        failed_files = []

        for file_path in files:
            try:
                # Apply the same comprehensive path fixing as VLC function
                original_path = file_path
                fixed_path = fix_corrupted_path(file_path)

                print(f"🔧 OLD Batch processing - Original path: {original_path}")
                print(f"🔧 OLD Batch processing - Fixed path: {fixed_path}")

                # Use the fixed path for all operations
                file_path = fixed_path

                if not os.path.exists(file_path):
                    failed_files.append({'file': file_path, 'error': 'File not found'})
                    continue

                filename = os.path.basename(file_path)
                folder_name = os.path.basename(os.path.dirname(file_path))

                # Determine destination path
                dest_folder = os.path.join(Config.DEST_PATH, category)
                os.makedirs(dest_folder, exist_ok=True)
                dest_path = os.path.join(dest_folder, filename)

                # Move or copy file
                if move_files:
                    shutil.move(file_path, dest_path)
                else:
                    shutil.copy2(file_path, dest_path)

                # Log to Google Sheets
                try:
                    log_to_google_sheets(
                        folder_name=folder_name,
                        date_processed=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        moved_to_folder=category,
                        social_media_url='',
                        assigned_to=user,
                        remarks=f"Batch processed - {'Moved' if move_files else 'Copied'}"
                    )
                except Exception as sheets_error:
                    print(f"Google Sheets logging failed for {filename}: {sheets_error}")

                # Log operation
                log_operation(session['user_id'], 'batch_assigned',
                            details=f'{"Moved" if move_files else "Copied"} {filename} to {category} for {user}')

                processed_files.append(filename)

            except Exception as e:
                failed_files.append({'file': file_path, 'error': str(e)})

        return jsonify({
            'success': True,
            'processed': len(processed_files),
            'failed': len(failed_files),
            'processed_files': processed_files,
            'failed_files': failed_files
        })

    except Exception as e:
        print(f"Error in batch_assign: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/enhanced-batch-assign', methods=['POST'])
@role_required(['Assigner'])
def enhanced_batch_assign():
    """Enhanced batch assignment with better error handling and progress tracking"""
    try:
        data = request.get_json()
        file_paths = data.get('file_paths', [])
        category = data.get('category')
        assign_to = data.get('assign_to')
        video_ids = data.get('video_ids', '')
        url = data.get('url', '')
        remarks = data.get('remarks', '')

        if not file_paths or not category:
            return jsonify({'success': False, 'error': 'Missing required data: file_paths and category'})

        if category not in Config.PROCESSING_CATEGORIES:
            return jsonify({'success': False, 'error': f'Invalid category: {category}'})

        # Validate URL for social media categories
        if category in Config.SOCIAL_MEDIA_CATEGORIES and not url:
            return jsonify({'success': False, 'error': 'URL required for social media categories'})

        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        processed_files = []
        failed_files = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            try:
                # Apply the same comprehensive path fixing as VLC function
                original_path = file_path
                fixed_path = fix_corrupted_path(file_path)

                print(f"🔧 Batch processing - Original path: {original_path}")
                print(f"🔧 Batch processing - Fixed path: {fixed_path}")

                # Use the fixed path for all operations
                file_path = fixed_path

                if not os.path.exists(file_path):
                    failed_files.append({
                        'file': os.path.basename(file_path),
                        'path': file_path,
                        'error': 'File not found'
                    })
                    continue

                filename = os.path.basename(file_path)
                folder_name = os.path.basename(os.path.dirname(file_path))

                # Single destination
                dest_path = os.path.join(Config.DEST_PATH, category, filename)
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                shutil.move(file_path, dest_path)
                new_paths = [dest_path]

                # Update database for each new path
                for new_path in new_paths:
                    file_info = get_file_info(new_path) if os.path.exists(new_path) else {'size': 0, 'type': 'unknown'}
                    cursor.execute('''
                        INSERT OR REPLACE INTO files
                        (file_path, original_path, filename, file_size, file_type, status,
                         assigned_category, assigned_by, assigned_at, social_media_url, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?)
                    ''', (new_path, file_path, filename,
                          file_info['size'], file_info['type'],
                          'Assigned', category, session['user_id'], url,
                          f"Video IDs: {video_ids}. Remarks: {remarks}"))

                # Enhanced Google Sheets logging
                try:
                    sheets_result = log_to_google_sheets(
                        folder_name=folder_name,
                        date_processed=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        moved_to_folder=category,
                        social_media_url=url,
                        assigned_to=assign_to or 'Unassigned',
                        remarks=f"Video IDs: {video_ids}. {remarks}. Batch: {i+1}/{total_files}"
                    )
                    print(f"Google Sheets logging successful for {filename}")
                except Exception as sheets_error:
                    print(f"Google Sheets logging failed for {filename}: {sheets_error}")
                    # Continue processing even if sheets logging fails

                # Log operation
                log_operation(session['user_id'], 'batch_assigned',
                            details=f'Assigned {filename} to {category} (batch {i+1}/{total_files})')

                processed_files.append({
                    'file': filename,
                    'path': file_path,
                    'destination': category,
                    'new_paths': new_paths
                })

            except Exception as e:
                failed_files.append({
                    'file': os.path.basename(file_path),
                    'path': file_path,
                    'error': str(e)
                })

        conn.commit()
        conn.close()

        # Prepare response
        response_data = {
            'success': True,
            'total_files': total_files,
            'processed': len(processed_files),
            'failed': len(failed_files),
            'processed_files': processed_files,
            'failed_files': failed_files,
            'category': category,
            'assign_to': assign_to
        }

        if failed_files:
            response_data['warning'] = f'{len(failed_files)} files failed to process'

        return jsonify(response_data)

    except Exception as e:
        print(f"Error in enhanced_batch_assign: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/add-to-queue', methods=['POST'])
@role_required(['Assigner'])
def add_folders_to_queue():
    """Add selected folders to processing queue"""
    try:
        data = request.get_json()
        folder_paths = data.get('folder_paths', [])
        category = data.get('category')
        assign_to = data.get('assign_to', 'Unassigned')
        video_ids = data.get('video_ids', '')
        url = data.get('url', '')
        remarks = data.get('remarks', '')
        operation_type = data.get('operation_type', 'move')

        if not folder_paths or not category:
            return jsonify({'success': False, 'error': 'Missing required data: folder_paths and category'})

        if category not in Config.PROCESSING_CATEGORIES:
            return jsonify({'success': False, 'error': f'Invalid category: {category}'})

        added_items = []
        failed_items = []

        for folder_path in folder_paths:
            try:
                # Apply path fixing
                fixed_path = fix_corrupted_path(folder_path)

                if not os.path.exists(fixed_path):
                    failed_items.append({'folder': folder_path, 'error': 'Folder not found'})
                    continue

                if not os.path.isdir(fixed_path):
                    failed_items.append({'folder': folder_path, 'error': 'Path is not a folder'})
                    continue

                # Add to queue
                queue_id = add_to_queue(
                    folder_path=fixed_path,
                    category=category,
                    assign_to=assign_to,
                    video_ids=video_ids,
                    url=url,
                    remarks=remarks,
                    operation_type=operation_type
                )

                added_items.append({
                    'queue_id': queue_id,
                    'folder_path': fixed_path,
                    'folder_name': os.path.basename(fixed_path)
                })

            except Exception as e:
                failed_items.append({'folder': folder_path, 'error': str(e)})

        return jsonify({
            'success': True,
            'added': len(added_items),
            'failed': len(failed_items),
            'added_items': added_items,
            'failed_items': failed_items,
            'queue_status': get_queue_status()
        })

    except Exception as e:
        print(f"Error in add_folders_to_queue: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process-queue', methods=['POST'])
@role_required(['Assigner'])
def process_queue():
    """Process all items in the queue"""
    try:
        queue_status = get_queue_status()
        queued_items = [item for item in queue_status['items'] if item['status'] == 'queued']

        if not queued_items:
            return jsonify({'success': False, 'error': 'No items in queue to process'})

        processed_items = []
        failed_items = []

        for item in queued_items:
            try:
                # Update status to processing
                update_queue_item_status(item['id'], 'processing')

                folder_path = item['folder_path']
                category = item['category']
                assign_to = item['assign_to']
                video_ids = item['video_ids']
                url = item['url']
                remarks = item['remarks']
                operation_type = item['operation_type']

                # Determine destination path
                dest_folder = os.path.join(Config.DEST_PATH, category)
                os.makedirs(dest_folder, exist_ok=True)

                folder_name = os.path.basename(folder_path)
                dest_path = os.path.join(dest_folder, folder_name)

                # Check if destination already exists
                if os.path.exists(dest_path):
                    # Create unique name
                    counter = 1
                    base_name = folder_name
                    while os.path.exists(dest_path):
                        folder_name = f"{base_name}_{counter}"
                        dest_path = os.path.join(dest_folder, folder_name)
                        counter += 1

                # Move or copy entire folder
                if operation_type == 'move':
                    shutil.move(folder_path, dest_path)
                else:
                    shutil.copytree(folder_path, dest_path)

                # Log to Google Sheets
                try:
                    sheets_result = log_to_google_sheets(
                        folder_name=folder_name,
                        date_processed=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        moved_to_folder=category,
                        social_media_url=url,
                        assigned_to=assign_to,
                        remarks=f"Video IDs: {video_ids}. {remarks}. Queue ID: {item['id']}"
                    )
                    print(f"Google Sheets logging successful for {folder_name}")
                except Exception as sheets_error:
                    print(f"Google Sheets logging failed for {folder_name}: {sheets_error}")

                # Log operation to database
                log_operation(session['user_id'], 'folder_processed',
                            details=f'Processed folder {folder_name} to {category} (Queue ID: {item["id"]})')

                # Update status to completed
                update_queue_item_status(item['id'], 'completed')

                processed_items.append({
                    'queue_id': item['id'],
                    'folder_name': folder_name,
                    'category': category,
                    'operation': operation_type
                })

            except Exception as e:
                error_msg = str(e)
                print(f"Error processing queue item {item['id']}: {error_msg}")

                # Update status to failed
                update_queue_item_status(item['id'], 'failed', error_msg)

                failed_items.append({
                    'queue_id': item['id'],
                    'folder_name': item['folder_name'],
                    'error': error_msg
                })

        return jsonify({
            'success': True,
            'processed': len(processed_items),
            'failed': len(failed_items),
            'processed_items': processed_items,
            'failed_items': failed_items,
            'queue_status': get_queue_status()
        })

    except Exception as e:
        print(f"Error in process_queue: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/queue-status', methods=['GET'])
@role_required(['Assigner'])
def get_queue_status_api():
    """Get current queue status"""
    try:
        return jsonify({
            'success': True,
            'queue_status': get_queue_status()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/clear-queue', methods=['POST'])
@role_required(['Assigner'])
def clear_queue():
    """Clear completed and failed items from queue"""
    try:
        clear_completed_queue_items()
        return jsonify({
            'success': True,
            'message': 'Queue cleared',
            'queue_status': get_queue_status()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Cross Checker Interface
@app.route('/cross-checker')
@role_required(['Cross Checker'])
def cross_checker_interface():
    """Cross Checker interface for reviewing processed folders"""
    try:
        # Get folders from "Folder to be cross checked" directory
        crosscheck_path = r"T:\To_Process\Rough folder\Folder to be cross checked"
        pending_folders = []

        if os.path.exists(crosscheck_path):
            for item in os.listdir(crosscheck_path):
                item_path = os.path.join(crosscheck_path, item)

                if os.path.isdir(item_path):
                    # Get folder metadata from database
                    with get_db_connection() as conn:
                        cursor = conn.cursor()

                        # Find metadata for this folder
                        cursor.execute('''
                            SELECT fm.*, qi.video_ids, qi.url, qi.remarks, qi.assigned_by, qi.processed_at
                            FROM file_metadata fm
                            JOIN queue_items qi ON fm.queue_item_id = qi.id
                            WHERE fm.software_show_name = ? OR qi.folder_name = ?
                            ORDER BY fm.created_at DESC
                            LIMIT 1
                        ''', (item, item))

                        metadata_row = cursor.fetchone()

                        # Get folder stats
                        file_count = count_files_in_folder(item_path)
                        folder_size = get_folder_size(item_path)

                        # Check for audio files
                        audio_files = []
                        for root, dirs, files in os.walk(item_path):
                            for file in files:
                                if file.lower().endswith('.wav'):
                                    audio_files.append(os.path.join(root, file))

                        folder_info = {
                            'folder_name': item,
                            'folder_path': item_path,
                            'file_count': file_count,
                            'folder_size': format_file_size(folder_size),
                            'audio_files': len(audio_files),
                            'has_metadata': metadata_row is not None,
                            'processed_date': metadata_row[37] if metadata_row else None,  # created_at
                            'video_ids': metadata_row[36] if metadata_row else '',  # video_ids
                            'url': metadata_row[37] if metadata_row else '',  # url
                            'remarks': metadata_row[38] if metadata_row else '',  # remarks
                        }

                        if metadata_row:
                            folder_info.update({
                                'metadata_id': metadata_row[0],
                                'ocd_vp_number': metadata_row[3],
                                'edited_file_name': metadata_row[4],
                                'language': metadata_row[5],
                                'video_type': metadata_row[8],
                                'department_name': metadata_row[12],
                                'audio_file_name': metadata_row[24],
                                'transcription_file_name': metadata_row[25],
                                'social_media_title': metadata_row[30],
                                'description': metadata_row[31],
                                'processing_notes': metadata_row[34]
                            })

                        pending_folders.append(folder_info)

        return render_template('cross_checker.html', pending_folders=pending_folders)

    except Exception as e:
        print(f"Error in cross_checker_interface: {e}")
        return render_template('cross_checker.html', pending_folders=[])

@app.route('/api/cross-check', methods=['POST'])
@role_required(['Cross Checker'])
def cross_check_file():
    """API endpoint for cross-checking files"""
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        action = data.get('action')  # 'approve' or 'reject'
        notes = data.get('notes', '')

        if not file_id or action not in ['approve', 'reject']:
            return jsonify({'success': False, 'error': 'Invalid data'})

        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        # Update file status
        new_status = 'Cross Checked' if action == 'approve' else 'Rejected'
        cursor.execute('''
            UPDATE files SET
                cross_checked_by = ?,
                cross_checked_at = CURRENT_TIMESTAMP,
                cross_check_status = ?,
                status = ?,
                notes = ?
            WHERE id = ?
        ''', (session['user_id'], action, new_status, notes, file_id))

        # Get file info for logging
        cursor.execute('SELECT filename FROM files WHERE id = ?', (file_id,))
        filename = cursor.fetchone()[0]

        # Log operation
        log_operation(session['user_id'], 'cross_checked', file_id=file_id,
                     details=f'{action.title()}d file {filename}')

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': f'File {action}d successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# New Cross-Checker API Endpoints for Folder-based Workflow
@app.route('/api/cross-checker/folder-details')
@role_required(['Cross Checker'])
def get_folder_details():
    """Get detailed information about a folder for cross-checking"""
    try:
        folder_name = request.args.get('folder_name')
        if not folder_name:
            return jsonify({'success': False, 'error': 'Folder name required'})

        crosscheck_path = r"T:\To_Process\Rough folder\Folder to be cross checked"
        folder_path = os.path.join(crosscheck_path, folder_name)

        if not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Folder not found'})

        # Get folder metadata from database
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT fm.*, qi.video_ids, qi.url, qi.remarks
                FROM file_metadata fm
                JOIN queue_items qi ON fm.queue_item_id = qi.id
                WHERE fm.software_show_name = ? OR qi.folder_name = ?
                ORDER BY fm.created_at DESC
                LIMIT 1
            ''', (folder_name, folder_name))

            metadata_row = cursor.fetchone()

        # Get file list
        file_list = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                file_list.append({
                    'name': file,
                    'size': format_file_size(file_size),
                    'path': file_path
                })

        folder_details = {
            'folder_name': folder_name,
            'folder_path': folder_path,
            'file_count': len(file_list),
            'folder_size': format_file_size(get_folder_size(folder_path)),
            'audio_files': len([f for f in file_list if f['name'].lower().endswith('.wav')]),
            'file_list': file_list,
            'has_metadata': metadata_row is not None
        }

        if metadata_row:
            # Return COMPLETE metadata set as required
            folder_details.update({
                # Basic Information
                'ocd_vp_number': metadata_row[3],
                'edited_file_name': metadata_row[4],
                'total_file_size': format_file_size(get_folder_size(folder_path)),
                'file_count': len(file_list),
                'language': metadata_row[5],
                'edited_year': metadata_row[6],
                'trim_closed_date': metadata_row[7],
                'video_type': metadata_row[8],
                'edited_location': metadata_row[9],
                'published_platforms': metadata_row[10],
                'department_name': metadata_row[12],
                'component': metadata_row[13],
                'content_tags': metadata_row[14],
                'backup_type': metadata_row[15],
                'access_level': metadata_row[16],
                'software_show_name': metadata_row[17],

                # Audio/Transcript Information
                'audio_code': metadata_row[23],
                'audio_file_name': metadata_row[24],
                'transcription_file_name': metadata_row[25],
                'transcription_status': metadata_row[27],

                # Social Media Information
                'published_date': metadata_row[28],
                'video_id': metadata_row[29],
                'social_media_title': metadata_row[30],
                'description': metadata_row[31],
                'social_media_url': metadata_row[32],
                'duration_category': metadata_row[33],

                # Additional Information
                'video_ids': metadata_row[-3] if len(metadata_row) > 36 else '',  # from queue_items
                'url': metadata_row[-2] if len(metadata_row) > 36 else '',       # from queue_items
                'remarks': metadata_row[-1] if len(metadata_row) > 36 else '',   # from queue_items
                'processing_notes': metadata_row[34] if len(metadata_row) > 34 else ''
            })

        return jsonify({
            'success': True,
            'folder_details': folder_details
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cross-checker/preview-folder', methods=['POST'])
@role_required(['Cross Checker'])
def preview_folder():
    """Open video files in VLC for preview"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')

        if not folder_path or not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Invalid folder path'})

        # Find video files
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        video_files = []

        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(os.path.join(root, file))

        if not video_files:
            return jsonify({'success': False, 'error': 'No video files found in folder'})

        # Open first video file in VLC
        video_file = video_files[0]
        try:
            subprocess.Popen([
                r"C:\Program Files\VideoLAN\VLC\vlc.exe",
                video_file
            ])

            return jsonify({
                'success': True,
                'message': f'Opened video in VLC',
                'video_files': len(video_files)
            })

        except Exception as e:
            return jsonify({'success': False, 'error': f'Failed to open VLC: {str(e)}'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cross-checker/play-audio', methods=['POST'])
@role_required(['Cross Checker'])
def play_audio():
    """Play audio files for validation"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')

        if not folder_path or not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Invalid folder path'})

        # Find audio files
        audio_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.wav'):
                    audio_files.append(os.path.join(root, file))

        if not audio_files:
            return jsonify({'success': False, 'error': 'No audio files found in folder'})

        # Play first audio file
        audio_file = audio_files[0]
        try:
            os.startfile(audio_file)

            return jsonify({
                'success': True,
                'message': f'Playing audio file',
                'audio_files': len(audio_files)
            })

        except Exception as e:
            return jsonify({'success': False, 'error': f'Failed to play audio: {str(e)}'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cross-checker/approve-folder', methods=['POST'])
@role_required(['Cross Checker'])
def approve_folder():
    """Approve and validate a folder - move to final destinations and update Google Sheets"""
    try:
        data = request.get_json()
        folder_name = data.get('folder_name')
        folder_path = data.get('folder_path')
        notes = data.get('notes', '')

        if not folder_name or not folder_path:
            return jsonify({'success': False, 'error': 'Folder name and path required'})

        if not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Folder not found'})

        # Define destination paths
        video_dest = r"T:\To_Process\Rough folder\To be Ingested\Videos"
        audio_dest = r"T:\To_Process\Rough folder\To be Ingested\Audios"

        # Create destination directories if they don't exist
        os.makedirs(video_dest, exist_ok=True)
        os.makedirs(audio_dest, exist_ok=True)

        # Move main folder (video) to Videos destination
        video_dest_path = os.path.join(video_dest, folder_name)

        # Find and move audio files separately
        audio_files_moved = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.wav'):
                    audio_file_path = os.path.join(root, file)
                    audio_dest_file = os.path.join(audio_dest, file)

                    # Move audio file
                    shutil.move(audio_file_path, audio_dest_file)
                    audio_files_moved.append(file)

        # Move the main folder to video destination
        shutil.move(folder_path, video_dest_path)

        # Update Google Sheets with validation status
        try:
            # Find the metadata for this folder
            with get_db_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT fm.*, qi.video_ids, qi.url, qi.remarks
                    FROM file_metadata fm
                    JOIN queue_items qi ON fm.queue_item_id = qi.id
                    WHERE fm.software_show_name = ? OR qi.folder_name = ?
                    ORDER BY fm.created_at DESC
                    LIMIT 1
                ''', (folder_name, folder_name))

                metadata_row = cursor.fetchone()

                if metadata_row:
                    # Update Google Sheets with validation status
                    service = get_google_sheets_service()
                    sheet = service.spreadsheets()

                    # Find the row with this folder's data
                    # FIXED: Extended range to include new validation columns AQ/AR
                    result = sheet.values().get(
                        spreadsheetId=Config.GOOGLE_SHEET_ID,
                        range='Records!A:AR'
                    ).execute()

                    values = result.get('values', [])

                    # Find the row with matching OCD number or folder name
                    ocd_number = metadata_row[3]  # ocd_vp_number
                    row_to_update = None

                    for i, row in enumerate(values):
                        if len(row) > 7 and (
                            (len(row) > 7 and row[7] == ocd_number) or  # Column H (index 7)
                            (len(row) > 1 and folder_name in str(row[1]))  # Column B (index 1)
                        ):
                            row_to_update = i + 1  # 1-based indexing
                            break

                    if row_to_update:
                        # Update validation columns
                        # FIXED: Changed from AI/AJ (35/36) to AQ/AR (43/44) as per requirements
                        # Column AQ (43) = "Validated Video"
                        # Column AR (44) = "Validated Audio"

                        updates = [
                            {
                                'range': f'Records!AQ{row_to_update}',
                                'values': [['Validated Video']]
                            },
                            {
                                'range': f'Records!AR{row_to_update}',
                                'values': [['Validated Audio']]
                            }
                        ]

                        body = {'valueInputOption': 'RAW', 'data': updates}

                        sheet.values().batchUpdate(
                            spreadsheetId=Config.GOOGLE_SHEET_ID,
                            body=body
                        ).execute()

                        print(f"✅ Updated Google Sheets validation status for {folder_name}")

                # Log the operation
                log_operation(
                    session['user_id'],
                    'cross_check_approved',
                    details=f'Approved and validated folder {folder_name}. Moved to ingestion. Audio files: {len(audio_files_moved)}. Notes: {notes}'
                )

        except Exception as sheets_error:
            print(f"⚠️ Google Sheets update failed: {sheets_error}")
            # Continue with the operation even if sheets update fails

        return jsonify({
            'success': True,
            'message': f'Folder approved and validated successfully. Moved {len(audio_files_moved)} audio files.',
            'details': {
                'video_destination': video_dest_path,
                'audio_files_moved': len(audio_files_moved),
                'audio_destination': audio_dest
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Executor Public Interface
@app.route('/executor-public')
@role_required(['Executor Public'])
def executor_public_interface():
    conn = sqlite3.connect(Config.DATABASE_PATH)
    cursor = conn.cursor()

    # Get files from queue_items assigned to Executor Public (exclude private files)
    cursor.execute('''
        SELECT id, folder_name, folder_path, category, video_ids, url, remarks,
               created_at, processed_at, assign_to
        FROM queue_items
        WHERE assign_to = 'Executor Public'
        AND status = 'completed'
        AND category != 'Private one video'
        ORDER BY processed_at DESC
    ''')

    available_files = []
    for row in cursor.fetchall():
        # Extract folder name for display
        folder_path = row[2]
        folder_name = row[1]

        available_files.append({
            'id': row[0],
            'filename': folder_name,
            'file_path': folder_path,
            'category': row[3],
            'url': row[5],
            'cross_checked_at': row[8],  # processed_at
            'cross_checked_by': 'Assigner',  # Since assigned by Assigner
            'video_ids': row[4],
            'remarks': row[6]
        })

    conn.close()
    return render_template('executor_public.html', available_files=available_files)

# Executor Private Interface
@app.route('/executor-private')
@role_required(['Executor Private'])
def executor_private_interface():
    conn = sqlite3.connect(Config.DATABASE_PATH)
    cursor = conn.cursor()

    # Get files from queue_items assigned to Executor Private (all files including private)
    cursor.execute('''
        SELECT id, folder_name, folder_path, category, video_ids, url, remarks,
               created_at, processed_at, assign_to
        FROM queue_items
        WHERE assign_to = 'Executor Private'
        AND status = 'completed'
        ORDER BY processed_at DESC
    ''')

    available_files = []
    for row in cursor.fetchall():
        # Extract folder name for display
        folder_path = row[2]
        folder_name = row[1]

        available_files.append({
            'id': row[0],
            'filename': folder_name,
            'file_path': folder_path,
            'category': row[3],
            'url': row[5],
            'cross_checked_at': row[8],  # processed_at
            'cross_checked_by': 'Assigner',  # Since assigned by Assigner
            'video_ids': row[4],
            'remarks': row[6]
        })

    conn.close()
    return render_template('executor_private.html', available_files=available_files)

# Editor Interface Removed - Role eliminated system-wide

@app.route('/api/process-file', methods=['POST'])
@role_required(['Executor Public', 'Executor Private'])
def process_file():
    """API endpoint for processing files"""
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        processing_notes = data.get('notes', '')

        if not file_id:
            return jsonify({'success': False, 'error': 'File ID required'})

        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        # Update file status
        cursor.execute('''
            UPDATE files SET
                processed_by = ?,
                processed_at = CURRENT_TIMESTAMP,
                status = 'Completed',
                notes = COALESCE(notes, '') || ? || ?
            WHERE id = ?
        ''', (session['user_id'], '\n--- Processing Notes ---\n' if processing_notes else '',
              processing_notes, file_id))

        # Get file info for logging
        cursor.execute('SELECT filename FROM files WHERE id = ?', (file_id,))
        filename = cursor.fetchone()[0]

        # Log operation
        log_operation(session['user_id'], 'processed', file_id=file_id,
                     details=f'Processed file {filename}')

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'File processed successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Admin Interface
@app.route('/admin')
@role_required(['Main Admin'])
def admin_interface():
    conn = sqlite3.connect(Config.DATABASE_PATH)
    cursor = conn.cursor()

    # Get all users
    cursor.execute('SELECT id, username, role, created_at, last_login, is_active FROM users ORDER BY created_at DESC')
    users = [
        {
            'id': row[0], 'username': row[1], 'role': row[2],
            'created_at': row[3], 'last_login': row[4], 'is_active': row[5]
        }
        for row in cursor.fetchall()
    ]

    # Get file statistics by status
    cursor.execute('''
        SELECT status, COUNT(*) as count
        FROM files
        GROUP BY status
        ORDER BY count DESC
    ''')
    file_stats = dict(cursor.fetchall())

    # Get processing statistics by category
    cursor.execute('''
        SELECT assigned_category, COUNT(*) as count
        FROM files
        WHERE assigned_category IS NOT NULL
        GROUP BY assigned_category
        ORDER BY count DESC
    ''')
    category_stats = dict(cursor.fetchall())

    conn.close()

    # Import datetime for template use
    from datetime import datetime

    return render_template('admin.html',
                         users=users,
                         file_stats=file_stats,
                         category_stats=category_stats,
                         user_roles=Config.USER_ROLES,
                         datetime=datetime)

@app.route('/api/admin/users', methods=['POST'])
@role_required(['Main Admin'])
def manage_users():
    """API endpoint for user management"""
    try:
        data = request.get_json()
        action = data.get('action')

        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()

        if action == 'create':
            username = data.get('username')
            password = data.get('password')
            role = data.get('role')

            if not all([username, password, role]) or role not in Config.USER_ROLES:
                return jsonify({'success': False, 'error': 'Invalid data'})

            password_hash = generate_password_hash(password)
            cursor.execute(
                'INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)',
                (username, password_hash, role)
            )

            log_operation(session['user_id'], 'user_created',
                         details=f'Created user {username} with role {role}')

        elif action == 'toggle_status':
            user_id = data.get('user_id')
            cursor.execute('UPDATE users SET is_active = NOT is_active WHERE id = ?', (user_id,))

            log_operation(session['user_id'], 'user_status_changed',
                         details=f'Toggled status for user ID {user_id}')

        elif action == 'delete':
            user_id = data.get('user_id')
            if user_id == session['user_id']:
                return jsonify({'success': False, 'error': 'Cannot delete your own account'})

            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))

            log_operation(session['user_id'], 'user_deleted',
                         details=f'Deleted user ID {user_id}')

        elif action == 'reset_password':
            user_id = data.get('user_id')
            new_password = data.get('new_password')

            if not new_password:
                return jsonify({'success': False, 'error': 'New password is required'})

            if len(new_password) < 6:
                return jsonify({'success': False, 'error': 'Password must be at least 6 characters'})

            password_hash = generate_password_hash(new_password)
            cursor.execute('UPDATE users SET password_hash = ? WHERE id = ?', (password_hash, user_id))

            log_operation(session['user_id'], 'password_reset',
                         details=f'Reset password for user ID {user_id}')

        elif action == 'edit':
            user_id = data.get('user_id')
            username = data.get('username')
            role = data.get('role')
            email = data.get('email')

            if not username or not role:
                return jsonify({'success': False, 'error': 'Username and role are required'})

            if role not in Config.USER_ROLES:
                return jsonify({'success': False, 'error': 'Invalid role'})

            cursor.execute('''
                UPDATE users
                SET username = ?, role = ?, email = ?
                WHERE id = ?
            ''', (username, role, email, user_id))

            log_operation(session['user_id'], 'user_edited',
                         details=f'Edited user ID {user_id}: {username} ({role})')

        elif action == 'bulk_create':
            users_data = data.get('users', [])
            created_count = 0

            for user_data in users_data:
                username = user_data.get('username')
                password = user_data.get('password', 'Shiva@123')  # Default password
                role = user_data.get('role')
                email = user_data.get('email')

                if username and role and role in Config.USER_ROLES:
                    try:
                        password_hash = generate_password_hash(password)
                        cursor.execute('''
                            INSERT INTO users (username, password_hash, role, email)
                            VALUES (?, ?, ?, ?)
                        ''', (username, password_hash, role, email))
                        created_count += 1
                    except sqlite3.IntegrityError:
                        continue  # Skip duplicate usernames

            log_operation(session['user_id'], 'bulk_users_created',
                         details=f'Bulk created {created_count} users')

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'Operation completed successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/test-directories')
@role_required(['Assigner'])
def test_directories():
    """Test endpoint to check directory structure"""
    try:
        result = {
            'source_path': Config.SOURCE_PATH,
            'source_exists': os.path.exists(Config.SOURCE_PATH),
            'dest_path': Config.DEST_PATH,
            'dest_exists': os.path.exists(Config.DEST_PATH),
            'source_contents': [],
            'error': None
        }

        if os.path.exists(Config.SOURCE_PATH):
            try:
                contents = os.listdir(Config.SOURCE_PATH)
                result['source_contents'] = contents[:10]  # Limit to first 10 items
                result['total_items'] = len(contents)
            except Exception as e:
                result['error'] = f"Cannot list source directory: {e}"
        else:
            # Create the directory if it doesn't exist
            try:
                os.makedirs(Config.SOURCE_PATH, exist_ok=True)
                result['source_exists'] = True
                result['source_contents'] = []
            except Exception as e:
                result['error'] = f"Cannot create source directory: {e}"

        return jsonify({'success': True, 'data': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/simple-files/<path:folder_name>')
@role_required(['Assigner'])
def get_simple_files(folder_name):
    """Simple API endpoint to get files by folder name with recursive search"""
    try:
        # Construct the full path
        full_path = os.path.join(Config.SOURCE_PATH, folder_name)

        print(f"Simple files request for folder: {folder_name}")
        print(f"Full path: {full_path}")

        if not os.path.exists(full_path):
            return jsonify({'success': False, 'error': f'Folder not found: {folder_name}'})

        if not os.path.isdir(full_path):
            return jsonify({'success': False, 'error': 'Path is not a directory'})

        video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.mxf', '.m4v', '.3gp', '.f4v'}
        files = []

        def scan_directory_recursive(directory_path, relative_path=""):
            """Recursively scan directory for video files"""
            try:
                items = os.listdir(directory_path)
                print(f"Scanning directory: {directory_path} - Found {len(items)} items")

                for item in items:
                    item_path = os.path.join(directory_path, item)

                    if os.path.isfile(item_path):
                        # Check if it's a video file
                        if any(item.lower().endswith(ext) for ext in video_extensions):
                            file_info = get_file_info(item_path)

                            # Create relative path for display
                            display_path = os.path.join(relative_path, item) if relative_path else item

                            files.append({
                                'name': item,
                                'display_name': display_path,
                                'path': item_path,
                                'size': file_info['size'],
                                'type': file_info['type'],
                                'modified': file_info['modified'].isoformat() if file_info['modified'] else None,
                                'status': 'Unassigned',
                                'subfolder': relative_path
                            })

                    elif os.path.isdir(item_path):
                        # Recursively scan subdirectory
                        new_relative_path = os.path.join(relative_path, item) if relative_path else item
                        scan_directory_recursive(item_path, new_relative_path)

            except PermissionError as e:
                print(f"Permission denied accessing: {directory_path}")
            except Exception as e:
                print(f"Error scanning directory {directory_path}: {e}")

        # Start recursive scan
        scan_directory_recursive(full_path)

        print(f"Found {len(files)} video files total (including subfolders)")

        # Sort files by subfolder and name
        files.sort(key=lambda x: (x['subfolder'], x['name']))

        return jsonify({'success': True, 'files': files, 'folder_path': full_path, 'total_files': len(files)})

    except Exception as e:
        print(f"Error in get_simple_files: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/directory-tree')
@role_required(['Assigner'])
def get_directory_tree():
    """Get complete directory tree structure with file counts"""
    try:
        def build_tree_recursive(directory_path, relative_path="", max_depth=10, current_depth=0):
            """Recursively build directory tree with file counts"""
            if current_depth > max_depth:
                return None

            tree_node = {
                'name': os.path.basename(directory_path) if relative_path else 'Root',
                'path': relative_path,
                'full_path': directory_path,
                'type': 'folder',
                'children': [],
                'video_count': 0,
                'total_size': 0,
                'is_expanded': current_depth < 2  # Auto-expand first 2 levels
            }

            try:
                items = os.listdir(directory_path)
                video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.mxf', '.m4v', '.3gp', '.f4v'}

                # Separate folders and files
                folders = []
                files = []

                for item in items:
                    item_path = os.path.join(directory_path, item)

                    if os.path.isdir(item_path):
                        folders.append(item)
                    elif os.path.isfile(item_path) and any(item.lower().endswith(ext) for ext in video_extensions):
                        try:
                            file_size = os.path.getsize(item_path)
                            files.append({
                                'name': item,
                                'path': os.path.join(relative_path, item) if relative_path else item,
                                'full_path': item_path,
                                'size': file_size,
                                'type': 'file'
                            })
                            tree_node['video_count'] += 1
                            tree_node['total_size'] += file_size
                        except Exception as e:
                            print(f"Error processing file {item}: {e}")
                            pass  # Skip files we can't access

                # Add folders to tree
                folders.sort()
                for folder in folders:
                    folder_path = os.path.join(directory_path, folder)
                    folder_relative = os.path.join(relative_path, folder) if relative_path else folder

                    child_node = build_tree_recursive(
                        folder_path,
                        folder_relative,
                        max_depth,
                        current_depth + 1
                    )

                    if child_node:
                        tree_node['children'].append(child_node)
                        tree_node['video_count'] += child_node['video_count']
                        tree_node['total_size'] += child_node['total_size']

                # Add files to tree
                for file_info in files:
                    tree_node['children'].append(file_info)

            except PermissionError:
                tree_node['error'] = 'Permission denied'
            except Exception as e:
                tree_node['error'] = str(e)

            return tree_node

        print(f"Building directory tree for: {Config.SOURCE_PATH}")
        tree = build_tree_recursive(Config.SOURCE_PATH)

        return jsonify({
            'success': True,
            'tree': tree,
            'source_path': Config.SOURCE_PATH
        })

    except Exception as e:
        print(f"Error building directory tree: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/folder-contents/', methods=['GET', 'POST'])
@app.route('/api/folder-contents/<path:folder_path>', methods=['GET', 'POST'])
@role_required(['Assigner'])
def get_folder_contents(folder_path=''):
    """Get contents of a specific folder (files only)"""
    try:
        # Handle POST request with folder path in body
        if request.method == 'POST':
            data = request.get_json()
            folder_path = data.get('folder_path', '')

        # Decode and clean the folder path
        if folder_path:
            import urllib.parse
            folder_path = urllib.parse.unquote(folder_path)
            # Remove any leading/trailing slashes and normalize
            folder_path = folder_path.strip('\\/')
            full_path = os.path.join(Config.SOURCE_PATH, folder_path)
        else:
            full_path = Config.SOURCE_PATH

        full_path = os.path.normpath(full_path)

        print(f"Getting folder contents for: '{folder_path}'")
        print(f"Full path: '{full_path}'")
        print(f"Source path: '{Config.SOURCE_PATH}'")
        print(f"Path exists: {os.path.exists(full_path)}")
        print(f"Is directory: {os.path.isdir(full_path) if os.path.exists(full_path) else 'N/A'}")

        # Security check
        if not os.path.abspath(full_path).startswith(os.path.abspath(Config.SOURCE_PATH)):
            return jsonify({'success': False, 'error': 'Invalid path - security violation'})

        if not os.path.exists(full_path):
            return jsonify({'success': False, 'error': f'Folder not found: {full_path}'})

        if not os.path.isdir(full_path):
            return jsonify({'success': False, 'error': f'Path is not a directory: {full_path}'})

        video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.mxf', '.m4v', '.3gp', '.f4v'}
        files = []

        try:
            items = os.listdir(full_path)
            print(f"Found {len(items)} items in directory")

            for item in items:
                item_path = os.path.join(full_path, item)
                if os.path.isfile(item_path) and any(item.lower().endswith(ext) for ext in video_extensions):
                    try:
                        file_info = get_file_info(item_path)
                        files.append({
                            'name': item,
                            'path': item_path,
                            'relative_path': os.path.join(folder_path, item) if folder_path else item,
                            'size': file_info['size'],
                            'type': file_info['type'],
                            'modified': file_info['modified'].isoformat() if file_info['modified'] else None,
                            'folder': folder_path
                        })
                    except Exception as e:
                        print(f"Error processing file {item}: {e}")
                        continue

            files.sort(key=lambda x: x['name'].lower())
            print(f"Found {len(files)} video files")

        except PermissionError:
            return jsonify({'success': False, 'error': 'Permission denied accessing folder'})
        except Exception as e:
            print(f"Error listing directory: {e}")
            return jsonify({'success': False, 'error': f'Error accessing directory: {e}'})

        return jsonify({
            'success': True,
            'files': files,
            'folder_path': folder_path,
            'full_path': full_path,
            'file_count': len(files)
        })

    except Exception as e:
        print(f"Error getting folder contents: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/debug-directory')
@role_required(['Assigner'])
def debug_directory():
    """Debug endpoint to check directory structure"""
    try:
        result = {
            'source_path': Config.SOURCE_PATH,
            'source_exists': os.path.exists(Config.SOURCE_PATH),
            'source_is_dir': os.path.isdir(Config.SOURCE_PATH) if os.path.exists(Config.SOURCE_PATH) else False,
            'contents': [],
            'error': None
        }

        if os.path.exists(Config.SOURCE_PATH) and os.path.isdir(Config.SOURCE_PATH):
            try:
                items = os.listdir(Config.SOURCE_PATH)
                for item in items[:20]:  # Limit to first 20 items
                    item_path = os.path.join(Config.SOURCE_PATH, item)
                    result['contents'].append({
                        'name': item,
                        'is_dir': os.path.isdir(item_path),
                        'is_file': os.path.isfile(item_path),
                        'path': item_path
                    })
            except Exception as e:
                result['error'] = f"Cannot list directory: {e}"

        return jsonify({'success': True, 'data': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/serve-video')
@login_required
def serve_video():
    """Simple video server that works"""
    try:
        file_path = request.args.get('path')

        if not file_path:
            return "No file path provided", 400

        # Simple URL decode
        import urllib.parse
        decoded_path = urllib.parse.unquote(file_path)

        # Check if file exists
        if not os.path.exists(decoded_path):
            return "File not found", 404

        # Security check
        allowed_paths = [Config.SOURCE_PATH, Config.DEST_PATH]
        if not any(os.path.abspath(decoded_path).startswith(os.path.abspath(allowed_path)) for allowed_path in allowed_paths):
            return "Access denied", 403

        # Serve file
        return send_file(decoded_path, as_attachment=False, conditional=True)

    except Exception as e:
        return f"Error: {str(e)}", 500

@app.route('/api/video-info')
@login_required
def get_video_info():
    """Get video file information"""
    try:
        file_path = request.args.get('path')
        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': 'File not found'})

        file_info = get_file_info(file_path)

        return jsonify({
            'success': True,
            'info': {
                'name': os.path.basename(file_path),
                'size': file_info['size'],
                'size_formatted': format_file_size(file_info['size']),
                'type': file_info['type'],
                'modified': file_info['modified'].isoformat() if file_info['modified'] else None,
                'path': file_path
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/open-vlc', methods=['POST'])
@login_required
def open_vlc():
    """VLC opener that handles both JSON and form data"""
    try:
        # Handle both JSON and form data for compatibility
        file_path = None

        # Try JSON first (new method)
        if request.is_json:
            data = request.get_json()
            file_path = data.get('file_path') if data else None
            print(f"🔧 Received JSON data: {file_path}")
        else:
            # Fallback to form data (old method)
            file_path = request.form.get('file_path')
            print(f"📝 Received form data: {file_path}")

        print(f"🎬 VLC Request - Raw file path: {file_path}")

        if not file_path:
            print("❌ No file path provided")
            return jsonify({'success': False, 'error': 'No file path provided'})

        # Apply the comprehensive fix using the global function
        file_path = fix_corrupted_path(file_path)

        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return jsonify({'success': False, 'error': f'File not found: {file_path}'})

        print(f"✅ File exists: {file_path}")

        import subprocess

        # Try VLC
        vlc_path = 'C:\\Program Files\\VideoLAN\\VLC\\vlc.exe'
        if os.path.exists(vlc_path):
            print(f"🚀 Launching VLC: {vlc_path}")
            subprocess.Popen([vlc_path, file_path])
            return jsonify({'success': True, 'message': 'VLC opened successfully'})

        print("⚠️ VLC not found, trying default app")
        # Fallback to default app
        os.startfile(file_path)
        return jsonify({'success': True, 'message': 'Opened with default app'})

    except Exception as e:
        print(f"❌ VLC Error: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/check-large-files')
@role_required(['Assigner'])
def check_large_files():
    """Check for large video files that might need special handling"""
    try:
        large_files = []
        threshold_mb = 100  # 100MB threshold

        def scan_for_large_files(directory):
            try:
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if any(file.lower().endswith(ext) for ext in ['.mov', '.mp4', '.avi', '.mkv', '.wmv', '.mxf']):
                            try:
                                size = os.path.getsize(file_path)
                                if size > threshold_mb * 1024 * 1024:
                                    large_files.append({
                                        'name': file,
                                        'path': file_path,
                                        'size': size,
                                        'size_mb': round(size / (1024 * 1024), 2),
                                        'relative_path': os.path.relpath(file_path, Config.SOURCE_PATH)
                                    })
                            except:
                                continue
            except:
                pass

        scan_for_large_files(Config.SOURCE_PATH)

        # Sort by size (largest first)
        large_files.sort(key=lambda x: x['size'], reverse=True)

        return jsonify({
            'success': True,
            'large_files': large_files[:50],  # Limit to top 50
            'threshold_mb': threshold_mb,
            'total_found': len(large_files)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/test-google-sheets', methods=['POST'])
@role_required(['Assigner'])
def test_google_sheets():
    """Test Google Sheets connectivity and logging"""
    try:
        test_result = log_to_google_sheets(
            folder_name="TEST_FOLDER",
            date_processed=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            moved_to_folder="TEST_CATEGORY",
            social_media_url="https://test.com",
            assigned_to="TEST_USER",
            remarks="Test entry from Archives Management System"
        )

        return jsonify({
            'success': test_result.get('success', False),
            'message': test_result.get('message', 'Unknown result'),
            'error': test_result.get('error'),
            'gspread_available': GSPREAD_AVAILABLE,
            'credentials_exist': os.path.exists(os.path.join(os.path.dirname(__file__), "credentials.json"))
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/convert-video-preview', methods=['POST'])
@login_required
def convert_video_preview():
    """Convert video to browser-compatible format for preview"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': 'File not found'})

        # Security check
        allowed_paths = [Config.SOURCE_PATH, Config.DEST_PATH]
        if not any(os.path.abspath(file_path).startswith(os.path.abspath(allowed_path)) for allowed_path in allowed_paths):
            return jsonify({'success': False, 'error': 'Access denied'})

        # Create preview directory
        preview_dir = os.path.join(os.path.dirname(__file__), 'temp_previews')
        os.makedirs(preview_dir, exist_ok=True)

        # Generate preview filename
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        preview_file = os.path.join(preview_dir, f"{base_name}_preview.mp4")

        # Check if preview already exists
        if os.path.exists(preview_file):
            preview_url = f"/api/serve-preview/{base_name}_preview.mp4"
            return jsonify({
                'success': True,
                'preview_url': preview_url,
                'message': 'Preview already exists'
            })

        # Try to convert using ffmpeg if available
        try:
            import subprocess

            # Simple ffmpeg command for browser compatibility
            cmd = [
                'ffmpeg',
                '-i', file_path,
                '-c:v', 'libx264',  # H.264 video codec
                '-c:a', 'aac',      # AAC audio codec
                '-movflags', '+faststart',  # Web optimization
                '-t', '30',         # First 30 seconds only for preview
                '-y',               # Overwrite output
                preview_file
            ]

            print(f"Converting video with command: {' '.join(cmd)}")

            # Run conversion
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0 and os.path.exists(preview_file):
                preview_url = f"/api/serve-preview/{base_name}_preview.mp4"
                return jsonify({
                    'success': True,
                    'preview_url': preview_url,
                    'message': 'Preview converted successfully (30 seconds)'
                })
            else:
                print(f"FFmpeg error: {result.stderr}")
                return jsonify({
                    'success': False,
                    'error': 'Video conversion failed',
                    'details': result.stderr
                })

        except subprocess.TimeoutExpired:
            return jsonify({
                'success': False,
                'error': 'Conversion timeout - file too large'
            })
        except FileNotFoundError:
            return jsonify({
                'success': False,
                'error': 'FFmpeg not installed - cannot convert video'
            })
        except Exception as conv_error:
            return jsonify({
                'success': False,
                'error': f'Conversion error: {str(conv_error)}'
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/serve-preview/<filename>')
@login_required
def serve_preview(filename):
    """Serve converted preview files"""
    try:
        preview_dir = os.path.join(os.path.dirname(__file__), 'temp_previews')
        file_path = os.path.join(preview_dir, filename)

        if not os.path.exists(file_path):
            return "Preview not found", 404

        return send_file(
            file_path,
            mimetype='video/mp4',
            as_attachment=False,
            conditional=True
        )

    except Exception as e:
        return f"Error: {str(e)}", 500

@app.route('/api/get-folders', methods=['GET'])
@role_required(['Assigner', 'Executor Public', 'Executor Private'])
def get_folders_api():
    """Get folder structure for professional assigner"""
    try:
        def scan_folder(folder_path, max_depth=5, current_depth=0, show_files=True):
            """Recursively scan folder structure with complete file listing"""
            if current_depth >= max_depth:
                return []

            items = []
            try:
                if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                    return items

                # Get all items in the folder
                all_items = []
                try:
                    all_items = os.listdir(folder_path)
                except PermissionError:
                    return items

                # Separate folders and files
                folders = []
                files = []

                for item in all_items:
                    item_path = os.path.join(folder_path, item)
                    try:
                        if os.path.isdir(item_path):
                            folders.append(item)
                        elif os.path.isfile(item_path) and show_files:
                            files.append(item)
                    except (PermissionError, OSError):
                        continue

                # Process folders first
                for folder in sorted(folders, key=str.lower):
                    folder_path_full = os.path.join(folder_path, folder)

                    # Count media files recursively
                    media_count = 0
                    try:
                        for root, dirs, folder_files in os.walk(folder_path_full):
                            media_count += len([f for f in folder_files
                                              if f.lower().endswith(('.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.m4a'))])
                    except:
                        media_count = 0

                    # Get children recursively
                    children = scan_folder(folder_path_full, max_depth, current_depth + 1, show_files)

                    folder_info = {
                        'name': folder,
                        'path': folder_path_full,
                        'type': 'folder',
                        'file_count': media_count,
                        'media_count': media_count,
                        'children': children,
                        'expanded': False
                    }
                    items.append(folder_info)

                # Process files if enabled
                if show_files:
                    for file in sorted(files, key=str.lower):
                        file_path = os.path.join(folder_path, file)

                        # Get file extension and check if it's media
                        file_ext = os.path.splitext(file)[1].lower()
                        is_media = file_ext in ['.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.mp3', '.wav', '.m4a', '.aac', '.ogg']

                        # Get file size
                        try:
                            file_size = os.path.getsize(file_path)
                            file_size_str = format_file_size(file_size)
                        except:
                            file_size_str = "Unknown"

                        file_info = {
                            'name': file,
                            'path': file_path,
                            'type': 'file',
                            'size': file_size_str,
                            'extension': file_ext,
                            'is_media': is_media,
                            'children': []
                        }
                        items.append(file_info)

            except PermissionError:
                pass
            except Exception as e:
                print(f"Error scanning folder {folder_path}: {e}")

            return sorted(items, key=lambda x: x['name'].lower())

        # Scan the source directory
        source_path = Config.SOURCE_PATH
        folders = scan_folder(source_path)

        return jsonify({
            'success': True,
            'folders': folders,
            'source_path': source_path
        })

    except Exception as e:
        print(f"Error in get_folders_api: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

# New API Endpoints for Enhanced Functionality

@app.route('/api/folder-details')
@role_required(['Executor Public', 'Executor Private'])
def get_executor_folder_details():
    """Get detailed information about a folder"""
    try:
        folder_path = request.args.get('path')
        if not folder_path or not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Invalid folder path'})

        # Get folder statistics
        file_count = 0
        video_files = 0
        audio_files = 0
        total_size = 0

        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_count += 1
                total_size += os.path.getsize(file_path)

                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm')):
                    video_files += 1
                elif file.lower().endswith(('.wav', '.mp3', '.aac', '.flac')):
                    audio_files += 1

        details = {
            'name': os.path.basename(folder_path),
            'path': folder_path,
            'file_count': file_count,
            'size': format_file_size(total_size),
            'video_files': video_files,
            'audio_files': audio_files
        }

        return jsonify({'success': True, 'details': details})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})





@app.route('/api/preview-folder-vlc', methods=['POST'])
@role_required(['Executor Public', 'Executor Private'])
def preview_folder_vlc():
    """Open all video files in a folder with VLC"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')

        if not folder_path or not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Invalid folder path'})

        # Find video files
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        video_files = []

        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(os.path.join(root, file))

        if not video_files:
            return jsonify({'success': False, 'error': 'No video files found in folder'})

        # Open first video file in VLC (opening all at once might be overwhelming)
        video_file = video_files[0]
        try:
            subprocess.Popen([
                r"C:\Program Files\VideoLAN\VLC\vlc.exe",
                video_file
            ])

            return jsonify({
                'success': True,
                'video_count': len(video_files),
                'message': f'Opened video in VLC ({len(video_files)} videos found)'
            })

        except Exception as e:
            return jsonify({'success': False, 'error': f'Failed to open VLC: {str(e)}'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/get-cross-check-folders')
@role_required(['Cross Checker'])
def get_cross_check_folders():
    """Get folder tree for cross-check directory"""
    try:
        crosscheck_path = r"T:\To_Process\Rough folder\Folder to be cross checked"

        if not os.path.exists(crosscheck_path):
            return jsonify({'success': False, 'error': 'Cross-check directory not found'})

        def build_tree(path, max_depth=3, current_depth=0):
            if current_depth >= max_depth:
                return None

            items = []
            try:
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)

                    if os.path.isdir(item_path):
                        folder_node = {
                            'name': item,
                            'path': item_path,
                            'type': 'folder',
                            'children': build_tree(item_path, max_depth, current_depth + 1) or []
                        }
                        items.append(folder_node)
                    else:
                        file_size = os.path.getsize(item_path)
                        file_node = {
                            'name': item,
                            'path': item_path,
                            'type': 'file',
                            'size': format_file_size(file_size)
                        }
                        items.append(file_node)
            except PermissionError:
                pass

            return items if items else None

        tree = {
            'name': 'Folder to be cross checked',
            'path': crosscheck_path,
            'type': 'folder',
            'children': build_tree(crosscheck_path) or []
        }

        return jsonify({'success': True, 'tree': tree})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cross-checker/approve-folder', methods=['POST'])
@role_required(['Cross Checker'])
def approve_cross_check_folder():
    """Approve and validate a folder, moving files to ingestion directories"""
    try:
        data = request.get_json()
        folder_name = data.get('folder_name')
        folder_path = data.get('folder_path')
        notes = data.get('notes', '')

        if not folder_name or not folder_path:
            return jsonify({'success': False, 'error': 'Missing required parameters'})

        if not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': 'Folder not found'})

        # Define destination directories
        video_dest = r"T:\To_Process\Rough folder\To be Ingested\Videos"
        audio_dest = r"T:\To_Process\Rough folder\To be Ingested\Audios"

        # Create destination directories
        os.makedirs(video_dest, exist_ok=True)
        os.makedirs(audio_dest, exist_ok=True)

        moved_videos = []
        moved_audios = []

        # Process files in the folder
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()

                if file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']:
                    # Move video files
                    dest_path = os.path.join(video_dest, file)
                    if os.path.exists(dest_path):
                        # Create unique name if file exists
                        base, ext = os.path.splitext(file)
                        counter = 1
                        while os.path.exists(dest_path):
                            dest_path = os.path.join(video_dest, f"{base}_{counter}{ext}")
                            counter += 1

                    shutil.move(file_path, dest_path)
                    moved_videos.append(dest_path)

                elif file_ext in ['.wav', '.mp3', '.aac', '.flac']:
                    # Move audio files
                    dest_path = os.path.join(audio_dest, file)
                    if os.path.exists(dest_path):
                        # Create unique name if file exists
                        base, ext = os.path.splitext(file)
                        counter = 1
                        while os.path.exists(dest_path):
                            dest_path = os.path.join(audio_dest, f"{base}_{counter}{ext}")
                            counter += 1

                    shutil.move(file_path, dest_path)
                    moved_audios.append(dest_path)

        # Remove the empty folder
        try:
            shutil.rmtree(folder_path)
        except:
            pass  # Folder might not be empty due to other files

        # Update Google Sheets with validation status (columns AI/AJ)
        try:
            # Find the folder in Google Sheets and update validation columns
            credentials_path = os.path.join(os.path.dirname(__file__), "credentials.json")
            if os.path.exists(credentials_path):
                scope = [
                    "https://spreadsheets.google.com/feeds",
                    "https://www.googleapis.com/auth/drive",
                    "https://www.googleapis.com/auth/spreadsheets"
                ]
                creds = Credentials.from_service_account_file(credentials_path, scopes=scope)
                client = gspread.authorize(creds)
                sheet = client.open_by_key("13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4").worksheet("Records")

                # Find the folder row
                try:
                    cell = sheet.find(folder_name)
                    row_number = cell.row

                    # Update validation columns (AQ = column 43, AR = column 44)
                    # FIXED: Changed from AI/AJ (35/36) to AQ/AR (43/44) as per requirements
                    sheet.update_cell(row_number, 43, 'VALIDATED')  # AQ column
                    sheet.update_cell(row_number, 44, f'Cross-checked by {session["user_id"]} on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}. {notes}')  # AR column

                except:
                    pass  # Folder not found in sheets
        except:
            pass  # Google Sheets update failed

        # Log the operation
        log_operation(
            session['user_id'],
            'cross_check_approved',
            details=f'Approved folder {folder_name}. Moved {len(moved_videos)} videos and {len(moved_audios)} audio files to ingestion directories. Notes: {notes}'
        )

        return jsonify({
            'success': True,
            'message': f'Folder approved and validated successfully. Moved {len(moved_videos)} videos and {len(moved_audios)} audio files to ingestion directories.',
            'moved_videos': len(moved_videos),
            'moved_audios': len(moved_audios)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
