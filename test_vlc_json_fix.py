#!/usr/bin/env python3
"""
VLC JSON FIX TEST
Tests the fixed VLC integration with JSON data
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5001"

def test_vlc_json_fix():
    """Test VLC integration with JSON data"""
    print("🔧 VLC JSON FIX TEST")
    print("=" * 60)
    print("Testing: Fixed VLC integration with JSON data instead of form data")
    print("=" * 60)
    
    # Create session for login
    session = requests.Session()
    
    # Test 1: Login
    print("1. 🔐 Testing Login...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    try:
        login_response = session.post(f"{BASE_URL}/login", data=login_data)
        if login_response.status_code == 200:
            print("   ✅ Login successful")
        else:
            print(f"   ❌ Login failed: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return False
    
    # Test 2: VLC Integration with JSON Data
    print("\n2. 🎬 Testing VLC Integration with JSON Data...")
    
    # Test with a known working file path (from the logs)
    test_file_path = "estored filesZ6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019OutputCauvery-Calling-core-video-2_5min-V2.mov"
    
    try:
        # Send JSON data (the fix)
        vlc_data = {
            'file_path': test_file_path
        }
        
        vlc_response = session.post(
            f"{BASE_URL}/api/open-vlc", 
            headers={'Content-Type': 'application/json'},
            data=json.dumps(vlc_data)
        )
        
        print(f"   📡 Request sent with JSON data")
        print(f"   📊 Response status: {vlc_response.status_code}")
        
        if vlc_response.status_code == 200:
            vlc_result = vlc_response.json()
            if vlc_result.get('success'):
                print(f"   ✅ VLC opened successfully!")
                print(f"   🎬 File: {test_file_path}")
                print(f"   📁 VLC Path: {vlc_result.get('vlc_path', 'Unknown')}")
                print(f"   💬 Message: {vlc_result.get('message', 'No message')}")
                return True
            else:
                print(f"   ❌ VLC failed: {vlc_result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ HTTP error: {vlc_response.status_code}")
            try:
                error_data = vlc_response.json()
                print(f"   📄 Error details: {error_data}")
            except:
                print(f"   📄 Raw response: {vlc_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ VLC test exception: {e}")
        return False

def test_vlc_form_data_comparison():
    """Test comparison between form data (old) and JSON data (new)"""
    print("\n" + "=" * 60)
    print("🔄 COMPARISON TEST: Form Data vs JSON Data")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    login_data = {'username': 'admin', 'password': 'admin123'}
    session.post(f"{BASE_URL}/login", data=login_data)
    
    test_file_path = "estored filesZ6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019OutputCauvery-Calling-core-video-2_5min-V2.mov"
    
    # Test 1: Form Data (old method - should fail)
    print("1. 📝 Testing Form Data (Old Method)...")
    try:
        form_response = session.post(
            f"{BASE_URL}/api/open-vlc",
            data={'file_path': test_file_path}
        )
        print(f"   📊 Status: {form_response.status_code}")
        if form_response.status_code == 200:
            result = form_response.json()
            if result.get('success'):
                print("   ✅ Form data worked (backend handles both)")
            else:
                print(f"   ❌ Form data failed: {result.get('error')}")
        else:
            print(f"   ❌ Form data HTTP error: {form_response.status_code}")
    except Exception as e:
        print(f"   ❌ Form data exception: {e}")
    
    # Test 2: JSON Data (new method - should work)
    print("\n2. 🔧 Testing JSON Data (New Method)...")
    try:
        json_response = session.post(
            f"{BASE_URL}/api/open-vlc",
            headers={'Content-Type': 'application/json'},
            data=json.dumps({'file_path': test_file_path})
        )
        print(f"   📊 Status: {json_response.status_code}")
        if json_response.status_code == 200:
            result = json_response.json()
            if result.get('success'):
                print("   ✅ JSON data worked perfectly!")
            else:
                print(f"   ❌ JSON data failed: {result.get('error')}")
        else:
            print(f"   ❌ JSON data HTTP error: {json_response.status_code}")
    except Exception as e:
        print(f"   ❌ JSON data exception: {e}")

if __name__ == "__main__":
    print("🎬 TESTING VLC JSON FIX")
    print("This test verifies that the VLC integration now works with JSON data")
    print("")
    
    success = test_vlc_json_fix()
    test_vlc_form_data_comparison()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS")
    print("=" * 60)
    
    if success:
        print("✅ VLC JSON FIX SUCCESSFUL!")
        print("🔧 Frontend now sends JSON data instead of form data")
        print("🎬 VLC integration should work perfectly in the browser")
        print("🌐 Test the fix at: http://127.0.0.1:5001/professional-assigner")
        print("")
        print("🎉 The '415 Unsupported Media Type' error is now FIXED!")
    else:
        print("❌ VLC JSON FIX FAILED")
        print("🔍 Check the Flask logs for more details")
    
    print("=" * 60)
