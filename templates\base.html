<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Archives Edited Backlog Project{% endblock %}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --light-color: #ecf0f1;
            --dark-color: #34495e;
        }

        body {
            background-color: var(--light-color);
            font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .sidebar {
            background: linear-gradient(180deg, var(--primary-color), var(--dark-color));
            min-height: calc(100vh - 56px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--secondary-color), var(--info-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: bold;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border: none;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending { background-color: var(--warning-color); color: white; }
        .status-completed { background-color: var(--success-color); color: white; }
        .status-rejected { background-color: var(--danger-color); color: white; }
        .status-assigned { background-color: var(--info-color); color: white; }
        .status-unassigned { background-color: #6c757d; color: white; }

        .file-item {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-item:hover {
            border-color: var(--secondary-color);
            background-color: rgba(52, 152, 219, 0.05);
        }

        .file-item.selected {
            border-color: var(--success-color);
            background-color: rgba(39, 174, 96, 0.1);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .stats-card {
            background: linear-gradient(135deg, var(--secondary-color), var(--info-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .folder-tree {
            max-height: 500px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
        }

        .folder-item {
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .folder-item:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }

        .folder-item.selected {
            background-color: var(--secondary-color);
            color: white;
        }

        .processing-option {
            margin: 5px;
            transition: all 0.3s ease;
        }

        .processing-option:hover {
            transform: scale(1.05);
        }

        .processing-option.selected {
            background-color: var(--success-color) !important;
            border-color: var(--success-color) !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-archive me-2"></i>Archives Edited Backlog Project
            </a>
            
            {% if session.user_id %}
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }} ({{ session.role }})
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
            {% endif %}
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if session.user_id %}
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <nav class="nav flex-column py-3">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        
                        {% if session.role == 'Assigner' %}
                        <a class="nav-link" href="{{ url_for('assigner_interface') }}">
                            <i class="fas fa-archive me-2"></i>Archives Assignment Console
                        </a>
                        {% endif %}
                        
                        {% if session.role == 'Cross Checker' %}
                        <a class="nav-link" href="{{ url_for('cross_checker_interface') }}">
                            <i class="fas fa-check-double me-2"></i>Cross Check
                        </a>
                        {% endif %}
                        
                        {% if session.role == 'Executor Public' %}
                        <a class="nav-link" href="{{ url_for('executor_public_interface') }}">
                            <i class="fas fa-cog me-2"></i>Execute Tasks
                        </a>
                        {% endif %}
                        
                        {% if session.role == 'Executor Private' %}
                        <a class="nav-link" href="{{ url_for('executor_private_interface') }}">
                            <i class="fas fa-lock me-2"></i>Private Tasks
                        </a>
                        {% endif %}

                        {% if session.role == 'Editor' %}
                        <a class="nav-link" href="{{ url_for('editor_interface') }}">
                            <i class="fas fa-edit me-2"></i>Edit Content
                        </a>
                        {% endif %}

                        {% if session.role == 'Main Admin' %}
                        <a class="nav-link" href="{{ url_for('admin_interface') }}">
                            <i class="fas fa-users-cog me-2"></i>Admin Panel
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10">
                <div class="main-content">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    {% block content %}{% endblock %}
                </div>
            </div>
            {% else %}
            <!-- Full width for login page -->
            <div class="col-12">
                {% block login_content %}{% endblock %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js"></script>
    
    <script>
        // Global utility functions
        function showAlert(message, type = 'info') {
            const alertDiv = $(`
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            
            $('.main-content').prepend(alertDiv);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.alert('close');
            }, 5000);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleString();
        }

        // AJAX error handler
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            console.error('AJAX Error:', xhr.responseText);
            showAlert('An error occurred. Please try again.', 'danger');
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
