#!/usr/bin/env python3
"""
Test VLC opening functionality
"""

import requests
import json

def test_vlc_open():
    """Test VLC opening"""
    print("🎬 Testing VLC Open Functionality")
    
    # Login first
    session = requests.Session()
    login_data = {'username': 'assigner', 'password': '<PERSON>@123'}
    response = session.post("http://127.0.0.1:5001/login", data=login_data)
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    print("✅ Login successful")
    
    # Test VLC opening
    video_path = r"T:\To_Process\Rough folder\restored files\Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019\Output\Cauvery-Calling-core-video-2_5min-V2.mov"
    vlc_path = r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs\VideoLAN\VLC media player.lnk"
    
    data = {
        'file_path': video_path,
        'vlc_path': vlc_path
    }
    
    print(f"Sending VLC request...")
    print(f"File: {video_path}")
    print(f"VLC: {vlc_path}")
    
    response = session.post(
        "http://127.0.0.1:5001/api/open-vlc",
        headers={'Content-Type': 'application/json'},
        data=json.dumps(data)
    )
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ VLC opened successfully!")
            print(f"Message: {result.get('message')}")
        else:
            print(f"❌ VLC failed: {result.get('error')}")
    else:
        print(f"❌ Request failed: {response.status_code}")
        print(f"Response: {response.text}")

if __name__ == "__main__":
    test_vlc_open()
