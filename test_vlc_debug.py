#!/usr/bin/env python3
"""
Debug VLC integration issue
"""

import requests
import json
import os

BASE_URL = "http://127.0.0.1:5001"

def test_vlc_debug():
    """Debug VLC integration"""
    print("🔍 Debugging VLC Integration Issue")
    print("=" * 50)
    
    # Login
    session = requests.Session()
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if response.status_code == 200:
        print("✅ Login successful")
    else:
        print("❌ Login failed")
        return
    
    # Get a test file
    print("\n📁 Getting test file...")
    response = session.get(f"{BASE_URL}/api/complete-tree")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            tree = data['tree']
            
            # Find a video file
            def find_video_file(node):
                if node['type'] == 'file':
                    name = node['name'].lower()
                    if any(name.endswith(ext) for ext in ['.mp4', '.mov', '.avi', '.mkv']):
                        return node
                elif node['type'] == 'folder' and node.get('children'):
                    for child in node['children']:
                        result = find_video_file(child)
                        if result:
                            return result
                return None
            
            video_file = find_video_file(tree)
            
            if video_file:
                print(f"🎥 Found test video: {video_file['name']}")
                print(f"📍 Path: {video_file['path']}")
                
                # Check if file exists
                if os.path.exists(video_file['path']):
                    print("✅ File exists on disk")
                else:
                    print("❌ File does not exist on disk!")
                    return
                
                # Test VLC integration
                print(f"\n🎬 Testing VLC integration...")
                vlc_data = {
                    'file_path': video_file['path'],
                    'vlc_path': 'C:\\Program Files\\VideoLAN\\VLC\\vlc.exe'
                }
                
                print(f"📤 Sending VLC request:")
                print(f"   File: {vlc_data['file_path']}")
                print(f"   VLC Path: {vlc_data['vlc_path']}")
                
                response = session.post(f"{BASE_URL}/api/open-vlc",
                                      headers={'Content-Type': 'application/json'},
                                      data=json.dumps(vlc_data))
                
                print(f"\n📥 VLC API Response:")
                print(f"   Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   Success: {result.get('success')}")
                    print(f"   Message: {result.get('message')}")
                    print(f"   Method Used: {result.get('method_used')}")
                    
                    if not result.get('success'):
                        print(f"   Error: {result.get('error')}")
                        print(f"   Suggestion: {result.get('suggestion')}")
                else:
                    print(f"   ❌ HTTP Error: {response.status_code}")
                    print(f"   Response: {response.text}")
                
                # Test manual VLC paths
                print(f"\n🔍 Testing VLC installation paths:")
                vlc_paths = [
                    'C:\\Program Files\\VideoLAN\\VLC\\vlc.exe',
                    'C:\\Program Files (x86)\\VideoLAN\\VLC\\vlc.exe'
                ]
                
                for vlc_path in vlc_paths:
                    exists = os.path.exists(vlc_path)
                    print(f"   {vlc_path}: {'✅ Found' if exists else '❌ Not found'}")
                
                # Test subprocess directly
                print(f"\n🧪 Testing subprocess directly...")
                try:
                    import subprocess
                    
                    # Try the most common VLC path
                    vlc_exe = 'C:\\Program Files\\VideoLAN\\VLC\\vlc.exe'
                    if os.path.exists(vlc_exe):
                        print(f"   Using VLC at: {vlc_exe}")
                        cmd = [vlc_exe, video_file['path']]
                        print(f"   Command: {cmd}")
                        
                        try:
                            process = subprocess.Popen(cmd, shell=False)
                            print(f"   ✅ Subprocess launched successfully!")
                            print(f"   Process ID: {process.pid}")
                            
                            # Check if process is running
                            import time
                            time.sleep(1)
                            if process.poll() is None:
                                print(f"   ✅ VLC process is running")
                            else:
                                print(f"   ⚠️ VLC process exited with code: {process.returncode}")
                                
                        except Exception as e:
                            print(f"   ❌ Subprocess failed: {e}")
                    else:
                        print(f"   ❌ VLC not found at standard location")
                        
                        # Try alternative method
                        print(f"   🔄 Trying Windows file association...")
                        try:
                            os.startfile(video_file['path'])
                            print(f"   ✅ File opened with default application")
                        except Exception as e:
                            print(f"   ❌ File association failed: {e}")
                            
                except ImportError:
                    print(f"   ❌ subprocess module not available")
                
            else:
                print("❌ No video files found for testing")
        else:
            print(f"❌ Failed to get tree: {data.get('error')}")
    else:
        print(f"❌ Failed to get tree: {response.status_code}")
    
    print("\n" + "=" * 50)
    print("🔍 VLC Debug Complete")

if __name__ == "__main__":
    test_vlc_debug()
