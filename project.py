import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import os
import shutil
import json
from pathlib import Path
import vlc
from datetime import datetime
import sqlite3
import hashlib

class FileManagerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Multi-User File Management System")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # Maximize window on Windows

        # Initialize database
        self.init_database()

        # Current user info
        self.current_user = None
        self.current_role = None
        self.logged_in = False

        # File data storage (path -> {url, status, assigned_by, etc.})
        self.file_data = self.load_file_data()

        # Source and destination paths
        self.source_path = r"O:\video\00_restore\00_Archive_Stems\for spliting reference"
        self.dest_path = r"T:\To_Process\Rough folder"

        # Create directories if they don't exist
        self.create_directory_structure()

        # Processing categories
        self.processing_categories = [
            "Social media single output with stems",
            "Social media single output without stems",
            "Internal video with stems",
            "Internal video without stems",
            "Private one video",
            "Tamil files",
            "Miscellaneous",
            "Multiple output with stems",
            "Multiple output only",
            "Multiple output with project file",
            "To Be Deleted"
        ]

        # Social media categories (require URL)
        self.social_media_categories = [
            "Social media single output with stems",
            "Social media single output without stems"
        ]

        # Multiple output categories (have subfolders)
        self.multiple_output_categories = [
            "Multiple output with stems",
            "Multiple output only",
            "Multiple output with project file"
        ]

        # VLC instance for video playback
        try:
            self.vlc_instance = vlc.Instance()
            self.player = None
        except:
            self.vlc_instance = None
            self.player = None
            print("VLC not available - video preview disabled")

        # File list for search
        self.current_files = []

        # Show login window
        self.show_login_window()

    def load_file_data(self):
        try:
            with open("file_data.json", "r") as f:
                return json.load(f)
        except FileNotFoundError:
            return {}

    def save_file_data(self):
        with open("file_data.json", "w") as f:
            json.dump(self.file_data, f, indent=4)

    def show_login_window(self):
        self.login_window = tk.Toplevel(self.root)
        self.login_window.title("Login")
        self.login_window.geometry("300x200")
        self.login_window.transient(self.root)
        self.login_window.grab_set()

        tk.Label(self.login_window, text="Username:").pack(pady=10)
        self.username_entry = tk.Entry(self.login_window)
        self.username_entry.pack(pady=5)

        tk.Label(self.login_window, text="Password:").pack(pady=10)
        self.password_entry = tk.Entry(self.login_window, show="*")
        self.password_entry.pack(pady=5)

        tk.Button(self.login_window, text="Login", command=self.validate_login).pack(pady=20)

    def validate_login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        if username in self.credentials and self.credentials[username] == password:
            self.logged_in = True
            self.login_window.destroy()
            self.create_main_widgets()
        else:
            messagebox.showerror("Login Failed", "Invalid username or password.")

    def create_main_widgets(self):
        # Main frame
        self.main_frame = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Left: Folder and file tree
        self.tree_frame = ttk.Frame(self.main_frame)
        self.main_frame.add(self.tree_frame, weight=1)
        tk.Label(self.tree_frame, text="Folders and Files").pack()
        self.tree = ttk.Treeview(self.tree_frame)
        self.tree.pack(fill=tk.BOTH, expand=True)
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

        # Right: Search, URL/status/metadata, buttons, and preview
        self.right_frame = ttk.Frame(self.main_frame)
        self.main_frame.add(self.right_frame, weight=3)

        # Search bar
        tk.Label(self.right_frame, text="Search Files:").pack()
        self.search_entry = tk.Entry(self.right_frame, width=50)
        self.search_entry.pack(pady=5)
        self.search_entry.bind("<KeyRelease>", self.on_search)

        # URL field
        tk.Label(self.right_frame, text="Social Media URL:").pack()
        self.url_entry = tk.Entry(self.right_frame, width=50)
        self.url_entry.pack(pady=5)
        self.url_entry.config(state="readonly")

        # Status field
        tk.Label(self.right_frame, text="File Status:").pack()
        self.status_entry = tk.Entry(self.right_frame, width=50)
        self.status_entry.pack(pady=5)
        self.status_entry.config(state="readonly")

        # Metadata display
        tk.Label(self.right_frame, text="File Metadata:").pack()
        self.metadata_text = tk.Text(self.right_frame, width=50, height=3, state="disabled")
        self.metadata_text.pack(pady=5)

        # Buttons frame
        self.buttons_frame = ttk.Frame(self.right_frame)
        self.buttons_frame.pack(pady=10)
        
        # Action buttons
        for btn_text in self.subfolders:
            tk.Button(self.buttons_frame, text=btn_text, command=lambda x=btn_text: self.button_action(x)).pack(fill=tk.X, pady=2)

        # Preview button
        tk.Button(self.right_frame, text="Preview File", command=self.preview_file).pack(pady=5)

        # Video preview canvas
        self.preview_frame = ttk.Frame(self.right_frame)
        self.preview_frame.pack(fill=tk.BOTH, expand=True)
        self.video_canvas = tk.Canvas(self.preview_frame, bg="black")
        self.video_canvas.pack(fill=tk.BOTH, expand=True)

        # Populate folder and file tree
        self.populate_tree()

    def populate_tree(self):
        self.tree.delete(*self.tree.get_children())
        self.current_files = []
        try:
            self.populate_tree_recursive(self.source_path, "")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load folders: {str(e)}")
        self.save_file_data()
        self.on_search(None)

    def populate_tree_recursive(self, path, parent):
        try:
            for item in os.listdir(path):
                full_path = os.path.join(path, item)
                if os.path.isdir(full_path):
                    node_id = self.tree.insert(parent, tk.END, text=item, values=[full_path, "folder"])
                    self.populate_tree_recursive(full_path, node_id)
                elif item.lower().endswith((".mov", ".mp4")):
                    self.tree.insert(parent, tk.END, text=item, values=[full_path, "file"])
                    self.current_files.append(full_path)
                    if full_path not in self.file_data:
                        self.file_data[full_path] = {"url": "", "status": "Unassigned"}
        except Exception as e:
            pass

    def on_search(self, event):
        search_term = self.search_entry.get().strip().lower()
        self.tree.delete(*self.tree.get_children())
        self.current_files = []
        try:
            self.populate_tree_recursive_search(self.source_path, "", search_term)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load folders: {str(e)}")
        self.save_file_data()

    def populate_tree_recursive_search(self, path, parent, search_term):
        try:
            for item in os.listdir(path):
                full_path = os.path.join(path, item)
                if os.path.isdir(full_path):
                    if search_term in item.lower() or search_term == "":
                        node_id = self.tree.insert(parent, tk.END, text=item, values=[full_path, "folder"])
                        self.populate_tree_recursive_search(full_path, node_id, search_term)
                elif item.lower().endswith((".mov", ".mp4")) and (search_term in item.lower() or search_term == ""):
                    self.tree.insert(parent, tk.END, text=item, values=[full_path, "file"])
                    self.current_files.append(full_path)
                    if full_path not in self.file_data:
                        self.file_data[full_path] = {"url": "", "status": "Unassigned"}
        except Exception as e:
            pass

    def on_tree_select(self, event):
        selected_item = self.tree.selection()
        if not selected_item:
            return
        item_values = self.tree.item(selected_item)["values"]
        if not item_values or len(item_values) < 2:
            return
        item_path, item_type = item_values
        # Update fields only for files
        if item_type == "file":
            self.url_entry.config(state="normal")
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, self.file_data.get(item_path, {}).get("url", ""))
            self.url_entry.config(state="readonly")
            self.status_entry.config(state="normal")
            self.status_entry.delete(0, tk.END)
            self.status_entry.insert(0, self.file_data.get(item_path, {}).get("status", "Unassigned"))
            self.status_entry.config(state="readonly")
            # Update metadata
            self.metadata_text.config(state="normal")
            self.metadata_text.delete("1.0", tk.END)
            try:
                file_size = os.path.getsize(item_path) / (1024 * 1024)  # MB
                creation_time = datetime.fromtimestamp(os.path.getctime(item_path)).strftime("%Y-%m-%d %H:%M:%S")
                self.metadata_text.insert(tk.END, f"File Size: {file_size:.2f} MB\n")
                self.metadata_text.insert(tk.END, f"Created: {creation_time}")
            except Exception as e:
                self.metadata_text.insert(tk.END, f"Error loading metadata: {str(e)}")
            self.metadata_text.config(state="disabled")
        else:
            # Clear fields for folders
            self.url_entry.config(state="normal")
            self.url_entry.delete(0, tk.END)
            self.url_entry.config(state="readonly")
            self.status_entry.config(state="normal")
            self.status_entry.delete(0, tk.END)
            self.status_entry.config(state="readonly")
            self.metadata_text.config(state="normal")
            self.metadata_text.delete("1.0", tk.END)
            self.metadata_text.config(state="disabled")
        self.stop_preview()

    def preview_file(self):
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("Error", "Please select a file to preview.")
            return
        item_values = self.tree.item(selected_item)["values"]
        if not item_values or len(item_values) < 2 or item_values[1] != "file":
            messagebox.showerror("Error", "Please select a video file (.mov or .mp4).")
            return
        file_path = item_values[0]
        
        try:
            self.stop_preview()
            self.player = self.vlc_instance.media_player_new()
            media = self.vlc_instance.media_new(file_path)
            self.player.set_media(media)
            self.player.set_hwnd(self.video_canvas.winfo_id())
            self.player.play()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to preview file: {str(e)}")

    def stop_preview(self):
        if self.player:
            self.player.stop()
            self.player = None

    def button_action(self, button_name):
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("Error", "Please select a file.")
            return
        item_values = self.tree.item(selected_item)["values"]
        if not item_values or len(item_values) < 2 or item_values[1] != "file":
            messagebox.showerror("Error", "Please select a video file (.mov or .mp4).")
            return
        source_file_path = item_values[0]
        selected_file = os.path.basename(source_file_path)

        if not os.path.exists(source_file_path):
            messagebox.showerror("Error", f"File {selected_file} does not exist.")
            return

        # Handle social media actions with URL input
        url = None
        if "Social media" in button_name.lower():
            url = simpledialog.askstring("Input", "Enter URL for social media:", parent=self.root)
            if url is None:
                return
            self.file_data[source_file_path]["url"] = url
            self.url_entry.config(state="normal")
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, url)
            self.url_entry.config(state="readonly")

        try:
            dest_folder = os.path.join(self.dest_path, button_name)
            new_file_paths = []
            if button_name.startswith("Multiple output"):
                for subfolder in ["Social media", "Internal"]:
                    final_dest = os.path.join(dest_folder, subfolder, selected_file)
                    os.makedirs(os.path.dirname(final_dest), exist_ok=True)
                    shutil.move(source_file_path, final_dest)
                    new_file_paths.append(final_dest)
            else:
                final_dest = os.path.join(dest_folder, selected_file)
                os.makedirs(os.path.dirname(final_dest), exist_ok=True)
                shutil.move(source_file_path, final_dest)
                new_file_paths.append(final_dest)

            # Update file data
            for new_path in new_file_paths:
                self.file_data[new_path] = {
                    "url": self.file_data[source_file_path]["url"],
                    "status": "Assigned"
                }
            del self.file_data[source_file_path]
            self.save_file_data()

            messagebox.showinfo("Success", f"File {selected_file} moved to {button_name}")
            self.populate_tree()  # Refresh tree
            self.stop_preview()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to process file: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = FileManagerApp(root)
    root.mainloop()