#!/usr/bin/env python3
"""
REAL GOOGLE SHEETS TEST - VERIFY COLUMN A-F WRITING
This will actually write test data to Google Sheets and verify the columns
"""

import requests
import json
import time
from datetime import datetime

def test_google_sheets_real_data():
    print("🚨 REAL GOOGLE SHEETS COLUMN MAPPING TEST")
    print("="*80)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Login as assigner
    print("\n🔐 STEP 1: LOGIN AS ASSIGNER")
    try:
        login_data = {'username': 'assigner', 'password': '<PERSON>@123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Assigner login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Test Google Sheets API directly
    print("\n📊 STEP 2: TEST GOOGLE SHEETS API DIRECTLY")
    try:
        # Create test data for Google Sheets
        test_data = {
            'folder_name': f'TEST_FOLDER_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'date_processed': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'moved_to_folder': 'Internal video without stems',
            'social_media_url': 'https://test.example.com',
            'assigned_to': 'Executor Public',
            'remarks': 'Test entry for column verification'
        }
        
        # Call the test Google Sheets endpoint
        sheets_response = session.post(f"{base_url}/api/test-google-sheets", json=test_data)
        
        if sheets_response.status_code == 200:
            sheets_data = sheets_response.json()
            print(f"✅ Google Sheets API response: {sheets_data.get('success', False)}")
            print(f"✅ Message: {sheets_data.get('message', 'No message')}")
            
            if 'row_data' in sheets_data:
                row_data = sheets_data['row_data']
                print(f"✅ Row data written: {row_data}")
                print(f"✅ Column A (Folder Name): {row_data[0] if len(row_data) > 0 else 'Missing'}")
                print(f"✅ Column B (Date): {row_data[1] if len(row_data) > 1 else 'Missing'}")
                print(f"✅ Column C (Category): {row_data[2] if len(row_data) > 2 else 'Missing'}")
                print(f"✅ Column D (URL): {row_data[3] if len(row_data) > 3 else 'Missing'}")
                print(f"✅ Column E (Assigned To): {row_data[4] if len(row_data) > 4 else 'Missing'}")
                print(f"✅ Column F (Remarks): {row_data[5] if len(row_data) > 5 else 'Missing'}")
        else:
            print(f"❌ Google Sheets API failed: {sheets_response.status_code}")
            try:
                error_data = sheets_response.json()
                print(f"❌ Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"❌ Raw response: {sheets_response.text}")
    except Exception as e:
        print(f"❌ Google Sheets API test error: {e}")
    
    # Test real assignment workflow
    print("\n📋 STEP 3: TEST REAL ASSIGNMENT WORKFLOW")
    try:
        # Get available folders
        folders_response = session.get(f"{base_url}/api/get-folders")
        if folders_response.status_code == 200:
            folders_data = folders_response.json()
            print(f"✅ Folders API works: {folders_data.get('success', False)}")
            
            if folders_data.get('success') and folders_data.get('folders'):
                # Use the first available folder for testing
                test_folder = folders_data['folders'][0]
                folder_path = test_folder['path']
                folder_name = test_folder['name']
                
                print(f"✅ Using test folder: {folder_name}")
                
                # Create assignment data
                assignment_data = {
                    'folder_paths': [folder_path],
                    'category': 'Internal video without stems',
                    'assign_to': 'Executor Public',
                    'video_ids': f'VID-TEST-{datetime.now().strftime("%H%M%S")}',
                    'url': 'https://test-assignment.example.com',
                    'remarks': f'Real test assignment at {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
                }
                
                # Submit assignment
                assign_response = session.post(f"{base_url}/api/assign-folders", json=assignment_data)
                
                if assign_response.status_code == 200:
                    assign_data = assign_response.json()
                    print(f"✅ Assignment successful: {assign_data.get('success', False)}")
                    print(f"✅ Message: {assign_data.get('message', 'No message')}")
                    
                    if assign_data.get('success'):
                        print(f"✅ Added {assign_data.get('added_count', 0)} items to queue")
                        
                        # Wait a moment for Google Sheets to update
                        print("⏳ Waiting 3 seconds for Google Sheets update...")
                        time.sleep(3)
                        
                        # Check queue status
                        queue_response = session.get(f"{base_url}/api/queue-status")
                        if queue_response.status_code == 200:
                            queue_data = queue_response.json()
                            print(f"✅ Queue status: {queue_data.get('success', False)}")
                            print(f"✅ Total items in queue: {queue_data.get('total', 0)}")
                            
                            # Find our test item
                            items = queue_data.get('items', [])
                            test_item = None
                            for item in items:
                                if item.get('folder_name') == folder_name:
                                    test_item = item
                                    break
                            
                            if test_item:
                                print(f"✅ Found test item in queue:")
                                print(f"   📁 Folder: {test_item.get('folder_name')}")
                                print(f"   📂 Category: {test_item.get('category')}")
                                print(f"   👤 Assigned to: {test_item.get('assign_to')}")
                                print(f"   🔗 URL: {test_item.get('url')}")
                                print(f"   📝 Remarks: {test_item.get('remarks')}")
                                print(f"   📊 Status: {test_item.get('status')}")
                        
                else:
                    print(f"❌ Assignment failed: {assign_response.status_code}")
                    try:
                        error_data = assign_response.json()
                        print(f"❌ Error: {error_data.get('error', 'Unknown error')}")
                    except:
                        print(f"❌ Raw response: {assign_response.text}")
            else:
                print("❌ No folders available for testing")
        else:
            print(f"❌ Folders API failed: {folders_response.status_code}")
    except Exception as e:
        print(f"❌ Assignment workflow test error: {e}")
    
    # Verify Google Sheets configuration
    print("\n🔧 STEP 4: VERIFY GOOGLE SHEETS CONFIGURATION")
    try:
        config_response = session.get(f"{base_url}/api/debug-directories")
        if config_response.status_code == 200:
            config_data = config_response.json()
            print(f"✅ Configuration check successful")
            print(f"✅ Source path exists: {config_data.get('source_exists', False)}")
            print(f"✅ Destination path exists: {config_data.get('dest_exists', False)}")
        
        # Check credentials
        import os
        credentials_exist = os.path.exists("credentials.json")
        print(f"✅ Google Sheets credentials exist: {credentials_exist}")
        
        if credentials_exist:
            print("✅ Google Sheets integration should be working")
            print("✅ Sheet ID: 13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
            print("✅ Sheet Name: Records")
            print("✅ Column mapping: A-F for Assigner entries")
        else:
            print("❌ Google Sheets credentials missing")
    except Exception as e:
        print(f"❌ Configuration check error: {e}")
    
    print("\n" + "="*80)
    print("🎯 REAL GOOGLE SHEETS TEST COMPLETE!")
    print("📊 EXPECTED RESULT: Data should appear in columns A-F of Google Sheets")
    print("🔍 MANUAL VERIFICATION REQUIRED:")
    print("   1. Open Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print("   2. Check 'Records' sheet")
    print("   3. Look for test data in columns A-F (not G-M)")
    print("   4. Verify folder name, date, category, URL, assigned to, remarks")
    print("="*80)

if __name__ == "__main__":
    test_google_sheets_real_data()
