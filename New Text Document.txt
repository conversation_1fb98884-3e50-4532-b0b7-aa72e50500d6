import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import os
import shutil
import json
from pathlib import Path
import vlc

class FileManagerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("File Manager - Assigner")
        self.root.geometry("1000x600")

        # Login credentials (in-memory)
        self.credentials = {"assigner": "password123"}
        self.logged_in = False

        # File data storage (path -> {url, status})
        self.file_data = self.load_file_data()
        
        # Source and destination paths
        self.source_path = r"O:\video\00_restore\00_Archive_Stems\for spliting reference"
        self.dest_path = r"T:\To_Process\Rough folder"
        
        # Create directories
        if not os.path.exists(self.source_path):
            os.makedirs(self.source_path)
        if not os.path.exists(self.dest_path):
            os.makedirs(self.dest_path)
        
        # Subfolders
        self.subfolders = [
            "Social media single output with stems",
            "Social media single output without stems",
            "Internal video with stems",
            "Internal video without stems",
            "Private one video",
            "Tamil files",
            "Miscellaneous",
            "Multiple output with stems",
            "Multiple output only",
            "Multiple output with project file",
            "To Be Deleted"
        ]
        for folder in self.subfolders:
            folder_path = os.path.join(self.dest_path, folder)
            os.makedirs(folder_path, exist_ok=True)
            if folder.startswith("Multiple output"):
                os.makedirs(os.path.join(folder_path, "Social media"), exist_ok=True)
                os.makedirs(os.path.join(folder_path, "Internal"), exist_ok=True)

        # VLC instance for video playback
        self.vlc_instance = vlc.Instance()
        self.player = None

        # Show login window
        self.show_login_window()

    def load_file_data(self):
        try:
            with open("file_data.json", "r") as f:
                return json.load(f)
        except FileNotFoundError:
            return {}

    def save_file_data(self):
        with open("file_data.json", "w") as f:
            json.dump(self.file_data, f, indent=4)

    def show_login_window(self):
        self.login_window = tk.Toplevel(self.root)
        self.login_window.title("Login")
        self.login_window.geometry("300x200")
        self.login_window.transient(self.root)
        self.login_window.grab_set()

        tk.Label(self.login_window, text="Username:").pack(pady=10)
        self.username_entry = tk.Entry(self.login_window)
        self.username_entry.pack(pady=5)

        tk.Label(self.login_window, text="Password:").pack(pady=10)
        self.password_entry = tk.Entry(self.login_window, show="*")
        self.password_entry.pack(pady=5)

        tk.Button(self.login_window, text="Login", command=self.validate_login).pack(pady=20)

    def validate_login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        if username in self.credentials and self.credentials[username] == password:
            self.logged_in = True
            self.login_window.destroy()
            self.create_main_widgets()
        else:
            messagebox.showerror("Login Failed", "Invalid username or password.")

    def create_main_widgets(self):
        # Main frame
        self.main_frame = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Left: Folder tree
        self.tree_frame = ttk.Frame(self.main_frame)
        self.main_frame.add(self.tree_frame, weight=1)
        tk.Label(self.tree_frame, text="Folders").pack()
        self.tree = ttk.Treeview(self.tree_frame)
        self.tree.pack(fill=tk.BOTH, expand=True)
        self.tree.bind("<<TreeviewSelect>>", self.on_folder_select)

        # Right: File list, URL/status fields, buttons, and preview
        self.right_frame = ttk.Frame(self.main_frame)
        self.main_frame.add(self.right_frame, weight=3)

        # File listbox
        tk.Label(self.right_frame, text="Files (.mov, .mp4)").pack()
        self.file_listbox = tk.Listbox(self.right_frame, width=50, height=15)
        self.file_listbox.pack(pady=5)
        self.file_listbox.bind("<<ListboxSelect>>", self.on_file_select)

        # URL field
        tk.Label(self.right_frame, text="Social Media URL:").pack()
        self.url_entry = tk.Entry(self.right_frame, width=50)
        self.url_entry.pack(pady=5)
        self.url_entry.config(state="readonly")

        # Status field
        tk.Label(self.right_frame, text="File Status:").pack()
        self.status_entry = tk.Entry(self.right_frame, width=50)
        self.status_entry.pack(pady=5)
        self.status_entry.config(state="readonly")

        # Buttons frame
        self.buttons_frame = ttk.Frame(self.right_frame)
        self.buttons_frame.pack(pady=10)
        
        # Action buttons
        for btn_text in self.subfolders:
            tk.Button(self.buttons_frame, text=btn_text, command=lambda x=btn_text: self.button_action(x)).pack(fill=tk.X, pady=2)

        # Preview button
        tk.Button(self.right_frame, text="Preview File", command=self.preview_file).pack(pady=5)

        # Video preview canvas
        self.preview_frame = ttk.Frame(self.right_frame)
        self.preview_frame.pack(fill=tk.BOTH, expand=True)
        self.video_canvas = tk.Canvas(self.preview_frame, bg="black")
        self.video_canvas.pack(fill=tk.BOTH, expand=True)

        # Populate folder tree
        self.populate_folders()

    def populate_folders(self):
        self.tree.delete(*self.tree.get_children())
        try:
            for item in os.listdir(self.source_path):
                full_path = os.path.join(self.source_path, item)
                if os.path.isdir(full_path):
                    self.tree.insert("", tk.END, text=item, values=[full_path])
                    self.populate_subfolders(full_path, item)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load folders: {str(e)}")

    def populate_subfolders(self, path, parent):
        try:
            for item in os.listdir(path):
                full_path = os.path.join(path, item)
                if os.path.isdir(full_path):
                    sub_id = self.tree.insert(parent, tk.END, text=item, values=[full_path])
                    self.populate_subfolders(full_path, sub_id)
        except Exception as e:
            pass

    def on_folder_select(self, event):
        selected_item = self.tree.selection()
        if not selected_item:
            return
        folder_path = self.tree.item(selected_item)["values"][0]
        self.file_listbox.delete(0, tk.END)
        try:
            for file in os.listdir(folder_path):
                if file.lower().endswith((".mov", ".mp4")):
                    self.file_listbox.insert(tk.END, file)
                    file_path = os.path.join(folder_path, file)
                    if file_path not in self.file_data:
                        self.file_data[file_path] = {"url": "", "status": "Unassigned"}
            self.save_file_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load files: {str(e)}")
        # Clear URL and status fields
        self.url_entry.config(state="normal")
        self.url_entry.delete(0, tk.END)
        self.url_entry.config(state="readonly")
        self.status_entry.config(state="normal")
        self.status_entry.delete(0, tk.END)
        self.status_entry.config(state="readonly")
        self.stop_preview()

    def on_file_select(self, event):
        selected_file = self.file_listbox.get(tk.ACTIVE)
        if not selected_file:
            return
        selected_item = self.tree.selection()
        if not selected_item:
            return
        folder_path = self.tree.item(selected_item)["values"][0]
        file_path = os.path.join(folder_path, selected_file)
        # Update URL and status fields
        self.url_entry.config(state="normal")
        self.url_entry.delete(0, tk.END)
        self.url_entry.insert(0, self.file_data.get(file_path, {}).get("url", ""))
        self.url_entry.config(state="readonly")
        self.status_entry.config(state="normal")
        self.status_entry.delete(0, tk.END)
        self.status_entry.insert(0, self.file_data.get(file_path, {}).get("status", "Unassigned"))
        self.status_entry.config(state="readonly")

    def preview_file(self):
        selected_file = self.file_listbox.get(tk.ACTIVE)
        if not selected_file:
            messagebox.showerror("Error", "Please select a file to preview.")
            return
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("Error", "Please select a folder.")
            return
        file_path = os.path.join(self.tree.item(selected_item)["values"][0], selected_file)
        
        try:
            self.stop_preview()
            self.player = self.vlc_instance.media_player_new()
            media = self.vlc_instance.media_new(file_path)
            self.player.set_media(media)
            self.player.set_hwnd(self.video_canvas.winfo_id())
            self.player.play()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to preview file: {str(e)}")

    def stop_preview(self):
        if self.player:
            self.player.stop()
            self.player = None

    def button_action(self, button_name):
        selected_file = self.file_listbox.get(tk.ACTIVE)
        if not selected_file:
            messagebox.showerror("Error", "Please select a file.")
            return
        
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showerror("Error", "Please select a folder.")
            return
        source_folder = self.tree.item(selected_item)["values"][0]
        source_file_path = os.path.join(source_folder, selected_file)

        if not os.path.exists(source_file_path):
            messagebox.showerror("Error", f"File {selected_file} does not exist.")
            return

        # Handle social media actions with URL input
        url = None
        if "Social media" in button_name.lower():
            url = simpledialog.askstring("Input", "Enter URL for social media:", parent=self.root)
            if url is None:
                return
            self.file_data[source_file_path]["url"] = url
            self.url_entry.config(state="normal")
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, url)
            self.url_entry.config(state="readonly")

        try:
            dest_folder = os.path.join(self.dest_path, button_name)
            new_file_paths = []
            if button_name.startswith("Multiple output"):
                for subfolder in ["Social media", "Internal"]:
                    final_dest = os.path.join(dest_folder, subfolder, selected_file)
                    os.makedirs(os.path.dirname(final_dest), exist_ok=True)
                    shutil.move(source_file_path, final_dest)
                    new_file_paths.append(final_dest)
            else:
                final_dest = os.path.join(dest_folder, selected_file)
                os.makedirs(os.path.dirname(final_dest), exist_ok=True)
                shutil.move(source_file_path, final_dest)
                new_file_paths.append(final_dest)

            # Update file data for new paths
            for new_path in new_file_paths:
                self.file_data[new_path] = {
                    "url": self.file_data[source_file_path]["url"],
                    "status": "Assigned"
                }
            del self.file_data[source_file_path]
            self.save_file_data()

            messagebox.showinfo("Success", f"File {selected_file} moved to {button_name}")
            self.on_folder_select(None)
            self.stop_preview()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to process file: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = FileManagerApp(root)
    root.mainloop()