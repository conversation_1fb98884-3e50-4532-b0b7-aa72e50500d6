#!/usr/bin/env python3
"""
TEST PROCESS BUTTON FLOW
Test the exact flow that happens when Process button is clicked
"""

import requests
import json

def test_process_button_flow():
    print("🔧 TESTING PROCESS BUTTON FLOW")
    print("=" * 80)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # STEP 1: Login
        print("1. 🔐 Login...")
        login_data = {
            'username': 'executor_public',
            'password': 'Shiva@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return False
        
        print("   ✅ Login successful!")
        
        # STEP 2: Get queue (like page loading)
        print("\n2. 📋 Get queue...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        if queue_response.status_code != 200:
            print("   ❌ Queue load failed!")
            return False
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue error: {queue_data.get('error')}")
            return False
        
        files = queue_data.get('files', [])
        print(f"   ✅ Queue loaded: {len(files)} files")
        
        if not files:
            print("   ⚠️ No files to test!")
            return False
        
        # STEP 3: Test file details API (what happens when Process button is clicked)
        print("\n3. 🔍 Test file details API (Process button action)...")
        test_file = files[0]
        queue_item_id = test_file.get('queue_item_id')
        folder_name = test_file.get('folder_name', 'Unknown')
        
        print(f"   📂 Testing: {folder_name}")
        print(f"   🆔 Queue ID: {queue_item_id}")
        
        details_response = session.get(f"{base_url}/api/file-details/{queue_item_id}")
        print(f"   📥 File Details API Status: {details_response.status_code}")
        
        if details_response.status_code != 200:
            print("   ❌ File details API failed!")
            print(f"      Response: {details_response.text[:200]}...")
            return False
        
        try:
            details_data = details_response.json()
        except:
            print("   ❌ Invalid JSON response!")
            print(f"      Response: {details_response.text[:200]}...")
            return False
        
        if not details_data.get('success'):
            print(f"   ❌ File details error: {details_data.get('error')}")
            return False
        
        file_details = details_data.get('file_details', {})
        print("   ✅ File details loaded successfully!")
        print("   📊 Response data:")
        print(f"      📁 Folder: {file_details.get('folder_name', 'Unknown')}")
        print(f"      📊 File Count: {file_details.get('file_count', 0)}")
        print(f"      💾 Total Size: {file_details.get('total_size_formatted', 'Unknown')}")
        print(f"      ⏱️ Duration: {file_details.get('detected_duration', 'Unknown')}")
        print(f"      🆔 Video IDs: {file_details.get('video_ids', 'Not specified')}")
        print(f"      💬 Remarks: {file_details.get('remarks', 'No remarks')}")
        
        # STEP 4: Test metadata options API (form dropdown data)
        print("\n4. 📝 Test metadata options API...")
        options_response = session.get(f"{base_url}/api/executive-public/metadata-options")
        print(f"   📥 Metadata Options API Status: {options_response.status_code}")
        
        if options_response.status_code != 200:
            print("   ❌ Metadata options API failed!")
            return False
        
        try:
            options_data = options_response.json()
        except:
            print("   ❌ Invalid JSON response!")
            return False
        
        if not options_data.get('success'):
            print(f"   ❌ Metadata options error: {options_data.get('error')}")
            return False
        
        options = options_data.get('options', {})
        print("   ✅ Metadata options loaded successfully!")
        print(f"      🎬 Video Types: {len(options.get('video_types', []))}")
        print(f"      🌍 Languages: {len(options.get('languages', []))}")
        print(f"      🏢 Departments: {len(options.get('departments', []))}")
        print(f"      🏷️ Content Tags: {len(options.get('content_tags', []))}")
        
        # STEP 5: Check if all required data is present for form
        print("\n5. 🔧 Check form data completeness...")
        
        required_for_form = {
            'folder_name': file_details.get('folder_name'),
            'category': file_details.get('category'),
            'folder_path': file_details.get('folder_path'),
            'file_count': file_details.get('file_count'),
            'total_size_formatted': file_details.get('total_size_formatted'),
            'video_types': options.get('video_types', []),
            'languages': options.get('languages', [])
        }
        
        all_data_present = True
        for key, value in required_for_form.items():
            if not value:
                print(f"   ❌ Missing: {key}")
                all_data_present = False
            else:
                print(f"   ✅ Present: {key}")
        
        if all_data_present:
            print("   ✅ All required data present for form!")
        else:
            print("   ❌ Some required data missing!")
        
        return all_data_present
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚨 TESTING PROCESS BUTTON FLOW")
    print("=" * 80)
    
    success = test_process_button_flow()
    
    print("\n" + "=" * 80)
    print("🎯 PROCESS BUTTON FLOW TEST RESULTS")
    print("=" * 80)
    
    if success:
        print("✅ BACKEND APIs WORKING CORRECTLY!")
        print("\n🔧 All APIs responding properly:")
        print("   ✅ Login working")
        print("   ✅ Queue API working")
        print("   ✅ File details API working")
        print("   ✅ Metadata options API working")
        print("   ✅ All required data present")
        
        print("\n🔍 ISSUE IS LIKELY IN FRONTEND:")
        print("   The backend is working correctly.")
        print("   The issue is probably in the JavaScript:")
        print("   1. ❓ Button click event not firing")
        print("   2. ❓ Modal not showing due to Bootstrap issue")
        print("   3. ❓ JavaScript error preventing modal display")
        print("   4. ❓ CSS hiding the modal")
        
        print("\n🛠️ NEXT STEPS:")
        print("   1. Check browser console for JavaScript errors")
        print("   2. Verify Bootstrap modal is working")
        print("   3. Test button click event manually")
        print("   4. Check if modal CSS is correct")
        
    else:
        print("❌ BACKEND ISSUES FOUND!")
        print("   Fix the backend API issues first")
        print("   Then test the frontend")
    
    return success

if __name__ == "__main__":
    main()
