<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Beautiful Archives Assigner</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --dark-color: #34495e;
            --light-color: #ecf0f1;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            background: var(--gradient-bg);
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1400px;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .content-section {
            padding: 40px;
        }

        .folder-browser {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid #dee2e6;
        }

        .folder-tree {
            max-height: 400px;
            overflow-y: auto;
            background: white;
            border-radius: 10px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }

        .folder-item {
            padding: 12px 15px;
            margin: 5px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .folder-item:hover {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-color: var(--secondary-color);
            transform: translateX(5px);
        }

        .folder-item.selected {
            background: linear-gradient(135deg, var(--secondary-color), #1976d2);
            color: white;
            border-color: var(--primary-color);
        }

        /* Tree View Styles */
        .tree-node {
            margin: 2px 0;
            user-select: none;
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .node-content:hover {
            background: rgba(52, 152, 219, 0.1);
        }

        .folder-node .node-content {
            font-weight: 500;
        }

        .file-node .node-content {
            font-size: 0.9em;
        }

        .expand-icon {
            cursor: pointer;
            margin-right: 8px;
            transition: transform 0.2s ease;
            width: 16px;
            text-align: center;
        }

        .expand-icon:hover {
            color: var(--secondary-color);
        }

        .folder-checkbox, .file-checkbox {
            margin-right: 8px;
            transform: scale(1.1);
        }

        .folder-children {
            border-left: 2px solid rgba(52, 152, 219, 0.2);
            margin-left: 15px;
            padding-left: 5px;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .file-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .file-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-bg);
        }

        .file-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: var(--secondary-color);
        }

        .file-card.selected {
            border-color: var(--success-color);
            background: linear-gradient(135deg, #f8fff8, #e8f5e8);
        }

        .file-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .file-icon {
            font-size: 2.5rem;
            color: var(--secondary-color);
            margin-right: 15px;
            flex-shrink: 0;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--dark-color);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .file-meta {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .file-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-play {
            background: linear-gradient(135deg, var(--success-color), #2ecc71);
            color: white;
        }

        .btn-play:hover {
            background: linear-gradient(135deg, #2ecc71, var(--success-color));
            transform: scale(1.05);
        }

        .btn-vlc {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }

        .btn-vlc:hover {
            background: linear-gradient(135deg, #f7931e, #ff6b35);
            transform: scale(1.05);
        }

        .btn-info {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            color: white;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #2980b9, var(--secondary-color));
            transform: scale(1.05);
        }

        .video-player-section {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            color: white;
            display: none;
        }

        .video-player {
            width: 100%;
            max-height: 500px;
            border-radius: 10px;
            background: #000;
        }

        .assignment-panel {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .assignment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .form-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 10px;
            display: block;
        }

        .form-select, .form-control {
            border-radius: 8px;
            border: 2px solid #dee2e6;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-select:focus, .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .process-btn {
            background: linear-gradient(135deg, var(--success-color), #2ecc71);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .process-btn:hover {
            background: linear-gradient(135deg, #2ecc71, var(--success-color));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        }

        .process-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .stats-bar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-item {
            flex: 1;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--secondary-color);
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .codec-warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }

        .search-box {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 1rem;
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-tab {
            padding: 10px 20px;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .filter-tab.active {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .filter-tab:hover {
            border-color: var(--secondary-color);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-title {
                font-size: 2rem;
            }

            .content-section {
                padding: 20px;
            }

            .file-grid {
                grid-template-columns: 1fr;
            }

            .assignment-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1 class="header-title">
                <i class="fas fa-film me-3"></i>Beautiful Archives Assigner
            </h1>
            <p class="header-subtitle">Professional Video File Management & Assignment System</p>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Stats Bar -->
            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-number" id="totalFiles">0</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="selectedFiles">0</div>
                    <div class="stat-label">Selected</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="processedFiles">0</div>
                    <div class="stat-label">Processed</div>
                </div>
            </div>

            <!-- Alert Area -->
            <div id="alertArea"></div>

            <!-- Folder & File Browser -->
            <div class="folder-browser">
                <h3><i class="fas fa-folder-tree me-2"></i>Folders & Files Browser</h3>
                <div class="search-box">
                    <input type="text" class="search-input" id="folderSearch" placeholder="Search folders and files...">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="folder-tree" id="folderTree" style="max-height: 500px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 8px;">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                        <p>Loading folders and files...</p>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-primary me-2" onclick="expandAllFolders()">
                        <i class="fas fa-expand-arrows-alt me-1"></i>Expand All
                    </button>
                    <button class="btn btn-sm btn-outline-secondary me-2" onclick="collapseAllFolders()">
                        <i class="fas fa-compress-arrows-alt me-1"></i>Collapse All
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="refreshFolders()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="testVLC()">
                        <i class="fas fa-play me-1"></i>Test VLC
                    </button>
                </div>
            </div>

            <!-- File Filters -->
            <div class="filter-tabs" id="filterTabs">
                <div class="filter-tab active" data-filter="all">
                    <i class="fas fa-list me-2"></i>All Files
                </div>
                <div class="filter-tab" data-filter="video">
                    <i class="fas fa-video me-2"></i>Videos
                </div>
                <div class="filter-tab" data-filter="audio">
                    <i class="fas fa-music me-2"></i>Audio
                </div>
                <div class="filter-tab" data-filter="large">
                    <i class="fas fa-weight-hanging me-2"></i>Large Files (>1GB)
                </div>
            </div>

            <!-- File Search -->
            <div class="search-box">
                <input type="text" class="search-input" id="fileSearch" placeholder="Search files by name...">
                <i class="fas fa-search search-icon"></i>
            </div>

            <!-- File Grid -->
            <div class="file-grid" id="fileGrid">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Select a folder to view files...</p>
                </div>
            </div>

            <!-- Video Player Section -->
            <div class="video-player-section" id="videoSection">
                <h4><i class="fas fa-play-circle me-2"></i>Video Preview</h4>
                <video class="video-player" id="videoPlayer" controls>
                    Your browser does not support the video tag.
                </video>
                <div class="codec-warning" id="codecWarning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Codec Notice:</strong> This video format may not play in the browser. Use "Open in VLC" for full compatibility.
                </div>
                <div class="mt-3">
                    <button class="btn btn-warning me-2" onclick="openInVLC()">
                        <i class="fas fa-external-link-alt me-2"></i>Open in VLC
                    </button>
                    <button class="btn btn-secondary" onclick="closeVideo()">
                        <i class="fas fa-times me-2"></i>Close Player
                    </button>
                </div>
            </div>

            <!-- Assignment Panel -->
            <div class="assignment-panel">
                <h3><i class="fas fa-tasks me-2"></i>File Assignment</h3>

                <div class="assignment-grid">
                    <div class="form-group">
                        <label class="form-label">Processing Category</label>
                        <select class="form-select" id="categorySelect">
                            <option value="">Select category...</option>
                            {% for category in processing_options %}
                            <option value="{{ category }}">{{ category }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Assign to User</label>
                        <select class="form-select" id="userSelect">
                            <option value="">Select user...</option>
                            {% for user in assignable_users %}
                            <option value="{{ user }}">{{ user }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group" id="urlSection" style="display: none;">
                        <label class="form-label">Social Media URL</label>
                        <input type="url" class="form-control" id="urlInput" placeholder="Enter URL...">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Operation Type</label>
                        <select class="form-select" id="operationType">
                            <option value="copy">Copy Files</option>
                            <option value="move">Move Files</option>
                        </select>
                    </div>
                </div>

                <div class="d-flex gap-3 flex-wrap">
                    <button class="process-btn" id="processBtn" onclick="processSelectedFiles()" disabled>
                        <i class="fas fa-cogs me-2"></i>Process Selected Files
                    </button>
                    <button class="btn btn-outline-primary" onclick="selectAllFiles()">
                        <i class="fas fa-check-square me-2"></i>Select All Files
                    </button>
                    <button class="btn btn-outline-success" onclick="selectEntireFolder()">
                        <i class="fas fa-folder-plus me-2"></i>Select Entire Folder
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearSelection()">
                        <i class="fas fa-square me-2"></i>Clear All
                    </button>
                </div>

                <!-- Progress Bar Section -->
                <div id="progressSection" style="display: none; margin-top: 20px;">
                    <h5><i class="fas fa-tasks me-2"></i>Processing Progress</h5>
                    <div class="progress mb-3" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             id="progressBar" role="progressbar" style="width: 0%">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                    <div id="progressDetails" class="small text-muted"></div>
                </div>

                <!-- Operation Logs Section -->
                <div id="logsSection" style="display: none; margin-top: 20px;">
                    <h5><i class="fas fa-list-alt me-2"></i>Operation Logs</h5>
                    <div id="operationLogs" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.9em;">
                        <!-- Logs will be populated here -->
                    </div>
                    <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearLogs()">
                        <i class="fas fa-trash me-1"></i>Clear Logs
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // Global variables
        let currentFolderPath = '';
        let allFiles = [];
        let filteredFiles = [];
        let selectedFiles = [];
        let currentVideoPath = '';
        let processedCount = 0;

        // Initialize the application
        $(document).ready(function() {
            loadFolders();
            setupEventListeners();
            updateStats();
        });

        // Setup event listeners
        function setupEventListeners() {
            // Category change handler
            $('#categorySelect').change(function() {
                const category = $(this).val();
                const socialMediaCategories = ['Social media outputs with stems', 'Social media outputs without stems'];

                if (socialMediaCategories.includes(category)) {
                    $('#urlSection').show();
                } else {
                    $('#urlSection').hide();
                    $('#urlInput').val('');
                }
                updateProcessButton();
            });

            // User selection handler
            $('#userSelect').change(updateProcessButton);

            // Search handlers
            $('#folderSearch').on('input', filterFolders);
            $('#fileSearch').on('input', filterFiles);

            // Filter tab handlers
            $('.filter-tab').click(function() {
                $('.filter-tab').removeClass('active');
                $(this).addClass('active');
                const filter = $(this).data('filter');
                applyFileFilter(filter);
            });
        }

        // Load complete folder and file tree
        function loadFolders() {
            showAlert('Loading complete folder structure...', 'info');

            $.get('/api/complete-tree')
                .done(function(response) {
                    if (response.success) {
                        renderCompleteTree(response.tree);
                        hideAlert();
                        showAlert('Folder tree loaded successfully!', 'success');
                    } else {
                        showAlert('Failed to load folder tree: ' + response.error, 'danger');
                    }
                })
                .fail(function() {
                    showAlert('Failed to connect to server', 'danger');
                });
        }

        // Render complete folder and file tree with checkboxes
        function renderCompleteTree(tree) {
            // Store tree globally for other functions to access
            window.currentTree = tree;

            let html = '<div class="tree-root">';
            html += renderTreeNode(tree, 0);
            html += '</div>';
            $('#folderTree').html(html);
        }

        // Render individual tree node (folder or file)
        function renderTreeNode(node, level) {
            const indent = level * 20;
            let html = '';

            if (node.type === 'folder') {
                const hasChildren = node.children && node.children.length > 0;
                const folderId = `folder_${Math.random().toString(36).substr(2, 9)}`;

                html += `
                    <div class="tree-node folder-node" style="margin-left: ${indent}px;" data-path="${node.path}" data-type="folder">
                        <div class="node-content">
                            <input type="checkbox" class="folder-checkbox" onchange="toggleFolderSelection('${node.path}', this.checked)">
                            ${hasChildren ? `<i class="fas fa-chevron-right expand-icon" onclick="toggleFolder('${folderId}')"></i>` : '<i class="fas fa-folder"></i>'}
                            <i class="fas fa-folder text-warning me-2"></i>
                            <strong>${node.name}</strong>
                            <span class="text-muted ms-2">(${node.file_count || 0} files)</span>
                        </div>
                        <div class="folder-children" id="${folderId}" style="display: none;">
                `;

                if (hasChildren) {
                    node.children.forEach(child => {
                        html += renderTreeNode(child, level + 1);
                    });
                }

                html += '</div></div>';
            } else if (node.type === 'file') {
                const fileIcon = getFileIcon(node.name);
                const isVideo = isVideoFile(node.name);

                html += `
                    <div class="tree-node file-node" style="margin-left: ${indent}px;" data-path="${node.path}" data-type="file">
                        <div class="node-content">
                            <input type="checkbox" class="file-checkbox" onchange="toggleFileSelection('${node.path}', this.checked)">
                            <i class="${fileIcon} me-2"></i>
                            <span>${node.name}</span>
                            <span class="text-muted ms-2">(${formatFileSize(node.size || 0)})</span>
                            ${isVideo ? `
                                <button class="btn btn-sm btn-outline-primary ms-2" onclick="playVideo('${node.path}', '${node.name}')">
                                    <i class="fas fa-play"></i>
                                </button>
                            ` : ''}
                            <button class="btn btn-sm btn-warning ms-1" onclick="openVLC('${node.path}')">
                                <i class="fas fa-play"></i> VLC
                            </button>
                        </div>
                    </div>
                `;
            }

            return html;
        }

        // Toggle folder expansion
        function toggleFolder(folderId) {
            const folderElement = $(`#${folderId}`);
            const expandIcon = folderElement.siblings('.node-content').find('.expand-icon');

            if (folderElement.is(':visible')) {
                folderElement.hide();
                expandIcon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
            } else {
                folderElement.show();
                expandIcon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
            }
        }

        // Expand all folders
        function expandAllFolders() {
            $('.folder-children').show();
            $('.expand-icon').removeClass('fa-chevron-right').addClass('fa-chevron-down');
            showAlert('All folders expanded', 'info');
        }

        // Collapse all folders
        function collapseAllFolders() {
            $('.folder-children').hide();
            $('.expand-icon').removeClass('fa-chevron-down').addClass('fa-chevron-right');
            showAlert('All folders collapsed', 'info');
        }

        // Toggle folder selection (select all files in folder)
        function toggleFolderSelection(folderPath, isChecked) {
            console.log(`📁 ${isChecked ? 'Selecting' : 'Deselecting'} folder:`, folderPath);

            // Find all files in this folder and its subfolders
            const folderFiles = [];
            $(`.file-node[data-path^="${folderPath}"]`).each(function() {
                const filePath = $(this).data('path');
                folderFiles.push(filePath);

                // Update file checkbox
                $(this).find('.file-checkbox').prop('checked', isChecked);

                // Update selection array
                if (isChecked) {
                    if (!selectedFiles.includes(filePath)) {
                        selectedFiles.push(filePath);
                    }
                } else {
                    const index = selectedFiles.indexOf(filePath);
                    if (index > -1) {
                        selectedFiles.splice(index, 1);
                    }
                }
            });

            updateStats();
            updateProcessButton();
            showAlert(`${isChecked ? 'Selected' : 'Deselected'} ${folderFiles.length} files from folder`, 'info');
        }

        // Toggle individual file selection
        function toggleFileSelection(filePath, isChecked) {
            console.log(`📄 ${isChecked ? 'Selecting' : 'Deselecting'} file:`, filePath);

            if (isChecked) {
                if (!selectedFiles.includes(filePath)) {
                    selectedFiles.push(filePath);
                }
            } else {
                const index = selectedFiles.indexOf(filePath);
                if (index > -1) {
                    selectedFiles.splice(index, 1);
                }
            }

            updateStats();
            updateProcessButton();
        }

        // Render files in grid
        function renderFiles() {
            if (filteredFiles.length === 0) {
                $('#fileGrid').html('<div class="text-center py-4"><i class="fas fa-folder-open"></i><p>No files found</p></div>');
                return;
            }

            let html = '';
            filteredFiles.forEach(file => {
                const isSelected = selectedFiles.includes(file.path);
                const fileIcon = getFileIcon(file.name);
                const fileSize = formatFileSize(file.size);

                html += `
                    <div class="file-card ${isSelected ? 'selected' : ''}" data-path="${file.path}" onclick="toggleFileSelection('${file.path}')">
                        <div class="file-header">
                            <i class="${fileIcon} file-icon"></i>
                            <div class="file-info">
                                <div class="file-name">${file.name}</div>
                                <div class="file-meta">
                                    <i class="fas fa-hdd me-1"></i>${fileSize}
                                    <i class="fas fa-folder ms-3 me-1"></i>${file.folder || 'Root'}
                                </div>
                            </div>
                        </div>
                        <div class="file-actions">
                            ${isVideoFile(file.name) ? `
                                <button class="action-btn btn-play" onclick="event.stopPropagation(); playVideo('${file.path}', '${file.name}')">
                                    <i class="fas fa-play"></i> Play
                                </button>
                            ` : ''}
                            <button class="action-btn btn-vlc" onclick="event.stopPropagation(); openFileInVLC('${file.path.replace(/'/g, "\\'")}')">
                                <i class="fas fa-external-link-alt"></i> VLC
                            </button>
                            <button class="action-btn btn-info" onclick="event.stopPropagation(); showFileInfo('${file.path}')">
                                <i class="fas fa-info"></i> Info
                            </button>
                        </div>
                    </div>
                `;
            });

            $('#fileGrid').html(html);
        }

        // Toggle file selection
        function toggleFileSelection(filePath) {
            const index = selectedFiles.indexOf(filePath);
            if (index > -1) {
                selectedFiles.splice(index, 1);
                $(`.file-card[data-path="${filePath}"]`).removeClass('selected');
            } else {
                selectedFiles.push(filePath);
                $(`.file-card[data-path="${filePath}"]`).addClass('selected');
            }
            updateStats();
            updateProcessButton();
        }

        // Select all files
        function selectAllFiles() {
            if (filteredFiles.length === 0) {
                showAlert('No files to select', 'warning');
                return;
            }

            selectedFiles = filteredFiles.map(file => file.path);
            $('.file-card').addClass('selected');
            updateStats();
            updateProcessButton();
            showAlert(`Selected ${selectedFiles.length} files`, 'success');
        }

        // Select entire folder
        function selectEntireFolder() {
            if (!currentFolderPath) {
                showAlert('Please select a folder first', 'warning');
                return;
            }

            if (allFiles.length === 0) {
                showAlert('No files in current folder', 'warning');
                return;
            }

            // Select all files in the current folder (not just filtered)
            selectedFiles = allFiles.map(file => file.path);
            $('.file-card').addClass('selected');

            // If we're viewing filtered files, also select those not currently visible
            if (filteredFiles.length < allFiles.length) {
                showAlert(`Selected entire folder: ${allFiles.length} files (${allFiles.length - filteredFiles.length} additional files not currently visible)`, 'success');
            } else {
                showAlert(`Selected entire folder: ${allFiles.length} files`, 'success');
            }

            updateStats();
            updateProcessButton();
        }

        // Clear selection
        function clearSelection() {
            selectedFiles = [];
            $('.file-card').removeClass('selected');
            updateStats();
            updateProcessButton();
            showAlert('Selection cleared', 'info');
        }

        // Play video
        function playVideo(filePath, fileName) {
            console.log('🎥 Playing video:', fileName);
            currentVideoPath = filePath;

            // Show video section
            $('#videoSection').show();
            $('#codecWarning').hide();

            // Proper URL encoding for video path
            const videoUrl = `/api/serve-video?path=${encodeURIComponent(filePath)}`;
            console.log('Video URL:', videoUrl);

            const video = document.getElementById('videoPlayer');
            video.src = videoUrl;
            video.load();

            // Handle video events
            video.addEventListener('loadstart', function() {
                showAlert(`Loading video: ${fileName}`, 'info');
            });

            video.addEventListener('canplay', function() {
                showAlert(`Video ready: ${fileName}`, 'success');
            });

            video.addEventListener('error', function() {
                $('#codecWarning').show();
                showAlert('Video codec not supported in browser. Use VLC for playback.', 'warning');
            });

            // Scroll to video section
            $('#videoSection')[0].scrollIntoView({ behavior: 'smooth' });
        }

        // Simple VLC function that works
        function openVLC(filePath) {
            console.log('🎬 VLC called with path:', filePath);

            $.ajax({
                url: '/api/open-vlc',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    file_path: filePath
                }),
                success: function(response) {
                    if (response.success) {
                        showAlert('VLC opened!', 'success');
                    } else {
                        showAlert('VLC failed: ' + response.error, 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    showAlert('VLC request failed: ' + error, 'danger');
                }
            });
        }

        // Missing function - same as openVLC but for file buttons
        function openFileInVLC(filePath) {
            console.log('🎬 openFileInVLC called with path:', filePath);

            $.ajax({
                url: '/api/open-vlc',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    file_path: filePath
                }),
                success: function(response) {
                    if (response.success) {
                        showAlert('VLC opened successfully!', 'success');
                    } else {
                        showAlert('VLC failed: ' + response.error, 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    showAlert('VLC request failed: ' + error, 'danger');
                }
            });
        }

        // Show file info
        function showFileInfo(filePath) {
            const encodedPath = encodeURIComponent(filePath);
            const infoUrl = `/api/video-info?path=${encodedPath}`;

            $.get(infoUrl)
                .done(function(response) {
                    if (response.success) {
                        const info = response.info;
                        const infoText = `
                            File: ${info.name}
                            Size: ${info.size_formatted}
                            Type: ${info.type}
                            Modified: ${info.modified ? new Date(info.modified).toLocaleString() : 'Unknown'}
                            Path: ${info.path}
                        `;
                        alert(infoText);
                        showAlert(`File info loaded: ${info.name}`, 'success');
                    } else {
                        alert('Failed to get file info');
                        showAlert('Failed to get file info', 'error');
                    }
                })
                .fail(function() {
                    alert('File info request failed');
                    showAlert('File info request failed', 'error');
                });
        }

        // Close video player
        function closeVideo() {
            $('#videoSection').hide();
            const video = document.getElementById('videoPlayer');
            video.pause();
            video.src = '';
            currentVideoPath = '';
        }

        // Open current video in VLC
        function openInVLC() {
            if (!currentVideoPath) {
                alert('No video selected');
                return;
            }
            openFileInVLC(currentVideoPath);
        }

        // Process selected files with progress tracking
        function processSelectedFiles() {
            if (selectedFiles.length === 0) {
                showAlert('Please select files to process', 'warning');
                return;
            }

            const category = $('#categorySelect').val();
            const user = $('#userSelect').val();
            const url = $('#urlInput').val();
            const operationType = $('#operationType').val();

            if (!category || !user) {
                showAlert('Please select category and user', 'warning');
                return;
            }

            // Check if URL is required
            const socialMediaCategories = ['Social media outputs with stems', 'Social media outputs without stems'];
            if (socialMediaCategories.includes(category) && !url) {
                showAlert('URL is required for social media categories', 'warning');
                return;
            }

            // Initialize progress tracking
            const totalFiles = selectedFiles.length;
            let processedFiles = 0;

            // Show progress section
            $('#progressSection').show();
            $('#logsSection').show();

            // Reset progress
            updateProgress(0, totalFiles, 'Starting batch processing...');
            addLog(`🚀 Starting batch processing of ${totalFiles} files`);
            addLog(`📁 Category: ${category}`);
            addLog(`👤 Assigned to: ${user}`);
            addLog(`🔄 Operation: ${operationType === 'move' ? 'Move' : 'Copy'} files`);
            if (url) addLog(`🔗 URL: ${url}`);
            addLog('─'.repeat(50));

            // Disable button and show processing
            $('#processBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Processing...');

            const requestData = {
                files: selectedFiles,
                category: category,
                user: user,
                move_files: operationType === 'move',
                url: url || null
            };

            // Start processing
            addLog('📤 Sending batch request to server...');

            $.ajax({
                url: '/api/batch-assign',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    console.log('Batch processing response:', response);

                    if (response.success) {
                        processedCount += response.processed;

                        // Update progress to 100%
                        updateProgress(totalFiles, totalFiles, 'Processing completed!');

                        // Log successful files
                        addLog(`✅ Successfully processed ${response.processed} files:`);
                        if (response.processed_files) {
                            response.processed_files.forEach((file, index) => {
                                addLog(`   ${index + 1}. ✅ ${file}`);
                            });
                        }

                        // Log failed files
                        if (response.failed > 0) {
                            addLog(`❌ Failed to process ${response.failed} files:`);
                            if (response.failed_files) {
                                response.failed_files.forEach((failedFile, index) => {
                                    addLog(`   ${index + 1}. ❌ ${failedFile.file}: ${failedFile.error}`);
                                });
                            }
                            showAlert(`Processed ${response.processed} files, ${response.failed} failed.`, 'warning');
                        } else {
                            showAlert(`Successfully processed all ${response.processed} files!`, 'success');
                        }

                        addLog('─'.repeat(50));
                        addLog(`🎉 Batch processing completed! Total: ${response.processed}/${totalFiles} files processed successfully.`);

                        // Reset selections and reload
                        clearSelection();
                        if (currentFolderPath) {
                            selectFolder(currentFolderPath);
                        }
                    } else {
                        updateProgress(0, totalFiles, 'Processing failed!');
                        addLog(`❌ Batch processing failed: ${response.error}`);
                        showAlert(`Error: ${response.error}`, 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Batch processing error:', status, error);
                    updateProgress(0, totalFiles, 'Processing failed!');
                    addLog(`❌ Server error: ${status} - ${error}`);
                    showAlert('Failed to process files - server error', 'danger');
                },
                complete: function() {
                    $('#processBtn').prop('disabled', false).html('<i class="fas fa-cogs me-2"></i>Process Selected Files');
                    updateStats();

                    // Auto-hide progress after 10 seconds if successful
                    setTimeout(() => {
                        if ($('#progressBar').attr('aria-valuenow') == 100) {
                            $('#progressSection').fadeOut();
                        }
                    }, 10000);
                }
            });
        }

        // Update progress bar
        function updateProgress(current, total, message) {
            const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
            $('#progressBar').css('width', percentage + '%').attr('aria-valuenow', percentage);
            $('#progressText').text(`${percentage}% (${current}/${total})`);
            $('#progressDetails').text(message);
        }

        // Add log entry
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            const logsContainer = $('#operationLogs');

            logsContainer.append(logEntry + '\n');

            // Auto-scroll to bottom
            logsContainer.scrollTop(logsContainer[0].scrollHeight);
        }

        // Clear logs
        function clearLogs() {
            $('#operationLogs').empty();
            addLog('📝 Logs cleared');
        }

        // Test VLC with a sample file
        function testVLC() {
            console.log('🧪 Testing VLC functionality...');

            // Find the first video file in the tree
            let testFile = null;

            function findFirstVideoFile(node) {
                if (node.type === 'file') {
                    const name = node.name.toLowerCase();
                    if (name.endsWith('.mp4') || name.endsWith('.mov') || name.endsWith('.avi') || name.endsWith('.mkv')) {
                        return node;
                    }
                } else if (node.type === 'folder' && node.children) {
                    for (let child of node.children) {
                        const result = findFirstVideoFile(child);
                        if (result) return result;
                    }
                }
                return null;
            }

            // Look for a video file in the loaded tree
            if (window.currentTree) {
                testFile = findFirstVideoFile(window.currentTree);
            }

            if (testFile) {
                console.log('🎥 Found test video file:', testFile.name);
                console.log('📍 Path:', testFile.path);
                showAlert(`Testing VLC with: ${testFile.name}`, 'info');
                openFileInVLC(testFile.path);
            } else {
                // Use a hardcoded test path if no video found
                const testPath = 'T:\\To_Process\\Rough folder\\restored files\\Just output\\Z6016_Talk_For-MACCIA-Event_Sustainability-Summit-Asia-2018_English_14Mins-07Secs_Consolidated_29-Jun-2019\\OUTPUT\\Sadhguru Video to be played at MACCIA Event.mov';
                console.log('🧪 Using hardcoded test path:', testPath);
                showAlert('Testing VLC with sample file...', 'info');
                openFileInVLC(testPath);
            }
        }

        // Filter functions
        function filterFolders() {
            const searchTerm = $('#folderSearch').val().toLowerCase();
            $('.folder-item').each(function() {
                const folderName = $(this).text().toLowerCase();
                if (folderName.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }

        function filterFiles() {
            const searchTerm = $('#fileSearch').val().toLowerCase();
            filteredFiles = allFiles.filter(file =>
                file.name.toLowerCase().includes(searchTerm)
            );
            renderFiles();
            updateStats();
        }

        function applyFileFilter(filter) {
            switch (filter) {
                case 'video':
                    filteredFiles = allFiles.filter(file => isVideoFile(file.name));
                    break;
                case 'audio':
                    filteredFiles = allFiles.filter(file => isAudioFile(file.name));
                    break;
                case 'large':
                    filteredFiles = allFiles.filter(file => file.size > 1024 * 1024 * 1024); // > 1GB
                    break;
                default:
                    filteredFiles = [...allFiles];
            }
            renderFiles();
            updateStats();
        }

        // Utility functions
        function updateStats() {
            $('#totalFiles').text(filteredFiles.length);
            $('#selectedFiles').text(selectedFiles.length);
            $('#processedFiles').text(processedCount);
        }

        function updateProcessButton() {
            const hasSelection = selectedFiles.length > 0;
            const hasCategory = $('#categorySelect').val() !== '';
            const hasUser = $('#userSelect').val() !== '';

            $('#processBtn').prop('disabled', !(hasSelection && hasCategory && hasUser));
        }

        function showAlert(message, type) {
            const alertClass = `alert-${type}`;
            const iconClass = type === 'success' ? 'check-circle' :
                             type === 'danger' ? 'exclamation-triangle' :
                             type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            const alertHtml = `
                <div class="alert ${alertClass} alert-custom alert-dismissible fade show" role="alert">
                    <i class="fas fa-${iconClass} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            $('#alertArea').html(alertHtml);

            // Auto-hide success and info alerts
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    $('.alert').alert('close');
                }, 3000);
            }
        }

        function hideAlert() {
            $('#alertArea').empty();
        }

        function getFileIcon(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            if (['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'].includes(ext)) {
                return 'fas fa-video';
            } else if (['mp3', 'wav', 'flac', 'aac', 'm4a'].includes(ext)) {
                return 'fas fa-music';
            } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)) {
                return 'fas fa-image';
            } else if (['pdf', 'doc', 'docx', 'txt'].includes(ext)) {
                return 'fas fa-file-alt';
            } else {
                return 'fas fa-file';
            }
        }

        function isVideoFile(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            return ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm', 'mxf', 'm4v', '3gp', 'f4v'].includes(ext);
        }

        function isAudioFile(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            return ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma'].includes(ext);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>