#!/usr/bin/env python3
"""
ASSIGNMENT WORKFLOW TEST
Test the actual assignment workflow that the user is experiencing
"""

import requests
import json
from datetime import datetime

def test_assignment_workflow():
    print("🚨 ASSIGNMENT WORKFLOW TEST")
    print("="*60)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Login as assigner
    print("🔐 Logging in as assigner...")
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    print("✅ Login successful")
    
    # Get folders
    print("\n📁 Getting available folders...")
    folders_response = session.get(f"{base_url}/api/get-folders")
    
    if folders_response.status_code != 200:
        print(f"❌ Folders API failed: {folders_response.status_code}")
        return
    
    folders_data = folders_response.json()
    if not folders_data.get('success') or not folders_data.get('folders'):
        print("❌ No folders available")
        return
    
    # Use first folder for testing
    test_folder = folders_data['folders'][0]
    folder_path = test_folder['path']
    folder_name = test_folder['name']
    
    print(f"✅ Using test folder: {folder_name}")
    print(f"✅ Folder path: {folder_path}")
    
    # Create assignment data (this is what the user does in the interface)
    assignment_data = {
        'folder_paths': [folder_path],
        'category': 'Internal video without stems',
        'assign_to': 'Executor Public',
        'video_ids': f'VID-TEST-{datetime.now().strftime("%H%M%S")}',
        'url': 'https://assignment-test.com',
        'remarks': f'Assignment test at {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
    }
    
    print(f"\n📋 Assignment data:")
    print(f"   📁 Folder: {folder_name}")
    print(f"   📂 Category: {assignment_data['category']}")
    print(f"   👤 Assign to: {assignment_data['assign_to']}")
    print(f"   🆔 Video IDs: {assignment_data['video_ids']}")
    print(f"   🔗 URL: {assignment_data['url']}")
    print(f"   📝 Remarks: {assignment_data['remarks']}")
    
    # Submit assignment (this triggers Google Sheets logging)
    print(f"\n🚀 Submitting assignment...")
    assign_response = session.post(f"{base_url}/api/assign-folders", json=assignment_data)
    
    if assign_response.status_code == 200:
        assign_result = assign_response.json()
        print(f"✅ Assignment successful: {assign_result.get('success', False)}")
        print(f"✅ Message: {assign_result.get('message', 'No message')}")
        
        if assign_result.get('success'):
            print(f"✅ Added {assign_result.get('added_count', 0)} items to queue")
            
            # Check queue to see the item
            print(f"\n📊 Checking queue...")
            queue_response = session.get(f"{base_url}/api/queue-status")
            if queue_response.status_code == 200:
                queue_data = queue_response.json()
                items = queue_data.get('items', [])
                
                # Find our test item
                test_item = None
                for item in items:
                    if item.get('folder_name') == folder_name:
                        test_item = item
                        break
                
                if test_item:
                    print(f"✅ Found assignment in queue:")
                    print(f"   📁 Folder: {test_item.get('folder_name')}")
                    print(f"   📂 Category: {test_item.get('category')}")
                    print(f"   👤 Assigned to: {test_item.get('assign_to')}")
                    print(f"   🔗 URL: {test_item.get('url')}")
                    print(f"   📝 Remarks: {test_item.get('remarks')}")
                    print(f"   📊 Status: {test_item.get('status')}")
                    
                    print(f"\n🔍 EXPECTED GOOGLE SHEETS DATA:")
                    print(f"   Column A: {test_item.get('folder_name')}")
                    print(f"   Column B: {test_item.get('added_at', 'Date')}")
                    print(f"   Column C: {test_item.get('category')}")
                    print(f"   Column D: {test_item.get('url')}")
                    print(f"   Column E: {test_item.get('assign_to')}")
                    print(f"   Column F: {test_item.get('remarks')}")
                else:
                    print("❌ Assignment not found in queue")
            else:
                print(f"❌ Queue check failed: {queue_response.status_code}")
        else:
            print(f"❌ Assignment failed: {assign_result.get('error', 'Unknown error')}")
    else:
        print(f"❌ Assignment API failed: {assign_response.status_code}")
        print(f"❌ Response: {assign_response.text}")
    
    print("\n" + "="*60)
    print("🎯 MANUAL VERIFICATION REQUIRED:")
    print("1. Check Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print("2. Look at the 'Records' sheet")
    print("3. Find the row with the test folder name")
    print("4. Verify data is in columns A-F (NOT G-M)")
    print("5. If data is in G-M, there's a bug in the assignment workflow")
    print("="*60)

if __name__ == "__main__":
    test_assignment_workflow()
