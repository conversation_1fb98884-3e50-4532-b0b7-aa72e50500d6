#!/usr/bin/env python3
"""
Test script to verify large file handling and specific folder structure
"""

import requests
import json
import os
import sys

# Configuration
BASE_URL = "http://127.0.0.1:5001"
TEST_USERNAME = "assigner"
TEST_PASSWORD = "Shiva@123"
SPECIFIC_FOLDER = "Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019/Output"

def test_login():
    """Test login functionality"""
    print("🔐 Testing login...")
    
    session = requests.Session()
    
    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    if response.status_code != 200:
        print(f"❌ Failed to access login page: {response.status_code}")
        return None
    
    # Login
    login_data = {
        'username': TEST_USERNAME,
        'password': TEST_PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200 and "dashboard" in response.url:
        print("✅ Login successful")
        return session
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def test_specific_folder_access(session):
    """Test access to the specific folder with .mov files"""
    print(f"📁 Testing access to specific folder: {SPECIFIC_FOLDER}")
    
    test_data = {'folder_path': SPECIFIC_FOLDER}
    response = session.post(
        f"{BASE_URL}/api/folder-contents/",
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            files = data.get('files', [])
            print(f"✅ Successfully accessed folder")
            print(f"   Found {len(files)} files")
            
            # Check for .mov files specifically
            mov_files = [f for f in files if f['name'].lower().endswith('.mov')]
            print(f"   Found {len(mov_files)} .mov files")
            
            # Check file sizes
            large_files = [f for f in files if f['size'] > 100 * 1024 * 1024]  # > 100MB
            if large_files:
                print(f"   Found {len(large_files)} large files (>100MB):")
                for file in large_files[:5]:  # Show first 5
                    size_mb = file['size'] / (1024 * 1024)
                    print(f"     • {file['name']} ({size_mb:.1f} MB)")
            
            return files
        else:
            print(f"❌ Folder access failed: {data.get('error')}")
            return []
    else:
        print(f"❌ Folder access API failed: {response.status_code}")
        return []

def test_large_file_detection(session):
    """Test large file detection API"""
    print("🔍 Testing large file detection...")
    
    response = session.get(f"{BASE_URL}/api/check-large-files")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            large_files = data.get('large_files', [])
            threshold = data.get('threshold_mb', 100)
            total = data.get('total_found', 0)
            
            print(f"✅ Large file detection working")
            print(f"   Threshold: {threshold}MB")
            print(f"   Total large files found: {total}")
            
            if large_files:
                print(f"   Top 5 largest files:")
                for file in large_files[:5]:
                    print(f"     • {file['name']} ({file['size_mb']} MB)")
            
            return True
        else:
            print(f"❌ Large file detection failed: {data.get('error')}")
            return False
    else:
        print(f"❌ Large file detection API failed: {response.status_code}")
        return False

def test_video_serving(session, files):
    """Test video serving for large files"""
    print("🎥 Testing video serving for large files...")
    
    if not files:
        print("⚠️  No files to test video serving")
        return False
    
    # Test with the first file
    test_file = files[0]
    file_path = test_file['path']
    
    # Test video info API
    response = session.get(f"{BASE_URL}/api/video-info", params={'path': file_path})
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            info = data.get('info', {})
            print(f"✅ Video info API working")
            print(f"   File: {info.get('name', 'Unknown')}")
            print(f"   Size: {info.get('size_formatted', 'Unknown')}")
            print(f"   Type: {info.get('type', 'Unknown')}")
            
            # Test video serving (just check if endpoint responds)
            video_response = session.head(f"{BASE_URL}/api/serve-video", params={'path': file_path})
            if video_response.status_code in [200, 206]:  # 206 for partial content
                print(f"✅ Video serving endpoint responding")
                return True
            else:
                print(f"⚠️  Video serving returned: {video_response.status_code}")
                return False
        else:
            print(f"❌ Video info failed: {data.get('error')}")
            return False
    else:
        print(f"❌ Video info API failed: {response.status_code}")
        return False

def test_processing_categories(session):
    """Test that all processing categories are available"""
    print("📋 Testing processing categories...")
    
    expected_categories = [
        "Internal video with stems",
        "Internal video without stems",
        "Miscellaneous",
        "Multiple output only",
        "Multiple output with project file",
        "Multiple output with stems",
        "Private one video",
        "Social media single output with stems",
        "Social media single output without stems",
        "Tamil files",
        "To Be Deleted",
        "For Editing"
    ]
    
    # Access tree assigner page to check categories
    response = session.get(f"{BASE_URL}/tree-assigner")
    
    if response.status_code == 200:
        content = response.text
        missing_categories = []
        
        for category in expected_categories:
            if category not in content:
                missing_categories.append(category)
        
        if not missing_categories:
            print(f"✅ All {len(expected_categories)} processing categories found")
            return True
        else:
            print(f"❌ Missing categories: {missing_categories}")
            return False
    else:
        print(f"❌ Tree assigner page failed: {response.status_code}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Large File and Folder Structure Tests\n")
    
    # Test login
    session = test_login()
    if not session:
        print("\n❌ Cannot proceed without login")
        sys.exit(1)
    
    print()
    
    # Run tests
    tests_results = []
    
    # Test specific folder access
    files = test_specific_folder_access(session)
    tests_results.append(len(files) > 0)
    print()
    
    # Test large file detection
    tests_results.append(test_large_file_detection(session))
    print()
    
    # Test video serving
    tests_results.append(test_video_serving(session, files))
    print()
    
    # Test processing categories
    tests_results.append(test_processing_categories(session))
    print()
    
    # Summary
    passed = sum(tests_results)
    total = len(tests_results)
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Large file handling is working correctly.")
        print("✅ The system can handle:")
        print("   • Large .mov files")
        print("   • Specific folder structure access")
        print("   • Enhanced video serving")
        print("   • All processing categories")
    else:
        print(f"⚠️  {total - passed} tests failed. Please check the issues above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
