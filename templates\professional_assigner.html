<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>🎬 Archives Assignment Console</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --dark-color: #34495e;
            --light-color: #ecf0f1;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --queue-bg: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }

        body {
            background: var(--gradient-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1600px;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .content-section {
            padding: 30px;
        }

        .folder-browser {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid #dee2e6;
        }

        .folder-tree {
            max-height: 400px;
            overflow-y: auto;
            background: white;
            border-radius: 10px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }

        .tree-node {
            margin: 2px 0;
            user-select: none;
        }

        .file-node {
            background: rgba(0,123,255,0.05);
            border-left: 3px solid #007bff;
            border-radius: 6px;
            margin: 1px 0;
            transition: all 0.2s ease;
        }

        .file-node:hover {
            background: rgba(0,123,255,0.15);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(0,123,255,0.2);
        }

        .file-node[data-media="true"] {
            border-left-color: #28a745;
            background: rgba(40,167,69,0.05);
        }

        .file-node[data-media="true"]:hover {
            background: rgba(40,167,69,0.15);
        }

        .folder-node:hover .node-content {
            background: rgba(255,193,7,0.1);
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 0.75rem;
            border-radius: 4px;
        }

        .file-info-modal, .folder-info-modal {
            max-width: 500px;
        }

        .tree-node .btn {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .tree-node:hover .btn {
            opacity: 1;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .selected-folders-list {
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border-radius: 8px;
            padding: 10px;
            border: 1px solid #dee2e6;
        }

        .selected-folder-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 10px;
            margin: 2px 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid var(--secondary-color);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .node-content:hover {
            background: rgba(52, 152, 219, 0.1);
        }

        .folder-node .node-content {
            font-weight: 500;
        }

        .folder-node.selected .node-content {
            background: linear-gradient(135deg, var(--secondary-color), #1976d2);
            color: white;
        }

        .expand-icon {
            cursor: pointer;
            margin-right: 8px;
            transition: transform 0.2s ease;
            width: 16px;
            text-align: center;
        }

        .folder-checkbox {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .folder-children {
            border-left: 2px solid rgba(52, 152, 219, 0.2);
            margin-left: 15px;
            padding-left: 5px;
        }

        .assignment-panel {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .assignment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .form-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 10px;
            display: block;
        }

        .form-select, .form-control {
            border-radius: 8px;
            border: 2px solid #dee2e6;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-select:focus, .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .queue-section {
            background: var(--queue-bg);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid #ff6b9d;
        }

        .queue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .queue-stats {
            display: flex;
            gap: 20px;
        }

        .queue-stat {
            text-align: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 8px;
        }

        .queue-stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .queue-stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        .queue-items {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
        }

        .queue-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 8px;
            border-left: 4px solid var(--secondary-color);
        }

        .queue-item.processing {
            border-left-color: var(--warning-color);
            background: #fff3cd;
        }

        .queue-item.completed {
            border-left-color: var(--success-color);
            background: #d4edda;
        }

        .queue-item.failed {
            border-left-color: var(--accent-color);
            background: #f8d7da;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, var(--secondary-color));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #2ecc71);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #2ecc71, var(--success-color));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--accent-color), #c0392b);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .stats-bar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-item {
            flex: 1;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--secondary-color);
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .progress-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            display: none;
        }

        .progress {
            height: 20px;
            border-radius: 10px;
            background: #e9ecef;
        }

        .progress-bar {
            border-radius: 10px;
            background: linear-gradient(135deg, var(--success-color), #2ecc71);
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-title {
                font-size: 2rem;
            }

            .content-section {
                padding: 20px;
            }

            .assignment-grid {
                grid-template-columns: 1fr;
            }

            .queue-stats {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1 class="header-title">
                <i class="fas fa-archive me-3"></i>Archives Assignment Console
            </h1>
            <p class="header-subtitle">Advanced Folder-Based Video Management & Queue Processing System</p>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Alert Area -->
            <div id="alertArea"></div>

            <!-- Stats Bar -->
            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-number" id="totalFolders">0</div>
                    <div class="stat-label">Total Folders</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="selectedFolders">0</div>
                    <div class="stat-label">Selected Folders</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="queuedFolders">0</div>
                    <div class="stat-label">In Queue</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="processedFolders">0</div>
                    <div class="stat-label">Processed</div>
                </div>
            </div>

            <!-- Folder Browser Section -->
            <div class="folder-browser">
                <h3 class="mb-4">
                    <i class="fas fa-folder-tree me-2"></i>Folder Browser
                    <button class="btn btn-outline-primary btn-sm float-end" onclick="refreshFolders()">
                        <i class="fas fa-refresh me-1"></i>Refresh
                    </button>
                </h3>
                <div class="row">
                    <div class="col-md-8">
                        <div class="folder-tree" id="folderTree">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                                <p class="mt-2">Loading folders...</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="selected-folders-panel">
                            <h5><i class="fas fa-check-square me-2"></i>Selected Folders</h5>
                            <div id="selectedFoldersList" class="selected-folders-list">
                                <p class="text-muted">No folders selected</p>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-outline-secondary btn-sm me-2" onclick="selectAllFolders()">
                                    <i class="fas fa-check-double me-1"></i>Select All
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>Clear All
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assignment Panel -->
            <div class="assignment-panel">
                <h3 class="mb-4">
                    <i class="fas fa-cogs me-2"></i>Assignment Configuration
                </h3>
                <div class="assignment-grid">
                    <div class="form-group">
                        <label class="form-label" for="categorySelect">
                            <i class="fas fa-tags me-1"></i>Destination Category *
                        </label>
                        <select class="form-select" id="categorySelect" required>
                            <option value="">Select Category...</option>
                            {% for category in categories %}
                            <option value="{{ category }}">{{ category }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="assignToSelect">
                            <i class="fas fa-user me-1"></i>Assign To
                        </label>
                        <select class="form-select" id="assignToSelect">
                            <option value="">Select Assignee...</option>
                            <option value="Executor Public">Executor Public</option>
                            <option value="Executor Private">Executor Private</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="videoIdsInput">
                            <i class="fas fa-video me-1"></i>Video IDs
                        </label>
                        <input type="text" class="form-control" id="videoIdsInput"
                               placeholder="e.g., Z6486, Z6371, Z6472">
                        <small class="form-text text-muted">Comma-separated video IDs</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="urlInput">
                            <i class="fas fa-link me-1"></i>URL/Reference
                        </label>
                        <input type="url" class="form-control" id="urlInput"
                               placeholder="https://example.com/video">
                    </div>
                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label class="form-label" for="remarksInput">
                            <i class="fas fa-comment me-1"></i>Remarks
                        </label>
                        <textarea class="form-control" id="remarksInput" rows="3"
                                  placeholder="Additional notes or comments..."></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="operationSelect">
                            <i class="fas fa-exchange-alt me-1"></i>Operation Type
                        </label>
                        <select class="form-select" id="operationSelect">
                            <option value="move">Move Folders</option>
                            <option value="copy">Copy Folders</option>
                        </select>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button class="btn btn-primary btn-lg me-3" onclick="addToQueue()" id="addToQueueBtn">
                        <i class="fas fa-plus me-2"></i>Add to Queue
                    </button>
                    <button class="btn btn-outline-secondary btn-lg" onclick="previewSelection()">
                        <i class="fas fa-eye me-2"></i>Preview Selection
                    </button>
                </div>
            </div>

            <!-- Queue Section -->
            <div class="queue-section">
                <div class="queue-header">
                    <h3 class="mb-0">
                        <i class="fas fa-list-ol me-2"></i>Processing Queue
                    </h3>
                    <div class="queue-stats">
                        <div class="queue-stat">
                            <div class="queue-stat-number" id="queueTotal">0</div>
                            <div class="queue-stat-label">Total</div>
                        </div>
                        <div class="queue-stat">
                            <div class="queue-stat-number" id="queueQueued">0</div>
                            <div class="queue-stat-label">Queued</div>
                        </div>
                        <div class="queue-stat">
                            <div class="queue-stat-number" id="queueProcessing">0</div>
                            <div class="queue-stat-label">Processing</div>
                        </div>
                        <div class="queue-stat">
                            <div class="queue-stat-number" id="queueCompleted">0</div>
                            <div class="queue-stat-label">Completed</div>
                        </div>
                        <div class="queue-stat">
                            <div class="queue-stat-number" id="queueFailed">0</div>
                            <div class="queue-stat-label">Failed</div>
                        </div>
                    </div>
                </div>
                <div class="queue-items" id="queueItems">
                    <div class="text-center py-4 text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>No items in queue</p>
                    </div>
                </div>
                <div class="queue-controls mt-3">
                    <button class="btn btn-success btn-lg me-3" onclick="processQueue()" id="processQueueBtn">
                        <i class="fas fa-play me-2"></i>Process Queue
                    </button>
                    <button class="btn btn-warning btn-lg me-3" onclick="refreshQueue()">
                        <i class="fas fa-refresh me-2"></i>Refresh Queue
                    </button>
                    <button class="btn btn-danger btn-lg" onclick="clearQueue()">
                        <i class="fas fa-trash me-2"></i>Clear Completed
                    </button>
                </div>
            </div>

            <!-- Progress Section -->
            <div class="progress-section" id="progressSection">
                <h4><i class="fas fa-tasks me-2"></i>Processing Progress</h4>
                <div class="progress mb-3">
                    <div class="progress-bar" role="progressbar" id="progressBar" style="width: 0%">
                        <span id="progressText">0%</span>
                    </div>
                </div>
                <div id="progressDetails" class="text-muted">
                    Ready to process...
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let selectedFolders = new Set();
        let folderData = {};
        let queueData = {};

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadFolders();
            refreshQueue();
            setInterval(refreshQueue, 5000); // Auto-refresh queue every 5 seconds
        });

        // Load folder structure
        async function loadFolders() {
            try {
                showAlert('Loading folders...', 'info');
                const response = await fetch('/api/get-folders');
                const data = await response.json();

                if (data.success) {
                    folderData = data.folders;
                    renderFolderTree(data.folders);
                    updateStats();
                    hideAlert();
                } else {
                    showAlert('Error loading folders: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('Error loading folders: ' + error.message, 'danger');
            }
        }

        // Render folder tree
        function renderFolderTree(folders) {
            const treeContainer = document.getElementById('folderTree');
            treeContainer.innerHTML = '';

            if (!folders || folders.length === 0) {
                treeContainer.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-folder-open fa-2x mb-2"></i><p>No folders found</p></div>';
                return;
            }

            folders.forEach(folder => {
                const folderElement = createFolderElement(folder, 0);
                treeContainer.appendChild(folderElement);
            });
        }

        // Create folder or file element with enhanced display
        function createFolderElement(item, level) {
            const div = document.createElement('div');
            const isFile = item.type === 'file';
            div.className = `tree-node ${isFile ? 'file-node' : 'folder-node'}`;
            div.style.marginLeft = (level * 20) + 'px';

            const hasChildren = item.children && item.children.length > 0;

            if (isFile) {
                // File element with enhanced media detection
                const isMedia = item.is_media || false;
                const fileIcon = getFileIcon(item.extension, isMedia);
                const fileColor = isMedia ? 'text-success' : 'text-info';

                div.innerHTML = `
                    <div class="node-content" data-path="${item.path}" data-type="file">
                        <span class="expand-icon" style="visibility: hidden; width: 20px;"></span>
                        <i class="${fileIcon} me-2 ${fileColor}"></i>
                        <span class="file-name" title="${item.name}">${truncateFileName(item.name, 40)}</span>
                        <small class="text-muted ms-2">(${item.size || 'Unknown'})</small>
                        ${isMedia ? `
                            <button class="btn btn-sm btn-success ms-2" onclick="openFileInVLC('${escapeHtml(item.path)}')" title="Open in VLC">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-secondary ms-1" onclick="showFileInfo('${escapeHtml(item.path)}')" title="File Info">
                            <i class="fas fa-info"></i>
                        </button>
                    </div>
                `;
            } else {
                // Folder element with enhanced display
                const folderIcon = hasChildren ? 'fas fa-folder' : 'fas fa-folder-open';
                const mediaCount = item.media_count || item.file_count || 0;

                div.innerHTML = `
                    <div class="node-content" data-path="${item.path}" data-type="folder">
                        <span class="expand-icon" onclick="toggleFolder(this)" style="visibility: ${hasChildren ? 'visible' : 'hidden'}">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                        <input type="checkbox" class="folder-checkbox" onchange="toggleFolderSelection(this.closest('.node-content').dataset.path, this.checked)">
                        <i class="${folderIcon} me-2 text-warning"></i>
                        <span class="folder-name" title="${item.name}">${truncateFileName(item.name, 35)}</span>
                        <small class="text-muted ms-2">(${mediaCount} media files)</small>
                        ${mediaCount > 0 ? `
                            <button class="btn btn-sm btn-warning ms-2" onclick="openFolderInVLC('${escapeHtml(item.path)}')" title="Open first media file in VLC">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-info ms-1" onclick="showFolderInfo('${escapeHtml(item.path)}')" title="Folder Info">
                            <i class="fas fa-info"></i>
                        </button>
                    </div>
                `;
            }

            if (hasChildren) {
                const childrenDiv = document.createElement('div');
                childrenDiv.className = 'folder-children';
                childrenDiv.style.display = 'none';

                item.children.forEach(child => {
                    const childElement = createFolderElement(child, level + 1);
                    childrenDiv.appendChild(childElement);
                });

                div.appendChild(childrenDiv);
            }

            return div;
        }

        // Helper functions for enhanced display
        function getFileIcon(extension, isMedia) {
            if (isMedia) {
                if (['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'].includes(extension)) {
                    return 'fas fa-file-video';
                } else if (['.mp3', '.wav', '.m4a', '.aac', '.ogg'].includes(extension)) {
                    return 'fas fa-file-audio';
                }
            }
            return 'fas fa-file';
        }

        function truncateFileName(name, maxLength) {
            if (name.length <= maxLength) return name;
            return name.substring(0, maxLength - 3) + '...';
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Toggle folder expansion
        function toggleFolder(element) {
            const icon = element.querySelector('i');
            const nodeContent = element.parentElement;
            const treeNode = nodeContent.parentElement;
            const childrenDiv = treeNode.querySelector('.folder-children');

            if (childrenDiv) {
                const isExpanded = childrenDiv.style.display !== 'none';
                childrenDiv.style.display = isExpanded ? 'none' : 'block';
                icon.className = isExpanded ? 'fas fa-chevron-right' : 'fas fa-chevron-down';
            }
        }

        // Toggle folder selection
        function toggleFolderSelection(folderPath, isSelected) {
            if (isSelected) {
                selectedFolders.add(folderPath);
            } else {
                selectedFolders.delete(folderPath);
            }

            updateSelectedFoldersList();
            updateStats();

            // Update visual selection - use safer method to find elements
            const nodeContents = document.querySelectorAll('[data-path]');
            for (const nodeContent of nodeContents) {
                if (nodeContent.dataset.path === folderPath) {
                    const folderNode = nodeContent.parentElement;
                    if (isSelected) {
                        folderNode.classList.add('selected');
                    } else {
                        folderNode.classList.remove('selected');
                    }
                    break;
                }
            }
        }

        // Update selected folders list
        function updateSelectedFoldersList() {
            const listContainer = document.getElementById('selectedFoldersList');

            if (selectedFolders.size === 0) {
                listContainer.innerHTML = '<p class="text-muted">No folders selected</p>';
                return;
            }

            let html = '';
            selectedFolders.forEach(folderPath => {
                const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();
                html += `
                    <div class="selected-folder-item d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <span class="folder-name">${folderName}</span>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFolderSelection('${folderPath}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            });

            listContainer.innerHTML = html;
        }

        // Remove folder from selection
        function removeFolderSelection(folderPath) {
            selectedFolders.delete(folderPath);

            // Uncheck checkbox and remove visual selection - use safer method
            const nodeContents = document.querySelectorAll('[data-path]');
            for (const nodeContent of nodeContents) {
                if (nodeContent.dataset.path === folderPath) {
                    const checkbox = nodeContent.parentElement.querySelector('.folder-checkbox');
                    if (checkbox) {
                        checkbox.checked = false;
                    }
                    nodeContent.parentElement.classList.remove('selected');
                    break;
                }
            }

            updateSelectedFoldersList();
            updateStats();
        }

        // Select all folders
        function selectAllFolders() {
            const checkboxes = document.querySelectorAll('.folder-checkbox');
            checkboxes.forEach(checkbox => {
                if (!checkbox.checked) {
                    checkbox.checked = true;
                    const folderPath = checkbox.closest('.node-content').dataset.path;
                    toggleFolderSelection(folderPath, true);
                }
            });
        }

        // Clear all selections
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.folder-checkbox');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    const folderPath = checkbox.closest('.node-content').dataset.path;
                    toggleFolderSelection(folderPath, false);
                }
            });
        }

        // Update statistics
        function updateStats() {
            document.getElementById('selectedFolders').textContent = selectedFolders.size;

            // Count only top-level folders (those with marginLeft: 0px)
            const allFolderCheckboxes = document.querySelectorAll('.folder-checkbox');
            let topLevelFolderCount = 0;

            allFolderCheckboxes.forEach(checkbox => {
                const treeNode = checkbox.closest('.tree-node');
                if (treeNode && treeNode.style.marginLeft === '0px') {
                    topLevelFolderCount++;
                }
            });

            document.getElementById('totalFolders').textContent = topLevelFolderCount;
            document.getElementById('queuedFolders').textContent = queueData.queued || 0;
            document.getElementById('processedFolders').textContent = queueData.completed || 0;
        }

        // Add folders to queue
        async function addToQueue() {
            if (selectedFolders.size === 0) {
                showAlert('Please select at least one folder', 'warning');
                return;
            }

            const category = document.getElementById('categorySelect').value;
            if (!category) {
                showAlert('Please select a destination category', 'warning');
                return;
            }

            const assignTo = document.getElementById('assignToSelect').value;
            const videoIds = document.getElementById('videoIdsInput').value;
            const url = document.getElementById('urlInput').value;
            const remarks = document.getElementById('remarksInput').value;
            const operationType = document.getElementById('operationSelect').value;

            const button = document.getElementById('addToQueueBtn');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding to Queue...';
            button.disabled = true;

            try {
                const response = await fetch('/api/add-to-queue', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify({
                        folder_paths: Array.from(selectedFolders),
                        category: category,
                        assign_to: assignTo,
                        video_ids: videoIds,
                        url: url,
                        remarks: remarks,
                        operation_type: operationType
                    })
                });

                const data = await response.json();

                // DEBUG: Log the exact response data
                console.log('🔍 DEBUG: Assignment response data:', data);
                console.log('🔍 DEBUG: data.success:', data.success);
                console.log('🔍 DEBUG: data.added:', data.added);
                console.log('🔍 DEBUG: data.failed:', data.failed);
                console.log('🔍 DEBUG: data.failed_items:', data.failed_items);

                if (data.success) {
                    showAlert(`Successfully added ${data.added} folders to queue!`, 'success');
                    if (data.failed > 0) {
                        console.log('🔍 DEBUG: Showing failed warning because data.failed > 0');
                        showAlert(`Failed to add ${data.failed} folders. Check console for details.`, 'warning');
                        console.log('Failed items:', data.failed_items);
                    } else {
                        console.log('🔍 DEBUG: No failed items, not showing warning');
                    }

                    // Clear form and selection
                    clearSelection();
                    document.getElementById('videoIdsInput').value = '';
                    document.getElementById('urlInput').value = '';
                    document.getElementById('remarksInput').value = '';

                    // Refresh queue
                    refreshQueue();
                } else {
                    showAlert('Error adding to queue: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('Error adding to queue: ' + error.message, 'danger');
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        // Process queue
        async function processQueue() {
            if (!queueData.queued || queueData.queued === 0) {
                showAlert('No items in queue to process', 'warning');
                return;
            }

            const button = document.getElementById('processQueueBtn');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            button.disabled = true;

            // Show progress section
            document.getElementById('progressSection').style.display = 'block';

            try {
                const response = await fetch('/api/process-queue', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(`Successfully processed ${data.processed} folders!`, 'success');
                    if (data.failed > 0) {
                        showAlert(`Failed to process ${data.failed} folders. Check queue for details.`, 'warning');
                    }

                    // Update progress
                    const total = data.processed + data.failed;
                    const percentage = total > 0 ? Math.round((data.processed / total) * 100) : 0;
                    updateProgress(percentage, `Processed ${data.processed}/${total} folders`);

                    // Refresh queue and folders
                    refreshQueue();
                    setTimeout(() => {
                        loadFolders(); // Refresh folder list as items have been moved
                    }, 1000);
                } else {
                    showAlert('Error processing queue: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('Error processing queue: ' + error.message, 'danger');
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;

                // Hide progress section after 5 seconds
                setTimeout(() => {
                    document.getElementById('progressSection').style.display = 'none';
                }, 5000);
            }
        }

        // Refresh queue with visual feedback
        async function refreshQueue() {
            try {
                // Show loading state
                const refreshBtn = document.querySelector('button[onclick="refreshQueue()"]');
                const originalText = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';
                refreshBtn.disabled = true;

                const response = await fetch('/api/queue-status');
                const data = await response.json();

                if (data.success) {
                    queueData = data.queue_status;
                    renderQueue(queueData);
                    updateQueueStats(queueData);
                    updateStats();
                    showAlert('Queue refreshed successfully', 'success');
                } else {
                    showAlert('Error refreshing queue: ' + data.error, 'warning');
                }

                // Restore button state
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            } catch (error) {
                console.error('Error refreshing queue:', error);
                showAlert('Error refreshing queue: ' + error.message, 'danger');

                // Restore button state
                const refreshBtn = document.querySelector('button[onclick="refreshQueue()"]');
                refreshBtn.innerHTML = '<i class="fas fa-refresh me-2"></i>Refresh Queue';
                refreshBtn.disabled = false;
            }
        }

        // Render queue items
        function renderQueue(queueStatus) {
            const queueContainer = document.getElementById('queueItems');

            if (!queueStatus.items || queueStatus.items.length === 0) {
                queueContainer.innerHTML = `
                    <div class="text-center py-4 text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>No items in queue</p>
                    </div>
                `;
                return;
            }

            let html = '';
            queueStatus.items.forEach(item => {
                const statusClass = item.status;
                const statusIcon = getStatusIcon(item.status);
                const statusColor = getStatusColor(item.status);

                html += `
                    <div class="queue-item ${statusClass}">
                        <div class="queue-item-info">
                            <div class="d-flex align-items-center">
                                <i class="fas ${statusIcon} me-2" style="color: ${statusColor}"></i>
                                <strong>${item.folder_name}</strong>
                                <span class="badge bg-secondary ms-2">#${item.id}</span>
                            </div>
                            <div class="text-muted small">
                                Category: ${item.category} | Assigned to: ${item.assign_to}
                                ${item.video_ids ? ' | Video IDs: ' + item.video_ids : ''}
                            </div>
                            ${item.error ? `<div class="text-danger small">Error: ${item.error}</div>` : ''}
                        </div>
                        <div class="queue-item-status">
                            <span class="badge bg-${getStatusBadgeColor(item.status)}">${item.status.toUpperCase()}</span>
                        </div>
                    </div>
                `;
            });

            queueContainer.innerHTML = html;
        }

        // Update queue statistics
        function updateQueueStats(queueStatus) {
            document.getElementById('queueTotal').textContent = queueStatus.total || 0;
            document.getElementById('queueQueued').textContent = queueStatus.queued || 0;
            document.getElementById('queueProcessing').textContent = queueStatus.processing || 0;
            document.getElementById('queueCompleted').textContent = queueStatus.completed || 0;
            document.getElementById('queueFailed').textContent = queueStatus.failed || 0;
        }

        // Clear completed queue items with confirmation
        async function clearQueue() {
            // Confirm before clearing
            if (!confirm('Are you sure you want to clear all completed items from the queue?')) {
                return;
            }

            try {
                // Show loading state
                const clearBtn = document.querySelector('button[onclick="clearQueue()"]');
                const originalText = clearBtn.innerHTML;
                clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Clearing...';
                clearBtn.disabled = true;

                const response = await fetch('/api/clear-queue', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(`Queue cleared successfully! Removed ${data.cleared_count || 0} completed items.`, 'success');
                    refreshQueue();
                    loadFolders(); // Refresh folder list as well
                } else {
                    showAlert('Error clearing queue: ' + data.error, 'danger');
                }

                // Restore button state
                clearBtn.innerHTML = originalText;
                clearBtn.disabled = false;
            } catch (error) {
                showAlert('Error clearing queue: ' + error.message, 'danger');

                // Restore button state
                const clearBtn = document.querySelector('button[onclick="clearQueue()"]');
                clearBtn.innerHTML = '<i class="fas fa-trash me-2"></i>Clear Completed';
                clearBtn.disabled = false;
            }
        }

        // Utility functions
        function getStatusIcon(status) {
            switch (status) {
                case 'queued': return 'fa-clock';
                case 'processing': return 'fa-spinner fa-spin';
                case 'completed': return 'fa-check-circle';
                case 'failed': return 'fa-exclamation-circle';
                default: return 'fa-question-circle';
            }
        }

        function getStatusColor(status) {
            switch (status) {
                case 'queued': return '#6c757d';
                case 'processing': return '#f39c12';
                case 'completed': return '#27ae60';
                case 'failed': return '#e74c3c';
                default: return '#6c757d';
            }
        }

        function getStatusBadgeColor(status) {
            switch (status) {
                case 'queued': return 'secondary';
                case 'processing': return 'warning';
                case 'completed': return 'success';
                case 'failed': return 'danger';
                default: return 'secondary';
            }
        }

        // Update progress bar
        function updateProgress(percentage, text) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const progressDetails = document.getElementById('progressDetails');

            progressBar.style.width = percentage + '%';
            progressText.textContent = percentage + '%';
            progressDetails.textContent = text;
        }

        // Show alert
        function showAlert(message, type = 'info') {
            const alertArea = document.getElementById('alertArea');
            const alertId = 'alert-' + Date.now();

            const alertHtml = `
                <div class="alert alert-${type} alert-custom alert-dismissible fade show" id="${alertId}" role="alert">
                    <i class="fas ${getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            alertArea.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.remove();
                }
            }, 5000);
        }

        // Hide all alerts
        function hideAlert() {
            const alertArea = document.getElementById('alertArea');
            alertArea.innerHTML = '';
        }

        function getAlertIcon(type) {
            switch (type) {
                case 'success': return 'fa-check-circle';
                case 'danger': return 'fa-exclamation-triangle';
                case 'warning': return 'fa-exclamation-circle';
                case 'info': return 'fa-info-circle';
                default: return 'fa-info-circle';
            }
        }

        // Open folder in VLC (first video file)
        async function openFolderInVLC(folderPath) {
            try {
                showAlert('Opening first video in folder...', 'info');

                const response = await fetch('/api/open-vlc', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_path: folderPath
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();
                        showAlert(`✅ VLC opened first video in: ${folderName}`, 'success');
                    } else {
                        showAlert(`❌ Error opening VLC: ${result.error}`, 'danger');
                    }
                } else {
                    showAlert(`❌ Error opening VLC: HTTP ${response.status}`, 'danger');
                }
            } catch (error) {
                showAlert(`❌ Error opening VLC: ${error.message}`, 'danger');
            }
        }

        // Open specific file in VLC with enhanced error handling
        async function openFileInVLC(filePath) {
            try {
                showAlert('Opening file in VLC...', 'info');

                const response = await fetch('/api/open-vlc', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_path: filePath
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        const fileName = filePath.split('\\').pop() || filePath.split('/').pop();
                        showAlert(`✅ VLC opened: ${fileName}`, 'success');
                    } else {
                        showAlert(`❌ Error opening VLC: ${result.error}`, 'danger');
                    }
                } else {
                    showAlert(`❌ Error opening VLC: HTTP ${response.status}`, 'danger');
                }
            } catch (error) {
                showAlert(`❌ Error opening VLC: ${error.message}`, 'danger');
            }
        }

        // Show file information
        async function showFileInfo(filePath) {
            try {
                const fileName = filePath.split('\\').pop() || filePath.split('/').pop();
                const fileExt = fileName.split('.').pop().toLowerCase();

                let infoHtml = `
                    <div class="file-info-modal">
                        <h5><i class="fas fa-file me-2"></i>File Information</h5>
                        <hr>
                        <p><strong>Name:</strong> ${fileName}</p>
                        <p><strong>Path:</strong> ${filePath}</p>
                        <p><strong>Extension:</strong> .${fileExt}</p>
                        <p><strong>Type:</strong> ${getFileTypeDescription(fileExt)}</p>
                    </div>
                `;

                // Show in a modal-like alert
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info alert-dismissible fade show';
                alertDiv.innerHTML = `
                    ${infoHtml}
                    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
                `;

                document.getElementById('alertArea').appendChild(alertDiv);

            } catch (error) {
                showAlert(`Error getting file info: ${error.message}`, 'danger');
            }
        }

        // Show folder information
        async function showFolderInfo(folderPath) {
            try {
                const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();

                let infoHtml = `
                    <div class="folder-info-modal">
                        <h5><i class="fas fa-folder me-2"></i>Folder Information</h5>
                        <hr>
                        <p><strong>Name:</strong> ${folderName}</p>
                        <p><strong>Path:</strong> ${folderPath}</p>
                        <p><strong>Type:</strong> Directory</p>
                    </div>
                `;

                // Show in a modal-like alert
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning alert-dismissible fade show';
                alertDiv.innerHTML = `
                    ${infoHtml}
                    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
                `;

                document.getElementById('alertArea').appendChild(alertDiv);

            } catch (error) {
                showAlert(`Error getting folder info: ${error.message}`, 'danger');
            }
        }

        // Get file type description
        function getFileTypeDescription(extension) {
            const types = {
                'mp4': 'MP4 Video File',
                'avi': 'AVI Video File',
                'mov': 'QuickTime Movie',
                'mkv': 'Matroska Video',
                'wmv': 'Windows Media Video',
                'flv': 'Flash Video',
                'webm': 'WebM Video',
                'mp3': 'MP3 Audio File',
                'wav': 'WAV Audio File',
                'm4a': 'M4A Audio File',
                'aac': 'AAC Audio File',
                'ogg': 'OGG Audio File'
            };
            return types[extension] || 'Unknown File Type';
        }

        // Refresh folders
        function refreshFolders() {
            loadFolders();
        }

        // Preview selection
        function previewSelection() {
            if (selectedFolders.size === 0) {
                showAlert('No folders selected for preview', 'warning');
                return;
            }

            const category = document.getElementById('categorySelect').value;
            const assignTo = document.getElementById('assignToSelect').value;
            const videoIds = document.getElementById('videoIdsInput').value;
            const operationType = document.getElementById('operationSelect').value;

            let previewText = `Preview of ${selectedFolders.size} selected folders:\n\n`;
            previewText += `Operation: ${operationType.toUpperCase()}\n`;
            previewText += `Category: ${category || 'Not selected'}\n`;
            previewText += `Assign to: ${assignTo}\n`;
            previewText += `Video IDs: ${videoIds || 'None'}\n\n`;
            previewText += `Selected folders:\n`;

            selectedFolders.forEach((folderPath, index) => {
                const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();
                previewText += `${index + 1}. ${folderName}\n`;
            });

            alert(previewText);
        }

        // Force cache refresh for VLC integration fix
        console.log('🔧 VLC Integration Fix Loaded - Timestamp: ' + new Date().toISOString());
        console.log('✅ Frontend now sends JSON data to VLC API');
        console.log('🎬 VLC buttons should work without 415 errors');
    </script>
</body>
</html>
