<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>🎬 Executive Public - Archives Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #e74c3c;
            --accent-color: #f39c12;
            --success-color: #27ae60;
            --info-color: #3498db;
            --warning-color: #f1c40f;
            --danger-color: #e74c3c;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header-section h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .header-section p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .tab-content {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            background: #f8f9fa;
            color: var(--primary-color);
            font-weight: 600;
            margin-right: 5px;
            padding: 15px 25px;
        }

        .nav-tabs .nav-link.active {
            background: var(--secondary-color);
            color: white;
        }

        .queue-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .queue-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 15px;
            border: none;
        }

        .queue-table td {
            padding: 12px 15px;
            vertical-align: middle;
            border-bottom: 1px solid #eee;
        }

        .queue-table tbody tr:hover {
            background: #f8f9fa;
        }

        .btn-preview {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-preview:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-process {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-process:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .search-filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .notification-area {
            background: var(--info-color);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .metadata-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
        }

        .processing-logs {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress {
            height: 25px;
            border-radius: 12px;
            background: #e9ecef;
        }

        .progress-bar {
            border-radius: 12px;
            background: linear-gradient(45deg, var(--success-color), #2ecc71);
        }

        .alert-custom {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .file-preview-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .social-media-section {
            background: #e8f4fd;
            border: 2px solid var(--info-color);
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
        }

        .batch-processing-section {
            background: #fff3cd;
            border: 2px solid var(--warning-color);
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tooltip-icon {
            color: var(--info-color);
            margin-left: 5px;
            cursor: help;
        }

        .required-field {
            color: var(--danger-color);
        }

        .btn-save-draft {
            background: var(--warning-color);
            color: #333;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .btn-copy-metadata {
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
        }

        /* Tree View Styles (same as Assigner) */
        .tree-node {
            margin: 2px 0;
            user-select: none;
        }

        .file-node {
            background: rgba(0,123,255,0.05);
            border-radius: 4px;
            padding: 2px 5px;
        }

        .folder-node {
            background: rgba(255,193,7,0.05);
            border-radius: 4px;
            padding: 2px 5px;
        }

        .folder-node.selected {
            background: rgba(40,167,69,0.2);
            border: 1px solid #28a745;
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .node-content:hover {
            background: rgba(0,123,255,0.1);
        }

        .expand-icon {
            cursor: pointer;
            width: 20px;
            text-align: center;
            color: #6c757d;
        }

        .expand-icon:hover {
            color: #007bff;
        }

        .folder-children {
            border-left: 1px dashed #dee2e6;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-user-tie me-3"></i>Executive Public Dashboard</h1>
            <p>File Processing & Metadata Management System</p>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="queue-tab" data-bs-toggle="tab" data-bs-target="#queue-pane" type="button" role="tab">
                    <i class="fas fa-list me-2"></i>Files in Queue to be Processed
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="folder-tab" data-bs-toggle="tab" data-bs-target="#folder-pane" type="button" role="tab">
                    <i class="fas fa-folder-tree me-2"></i>Folder Navigation
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="processing-tab" data-bs-toggle="tab" data-bs-target="#processing-pane" type="button" role="tab">
                    <i class="fas fa-cogs me-2"></i>File Processing
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Queue Tab -->
            <div class="tab-pane fade show active" id="queue-pane" role="tabpanel">
                <!-- Notification Area -->
                <div class="notification-area" id="notificationArea">
                    <i class="fas fa-bell me-2"></i>
                    <span id="notificationText">Welcome! Loading your file queue...</span>
                </div>

                <!-- Search and Filter Section -->
                <div class="search-filter-section">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Search Files</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="Search by file name or folder...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Sort By</label>
                            <select class="form-select" id="sortBy">
                                <option value="name">File Name</option>
                                <option value="size">File Size</option>
                                <option value="date">Date</option>
                                <option value="folder">Folder</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Filter Size</label>
                            <select class="form-select" id="filterSize">
                                <option value="">All Sizes</option>
                                <option value="small">< 1GB</option>
                                <option value="medium">1-5GB</option>
                                <option value="large">> 5GB</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Filter Date</label>
                            <input type="date" class="form-control" id="filterDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100" onclick="applyFilters()">
                                <i class="fas fa-filter me-2"></i>Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Queue Table -->
                <div class="table-responsive">
                    <table class="table queue-table" id="queueTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll"></th>
                                <th>File Name</th>
                                <th>File Size</th>
                                <th>Number of Files</th>
                                <th>Assigned Date</th>
                                <th>Folder Path</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="queueTableBody">
                            <!-- Dynamic content will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Batch Actions -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <button class="btn btn-copy-metadata" onclick="showBatchProcessing()">
                            <i class="fas fa-copy me-2"></i>Batch Process Selected
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <span id="selectedCount">0 files selected</span>
                    </div>
                </div>
            </div>

            <!-- Folder Navigation Tab -->
            <div class="tab-pane fade" id="folder-pane" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-folder-tree me-2"></i>Folder Structure</h5>
                            </div>
                            <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                                <div id="folderTreeContainer">
                                    <div class="text-center py-4">
                                        <div class="loading-spinner"></div>
                                        <p class="mt-2">Loading folder structure...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>Folder Details</h5>
                            </div>
                            <div class="card-body">
                                <div id="folderDetailsContainer">
                                    <p class="text-muted">Select a folder to view details</p>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5><i class="fas fa-play me-2"></i>Video Preview</h5>
                            </div>
                            <div class="card-body">
                                <div id="videoPreviewContainer">
                                    <div class="text-center p-4 bg-light rounded">
                                        <i class="fas fa-video fa-3x text-muted mb-3"></i>
                                        <p>Select a video file to preview</p>
                                        <button class="btn btn-primary" id="openVlcBtn" style="display: none;" onclick="openSelectedInVlc()">
                                            <i class="fas fa-external-link-alt me-2"></i>Open in VLC
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Tab -->
            <div class="tab-pane fade" id="processing-pane" role="tabpanel">
                <!-- File Preview Section -->
                <div class="file-preview-section" id="filePreviewSection" style="display: none;">
                    <h4><i class="fas fa-play-circle me-2"></i>File Preview</h4>
                    <div class="row">
                        <div class="col-md-8">
                            <div id="vlcPreview" class="text-center p-4 bg-light rounded">
                                <i class="fas fa-video fa-3x text-muted mb-3"></i>
                                <p>Select a file to preview</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>File Metadata</h6>
                            <div id="fileMetadata">
                                <p><strong>Duration:</strong> <span id="fileDuration">--</span></p>
                                <p><strong>Resolution:</strong> <span id="fileResolution">--</span></p>
                                <p><strong>Format:</strong> <span id="fileFormat">--</span></p>
                                <p><strong>Size:</strong> <span id="fileSize">--</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Metadata Entry Form -->
                <div class="metadata-form" id="metadataForm">
                    <h4><i class="fas fa-edit me-2"></i>Metadata Entry Form</h4>
                    <p class="text-muted">Complete all required fields marked with <span class="required-field">*</span></p>
                    
                    <form id="metadataEntryForm">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        OCD/VP Number <span class="required-field">*</span>
                                        <i class="fas fa-info-circle tooltip-icon" title="Unique identifier for internal tracking"></i>
                                    </label>
                                    <input type="text" class="form-control" id="ocdVpNumber" placeholder="OCD-2025-001" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        Edited File Name <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="editedFileName" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Language <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="language" required>
                                        <option value="">Select Language</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Edited Year <span class="required-field">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="editedYear" min="2000" max="2030" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Trim Closed Date</label>
                                    <input type="date" class="form-control" id="trimClosedDate">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Total Duration (HH:MM:SS) <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="totalDuration" placeholder="00:05:30" pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Type of Video <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="videoType" required>
                                        <option value="">Select Video Type</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Edited in Ashram/US <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="editedLocation" required>
                                        <option value="">Select Location</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Metadata Fields -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Published Platforms <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="publishedPlatforms" required>
                                        <option value="">Select Platform</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Department Name <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="departmentName" required>
                                        <option value="">Select Department</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Component <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="component" required>
                                        <option value="">Select Component</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Content Tag <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="contentTag" required>
                                        <option value="">Select Content Tag</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Backup Type <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="backupType" required>
                                        <option value="">Select Backup Type</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        Access Level <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="accessLevel" required>
                                        <option value="">Select Access Level</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Software Show / Renamed Folder Name</label>
                                    <input type="text" class="form-control" id="renamedFolderName" placeholder="New folder name">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Number of Files</label>
                                    <input type="number" class="form-control" id="numberOfFiles" min="1">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Size of Folder (GB/MB)</label>
                                    <input type="text" class="form-control" id="folderSize" placeholder="e.g., 2.5 GB">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">AUDIO/Transcript Code</label>
                                    <input type="text" class="form-control" id="audioTranscriptCode" placeholder="AUD-2025-001">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Audio File Name</label>
                                    <input type="text" class="form-control" id="audioFileName">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Transcription File Name</label>
                                    <input type="text" class="form-control" id="transcriptionFileName">
                                </div>
                            </div>
                        </div>

                        <!-- Audio Extraction Section -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-volume-up me-2"></i>Audio Extraction</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label">Select Audio File from Folder</label>
                                                    <select class="form-select" id="audioFileSelect">
                                                        <option value="">Select audio file...</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label">Extraction Progress</label>
                                                    <div class="progress" id="audioExtractionProgress" style="display: none;">
                                                        <div class="progress-bar bg-info" role="progressbar" style="width: 0%" id="audioProgressBar">
                                                            <span id="audioProgressText">0%</span>
                                                        </div>
                                                    </div>
                                                    <button type="button" class="btn btn-info" id="extractAudioBtn" onclick="extractAudio()">
                                                        <i class="fas fa-download me-2"></i>Extract Audio (.wav)
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Audio will be extracted in .wav format and saved to: T:\To_Process\Rough folder\Folder to be cross checked
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Social Media Details Section -->
                        <div class="social-media-section">
                            <h5><i class="fas fa-share-alt me-2"></i>Social Media Details</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Published Date</label>
                                        <input type="date" class="form-control" id="socialPublishedDate">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Video ID</label>
                                        <input type="text" class="form-control" id="socialVideoId" placeholder="VID-2025-001">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Social Media Duration</label>
                                        <select class="form-select" id="socialDuration">
                                            <option value="">Select Duration</option>
                                            <option value="<1min">&lt;1min</option>
                                            <option value="1-3min">1-3min</option>
                                            <option value="3-5min">3-5min</option>
                                            <option value=">5min">&gt;5min</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Social Media Title</label>
                                        <input type="text" class="form-control" id="socialTitle" placeholder="Enter social media title">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Social Media URL</label>
                                        <input type="url" class="form-control" id="socialUrl" placeholder="https://...">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Social Media Description</label>
                                        <textarea class="form-control" id="socialDescription" rows="3" placeholder="Enter description..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Batch Processing Section -->
                        <div class="batch-processing-section" id="batchSection" style="display: none;">
                            <h5><i class="fas fa-layer-group me-2"></i>Batch Processing Options</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-copy-metadata" onclick="copyMetadataToSelected()">
                                        <i class="fas fa-copy me-2"></i>Copy Metadata to Selected Files
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-warning" onclick="processBatch()">
                                        <i class="fas fa-layer-group me-2"></i>Process Batch
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-save-draft me-2" onclick="saveDraft()">
                                    <i class="fas fa-save me-2"></i>Save Draft
                                </button>
                                <button type="button" class="btn btn-process" onclick="processMetadata()">
                                    <i class="fas fa-check me-2"></i>Process & Submit
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="clearForm()">
                                    <i class="fas fa-times me-2"></i>Clear Form
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Processing Logs -->
                <div class="processing-logs" id="processingLogs" style="display: none;">
                    <h6><i class="fas fa-terminal me-2"></i>Processing Logs</h6>
                    <div id="logContent">
                        Ready to process files...
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-container" id="progressContainer" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript will be added in the next part -->
    <script>
        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎬 Executive Public Dashboard Loaded');
            loadQueue();
            loadMetadataOptions();

            // Add event listener for folder tab
            document.getElementById('folder-tab').addEventListener('click', function() {
                loadFolderTree();
            });
        });

        // Load files in queue
        async function loadQueue() {
            try {
                const response = await fetch('/api/executive-public/queue');
                const data = await response.json();
                
                if (data.success) {
                    displayQueueFiles(data.files);
                    updateNotification(`${data.total_count} files in queue for processing`);
                } else {
                    updateNotification('Error loading queue: ' + data.error, 'error');
                }
            } catch (error) {
                updateNotification('Error loading queue: ' + error.message, 'error');
            }
        }

        // Display queue files in table
        function displayQueueFiles(files) {
            const tbody = document.getElementById('queueTableBody');
            tbody.innerHTML = '';

            files.forEach((file, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="file-checkbox" value="${file.folder_path}"></td>
                    <td>${file.folder_name}</td>
                    <td>${file.total_size}</td>
                    <td>${file.file_count}</td>
                    <td>${file.assigned_date}</td>
                    <td title="${file.folder_path}">${file.folder_path.substring(0, 50)}...</td>
                    <td>
                        <button class="btn btn-preview btn-sm me-2" onclick="previewFile('${file.folder_path}')">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                        <button class="btn btn-process btn-sm" onclick="processFile('${file.folder_path}')">
                            <i class="fas fa-cogs"></i> Process
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update checkbox listeners
            updateCheckboxListeners();
        }

        // Update notification area
        function updateNotification(message, type = 'info') {
            const notificationArea = document.getElementById('notificationArea');
            const notificationText = document.getElementById('notificationText');
            
            notificationText.textContent = message;
            
            // Update styling based on type
            notificationArea.className = 'notification-area';
            if (type === 'error') {
                notificationArea.style.background = 'var(--danger-color)';
            } else if (type === 'success') {
                notificationArea.style.background = 'var(--success-color)';
            } else {
                notificationArea.style.background = 'var(--info-color)';
            }
        }

        // Load metadata options
        async function loadMetadataOptions() {
            try {
                const response = await fetch('/api/executive-public/metadata-options');
                const data = await response.json();
                
                if (data.success) {
                    populateDropdowns(data.options);
                }
            } catch (error) {
                console.error('Error loading metadata options:', error);
            }
        }

        // Populate dropdown options
        function populateDropdowns(options) {
            // Populate all dropdowns with their respective options
            populateSelect('language', options.languages);
            populateSelect('videoType', options.video_types);
            populateSelect('editedLocation', options.edited_locations);
            populateSelect('publishedPlatforms', options.published_platforms);
            populateSelect('departmentName', options.departments);
            populateSelect('component', options.components);
            populateSelect('contentTag', options.content_tags);
            populateSelect('backupType', options.backup_types);
            populateSelect('accessLevel', options.access_levels);
        }

        function populateSelect(selectId, options) {
            const select = document.getElementById(selectId);
            if (select) {
                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = option;
                    select.appendChild(optionElement);
                });
            }
        }

        // Preview file function
        function previewFile(folderPath) {
            // Switch to processing tab
            document.getElementById('processing-tab').click();
            
            // Show file preview section
            document.getElementById('filePreviewSection').style.display = 'block';
            
            // Load file for preview (VLC integration)
            loadFilePreview(folderPath);
        }

        // Process file function
        function processFile(folderPath) {
            // Switch to processing tab and load metadata form
            document.getElementById('processing-tab').click();
            
            // Pre-populate form with file information
            populateMetadataForm(folderPath);
        }

        // Additional functions will be added in the next part...
        
        function updateCheckboxListeners() {
            // Add checkbox event listeners
            const checkboxes = document.querySelectorAll('.file-checkbox');
            const selectAll = document.getElementById('selectAll');
            
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedCount);
            });
            
            selectAll.addEventListener('change', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateSelectedCount();
            });
        }

        function updateSelectedCount() {
            const checked = document.querySelectorAll('.file-checkbox:checked').length;
            document.getElementById('selectedCount').textContent = `${checked} files selected`;
        }

        // Load file preview with VLC integration
        async function loadFilePreview(folderPath) {
            try {
                // Find first video file in folder and open with VLC
                const response = await fetch('/api/open-vlc', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_path: folderPath
                    })
                });

                const result = await response.json();
                if (result.success) {
                    updateNotification('File opened in VLC for preview', 'success');

                    // Update file metadata display
                    document.getElementById('fileDuration').textContent = 'Auto-detecting...';
                    document.getElementById('fileResolution').textContent = 'Auto-detecting...';
                    document.getElementById('fileFormat').textContent = 'Video';
                    document.getElementById('fileSize').textContent = 'Calculating...';
                } else {
                    updateNotification('Error opening file: ' + result.error, 'error');
                }
            } catch (error) {
                updateNotification('Error previewing file: ' + error.message, 'error');
            }
        }

        // Populate metadata form with file information
        function populateMetadataForm(folderPath) {
            // Auto-populate some fields based on file path
            const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();

            // Set current year as default
            document.getElementById('editedYear').value = new Date().getFullYear();

            // Generate OCD/VP number
            const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
            document.getElementById('ocdVpNumber').value = `OCD-${timestamp}-001`;

            // Set edited file name based on folder name
            document.getElementById('editedFileName').value = folderName;

            // Store current folder path for processing
            document.getElementById('metadataForm').setAttribute('data-folder-path', folderPath);
        }

        // Save draft functionality
        function saveDraft() {
            const formData = collectFormData();

            // Save to localStorage for now
            localStorage.setItem('metadata_draft', JSON.stringify(formData));

            updateNotification('Draft saved successfully', 'success');
        }

        // Process metadata and submit
        async function processMetadata() {
            try {
                const folderPath = document.getElementById('metadataForm').getAttribute('data-folder-path');
                const metadata = collectFormData();

                // Validate required fields
                if (!validateForm(metadata)) {
                    updateNotification('Please fill all required fields', 'error');
                    return;
                }

                // Show processing logs
                document.getElementById('processingLogs').style.display = 'block';
                document.getElementById('progressContainer').style.display = 'block';

                addLog('Starting metadata processing...');
                updateProgress(10, 'Validating metadata...');

                // Submit metadata
                const response = await fetch('/api/executive-public/process-metadata', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        folder_path: folderPath,
                        metadata: metadata
                    })
                });

                updateProgress(50, 'Processing metadata...');

                const result = await response.json();

                if (result.success) {
                    updateProgress(100, 'Processing complete!');
                    addLog('✅ Metadata processed successfully');
                    addLog('📁 Folder moved to cross-checker queue');
                    addLog(`📍 Destination: ${result.destination_path}`);

                    updateNotification('File processed successfully and moved to cross-checker queue', 'success');

                    // Clear form and reload queue
                    document.getElementById('metadataEntryForm').reset();
                    setTimeout(() => {
                        loadQueue();
                        document.getElementById('queue-tab').click();
                    }, 2000);
                } else {
                    updateProgress(0, 'Processing failed');
                    addLog('❌ Error: ' + result.error);
                    updateNotification('Error processing metadata: ' + result.error, 'error');
                }

            } catch (error) {
                updateProgress(0, 'Processing failed');
                addLog('❌ Exception: ' + error.message);
                updateNotification('Error processing metadata: ' + error.message, 'error');
            }
        }

        // Collect form data
        function collectFormData() {
            return {
                ocd_vp_number: document.getElementById('ocdVpNumber').value,
                edited_file_name: document.getElementById('editedFileName').value,
                language: document.getElementById('language').value,
                edited_year: document.getElementById('editedYear').value,
                trim_closed_date: document.getElementById('trimClosedDate').value,
                total_duration: document.getElementById('totalDuration').value,
                video_type: document.getElementById('videoType').value,
                edited_location: document.getElementById('editedLocation').value,
                published_platforms: document.getElementById('publishedPlatforms').value,
                department_name: document.getElementById('departmentName').value,
                component: document.getElementById('component').value,
                content_tag: document.getElementById('contentTag').value,
                backup_type: document.getElementById('backupType').value,
                access_level: document.getElementById('accessLevel').value,
                renamed_folder_name: document.getElementById('renamedFolderName').value,
                number_of_files: document.getElementById('numberOfFiles').value,
                folder_size: document.getElementById('folderSize').value,
                audio_transcript_code: document.getElementById('audioTranscriptCode').value,
                audio_file_name: document.getElementById('audioFileName').value,
                transcription_file_name: document.getElementById('transcriptionFileName').value,
                social_published_date: document.getElementById('socialPublishedDate').value,
                social_video_id: document.getElementById('socialVideoId').value,
                social_duration: document.getElementById('socialDuration').value,
                social_title: document.getElementById('socialTitle').value,
                social_url: document.getElementById('socialUrl').value,
                social_description: document.getElementById('socialDescription').value
            };
        }

        // Validate form
        function validateForm(metadata) {
            const requiredFields = [
                'ocd_vp_number', 'edited_file_name', 'language', 'edited_year',
                'total_duration', 'video_type', 'edited_location', 'published_platforms',
                'department_name', 'component', 'content_tag', 'backup_type', 'access_level'
            ];

            for (const field of requiredFields) {
                if (!metadata[field] || metadata[field].trim() === '') {
                    return false;
                }
            }
            return true;
        }

        // Clear form function
        function clearForm() {
            document.getElementById('metadataEntryForm').reset();
            updateNotification('Form cleared', 'info');
        }

        // Copy metadata to selected files
        function copyMetadataToSelected() {
            const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked'));
            if (selectedFiles.length === 0) {
                updateNotification('Please select files to copy metadata to', 'error');
                return;
            }

            const metadata = collectFormData();
            if (!validateForm(metadata)) {
                updateNotification('Please complete the metadata form first', 'error');
                return;
            }

            // Store metadata for batch processing
            sessionStorage.setItem('batch_metadata', JSON.stringify(metadata));
            updateNotification(`Metadata copied to ${selectedFiles.length} files`, 'success');
        }

        // Process batch files
        async function processBatch() {
            const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedFiles.length === 0) {
                updateNotification('Please select files for batch processing', 'error');
                return;
            }

            const batchMetadata = sessionStorage.getItem('batch_metadata');
            if (!batchMetadata) {
                updateNotification('Please copy metadata first', 'error');
                return;
            }

            // Show processing logs
            document.getElementById('processingLogs').style.display = 'block';
            document.getElementById('progressContainer').style.display = 'block';

            addLog(`Starting batch processing for ${selectedFiles.length} files...`);

            let processed = 0;
            const total = selectedFiles.length;

            for (const filePath of selectedFiles) {
                try {
                    addLog(`Processing: ${filePath.split('\\').pop()}`);

                    const response = await fetch('/api/executive-public/process-metadata', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            folder_path: filePath,
                            metadata: JSON.parse(batchMetadata)
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        processed++;
                        addLog(`✅ Processed: ${filePath.split('\\').pop()}`);
                    } else {
                        addLog(`❌ Failed: ${filePath.split('\\').pop()} - ${result.error}`);
                    }

                    updateProgress((processed / total) * 100, `Processed ${processed}/${total} files`);

                } catch (error) {
                    addLog(`❌ Error processing ${filePath.split('\\').pop()}: ${error.message}`);
                }
            }

            addLog(`Batch processing complete. Processed ${processed}/${total} files.`);
            updateNotification(`Batch processing complete. ${processed}/${total} files processed successfully`, 'success');

            // Reload queue after batch processing
            setTimeout(() => {
                loadQueue();
                document.getElementById('queue-tab').click();
            }, 2000);
        }

        // Add log entry
        function addLog(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `\n[${timestamp}] ${message}`;
            logContent.scrollTop = logContent.scrollHeight;
        }

        // Update progress bar
        function updateProgress(percentage, text) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            progressBar.style.width = percentage + '%';
            progressText.textContent = text || `${percentage}%`;
        }

        // Apply filters to queue table
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const sortBy = document.getElementById('sortBy').value;
            const filterSize = document.getElementById('filterSize').value;
            const filterDate = document.getElementById('filterDate').value;

            // Implement filtering logic here
            updateNotification('Filters applied', 'info');
        }

        // Show batch processing modal/section
        function showBatchProcessing() {
            const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedFiles.length === 0) {
                updateNotification('Please select files for batch processing', 'error');
                return;
            }

            updateNotification(`Batch processing ${selectedFiles.length} files`, 'info');
            // Implement batch processing UI
        }

        // Auto-save functionality
        setInterval(() => {
            const formData = collectFormData();
            if (Object.values(formData).some(value => value && value.trim() !== '')) {
                localStorage.setItem('metadata_autosave', JSON.stringify(formData));
            }
        }, 30000); // Auto-save every 30 seconds

        // Load auto-saved data on page load
        window.addEventListener('load', () => {
            const autoSaved = localStorage.getItem('metadata_autosave');
            if (autoSaved) {
                try {
                    const data = JSON.parse(autoSaved);
                    // Populate form with auto-saved data
                    Object.keys(data).forEach(key => {
                        const element = document.getElementById(key.replace(/_/g, ''));
                        if (element && data[key]) {
                            element.value = data[key];
                        }
                    });
                } catch (error) {
                    console.error('Error loading auto-saved data:', error);
                }
            }
        });

        // Folder Tree Functionality
        let selectedFolderPath = null;
        let selectedVideoFile = null;

        async function loadFolderTree() {
            try {
                const response = await fetch('/api/get-folders');
                const data = await response.json();

                if (data.success) {
                    renderFolderTree(data.tree);
                } else {
                    document.getElementById('folderTreeContainer').innerHTML =
                        '<div class="alert alert-danger">Error loading folders: ' + data.error + '</div>';
                }
            } catch (error) {
                console.error('Error loading folder tree:', error);
                document.getElementById('folderTreeContainer').innerHTML =
                    '<div class="alert alert-danger">Failed to load folder structure</div>';
            }
        }

        // Render folder tree (same as Assigner)
        function renderFolderTree(data) {
            const container = document.getElementById('folderTreeContainer');
            container.innerHTML = '';

            if (!data.folders || data.folders.length === 0) {
                container.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-folder-open fa-2x mb-2"></i><p>No folders found</p></div>';
                return;
            }

            data.folders.forEach(folder => {
                const folderElement = createFolderElement(folder, 0);
                container.appendChild(folderElement);
            });
        }

        // Create folder or file element (same as Assigner)
        function createFolderElement(item, level) {
            const div = document.createElement('div');
            const isFile = item.type === 'file';
            div.className = `tree-node ${isFile ? 'file-node' : 'folder-node'}`;
            div.style.marginLeft = (level * 20) + 'px';

            const hasChildren = item.children && item.children.length > 0;

            if (isFile) {
                // File element with enhanced media detection
                const isMedia = item.is_media || false;
                const fileIcon = getFileIcon(item.extension, isMedia);
                const fileColor = isMedia ? 'text-success' : 'text-info';

                div.innerHTML = `
                    <div class="node-content" data-path="${item.path}" data-type="file">
                        <span class="expand-icon" style="visibility: hidden; width: 20px;"></span>
                        <i class="${fileIcon} me-2 ${fileColor}"></i>
                        <span class="file-name" title="${item.name}">${truncateFileName(item.name, 40)}</span>
                        <small class="text-muted ms-2">(${item.size || 'Unknown'})</small>
                        ${isMedia ? `
                            <button class="btn btn-sm btn-success ms-2" onclick="openFileInVLC('${escapeHtml(item.path)}')" title="Open in VLC">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-primary ms-1" onclick="selectFileForProcessing('${escapeHtml(item.path)}')" title="Select for Processing">
                            <i class="fas fa-cogs"></i>
                        </button>
                    </div>
                `;
            } else {
                // Folder element with enhanced display
                const folderIcon = hasChildren ? 'fas fa-folder' : 'fas fa-folder-open';
                const mediaCount = item.media_count || item.file_count || 0;

                div.innerHTML = `
                    <div class="node-content" data-path="${item.path}" data-type="folder">
                        <span class="expand-icon" onclick="toggleExecutorFolder(this)" style="visibility: ${hasChildren ? 'visible' : 'hidden'}">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                        <i class="${folderIcon} me-2 text-warning"></i>
                        <span class="folder-name" title="${item.name}">${truncateFileName(item.name, 35)}</span>
                        <small class="text-muted ms-2">(${mediaCount} media files)</small>
                        ${mediaCount > 0 ? `
                            <button class="btn btn-sm btn-warning ms-2" onclick="openFolderInVLC('${escapeHtml(item.path)}')" title="Open first media file in VLC">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-success ms-1" onclick="selectFolderForProcessing('${escapeHtml(item.path)}')" title="Select Folder for Processing">
                            <i class="fas fa-cogs"></i>
                        </button>
                    </div>
                `;
            }

            if (hasChildren) {
                const childrenDiv = document.createElement('div');
                childrenDiv.className = 'folder-children';
                childrenDiv.style.display = 'none';

                item.children.forEach(child => {
                    const childElement = createFolderElement(child, level + 1);
                    childrenDiv.appendChild(childElement);
                });

                div.appendChild(childrenDiv);
            }

            return div;
        }

        // Helper functions (same as Assigner)
        function getFileIcon(extension, isMedia) {
            if (isMedia) {
                if (['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'].includes(extension)) {
                    return 'fas fa-file-video';
                } else if (['.mp3', '.wav', '.m4a', '.aac', '.ogg'].includes(extension)) {
                    return 'fas fa-file-audio';
                }
            }
            return 'fas fa-file';
        }

        function truncateFileName(name, maxLength) {
            if (name.length <= maxLength) return name;
            return name.substring(0, maxLength - 3) + '...';
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Toggle folder expansion (same as Assigner)
        function toggleExecutorFolder(element) {
            const icon = element.querySelector('i');
            const nodeContent = element.parentElement;
            const treeNode = nodeContent.parentElement;
            const childrenDiv = treeNode.querySelector('.folder-children');

            if (childrenDiv) {
                const isExpanded = childrenDiv.style.display !== 'none';
                childrenDiv.style.display = isExpanded ? 'none' : 'block';
                icon.className = isExpanded ? 'fas fa-chevron-right' : 'fas fa-chevron-down';
            }
        }

        function toggleFolder(element, folderPath) {
            const childrenDiv = element.parentElement.querySelector('.children');
            const toggleIcon = element.querySelector('.toggle-icon');

            if (childrenDiv) {
                if (childrenDiv.style.display === 'none') {
                    childrenDiv.style.display = 'block';
                    toggleIcon.classList.remove('fa-chevron-right');
                    toggleIcon.classList.add('fa-chevron-down');
                } else {
                    childrenDiv.style.display = 'none';
                    toggleIcon.classList.remove('fa-chevron-down');
                    toggleIcon.classList.add('fa-chevron-right');
                }
            }

            // Load folder details
            loadFolderDetails(folderPath);
        }

        function selectFile(filePath) {
            selectedVideoFile = filePath;

            // Check if it's a video file
            const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'];
            const isVideo = videoExtensions.some(ext => filePath.toLowerCase().endsWith(ext));

            if (isVideo) {
                document.getElementById('videoPreviewContainer').innerHTML = `
                    <div class="text-center p-4 bg-light rounded">
                        <i class="fas fa-video fa-3x text-success mb-3"></i>
                        <p><strong>Selected Video:</strong></p>
                        <p class="small text-muted">${filePath}</p>
                        <button class="btn btn-primary" onclick="openSelectedInVlc()">
                            <i class="fas fa-external-link-alt me-2"></i>Open in VLC
                        </button>
                    </div>
                `;
            } else {
                document.getElementById('videoPreviewContainer').innerHTML = `
                    <div class="text-center p-4 bg-light rounded">
                        <i class="fas fa-file fa-3x text-info mb-3"></i>
                        <p><strong>Selected File:</strong></p>
                        <p class="small text-muted">${filePath}</p>
                        <p class="text-muted">Not a video file</p>
                    </div>
                `;
            }
        }

        async function loadFolderDetails(folderPath) {
            selectedFolderPath = folderPath;

            try {
                const response = await fetch(`/api/folder-details?path=${encodeURIComponent(folderPath)}`);
                const data = await response.json();

                if (data.success) {
                    const details = data.details;
                    document.getElementById('folderDetailsContainer').innerHTML = `
                        <h6><i class="fas fa-folder me-2"></i>${details.name}</h6>
                        <hr>
                        <p><strong>Path:</strong> <small>${details.path}</small></p>
                        <p><strong>Files:</strong> ${details.file_count}</p>
                        <p><strong>Size:</strong> ${details.size}</p>
                        <p><strong>Video Files:</strong> ${details.video_files}</p>
                        <p><strong>Audio Files:</strong> ${details.audio_files}</p>

                        <div class="mt-3">
                            <button class="btn btn-sm btn-primary me-2" onclick="previewFolderInVlc('${folderPath}')">
                                <i class="fas fa-play me-1"></i>Preview Videos
                            </button>
                            <button class="btn btn-sm btn-success" onclick="processFolderFromTree('${folderPath}')">
                                <i class="fas fa-cogs me-1"></i>Process Folder
                            </button>
                        </div>
                    `;
                } else {
                    document.getElementById('folderDetailsContainer').innerHTML =
                        '<div class="alert alert-warning">Error loading folder details</div>';
                }
            } catch (error) {
                console.error('Error loading folder details:', error);
                document.getElementById('folderDetailsContainer').innerHTML =
                    '<div class="alert alert-danger">Failed to load folder details</div>';
            }
        }

        async function openSelectedInVlc() {
            if (!selectedVideoFile) {
                alert('No video file selected');
                return;
            }

            try {
                const response = await fetch('/api/open-vlc', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `file_path=${encodeURIComponent(selectedVideoFile)}`
                });

                const data = await response.json();
                if (data.success) {
                    updateNotification('Video opened in VLC successfully', 'success');
                } else {
                    updateNotification('Error opening video: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error opening VLC:', error);
                updateNotification('Failed to open VLC', 'danger');
            }
        }

        async function previewFolderInVlc(folderPath) {
            try {
                const response = await fetch('/api/preview-folder-vlc', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ folder_path: folderPath })
                });

                const data = await response.json();
                if (data.success) {
                    updateNotification(`Opened ${data.video_count} videos in VLC`, 'success');
                } else {
                    updateNotification('Error previewing folder: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error previewing folder:', error);
                updateNotification('Failed to preview folder', 'danger');
            }
        }

        function processFolderFromTree(folderPath) {
            // Switch to processing tab and pre-fill the folder
            document.getElementById('processing-tab').click();

            // Set the current file for processing
            currentFile = {
                folder_path: folderPath,
                folder_name: folderPath.split('\\').pop() || folderPath.split('/').pop()
            };

            updateNotification(`Ready to process: ${currentFile.folder_name}`, 'info');

            // Scroll to metadata form
            document.getElementById('metadataForm').scrollIntoView({ behavior: 'smooth' });
        }

        // Audio Extraction Functionality
        async function extractAudio() {
            const audioFileSelect = document.getElementById('audioFileSelect');
            const selectedFile = audioFileSelect.value;

            if (!selectedFile) {
                alert('Please select an audio file to extract');
                return;
            }

            if (!currentFile) {
                alert('No folder selected for processing');
                return;
            }

            const extractBtn = document.getElementById('extractAudioBtn');
            const progressContainer = document.getElementById('audioExtractionProgress');
            const progressBar = document.getElementById('audioProgressBar');
            const progressText = document.getElementById('audioProgressText');

            // Show progress and disable button
            extractBtn.disabled = true;
            extractBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Extracting...';
            progressContainer.style.display = 'block';

            try {
                const response = await fetch('/api/extract-audio', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        folder_path: currentFile.folder_path,
                        audio_file: selectedFile,
                        destination: 'T:\\To_Process\\Rough folder\\Folder to be cross checked'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Simulate progress
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += 10;
                        progressBar.style.width = progress + '%';
                        progressText.textContent = progress + '%';

                        if (progress >= 100) {
                            clearInterval(progressInterval);
                            updateNotification('Audio extracted successfully to cross-check folder', 'success');

                            // Update audio file name field
                            document.getElementById('audioFileName').value = data.extracted_file_name;
                        }
                    }, 200);
                } else {
                    updateNotification('Error extracting audio: ' + data.error, 'danger');
                    progressContainer.style.display = 'none';
                }
            } catch (error) {
                console.error('Error extracting audio:', error);
                updateNotification('Failed to extract audio', 'danger');
                progressContainer.style.display = 'none';
            } finally {
                extractBtn.disabled = false;
                extractBtn.innerHTML = '<i class="fas fa-download me-2"></i>Extract Audio (.wav)';
            }
        }

        // Load audio files when a folder is selected for processing
        function loadAudioFiles(folderPath) {
            fetch(`/api/get-audio-files?folder_path=${encodeURIComponent(folderPath)}`)
                .then(response => response.json())
                .then(data => {
                    const audioSelect = document.getElementById('audioFileSelect');
                    audioSelect.innerHTML = '<option value="">Select audio file...</option>';

                    if (data.success && data.audio_files.length > 0) {
                        data.audio_files.forEach(file => {
                            const option = document.createElement('option');
                            option.value = file.path;
                            option.textContent = `${file.name} (${file.size})`;
                            audioSelect.appendChild(option);
                        });
                        console.log(`Loaded ${data.audio_files.length} audio files for extraction`);
                    } else {
                        audioSelect.innerHTML = '<option value="">No audio files found</option>';
                        console.log('No audio files found in folder');
                    }
                })
                .catch(error => {
                    console.error('Error loading audio files:', error);
                    document.getElementById('audioFileSelect').innerHTML = '<option value="">Error loading files</option>';
                });
        }

        // Auto-load audio files when a file is selected for processing
        function selectFileForProcessing(filePath) {
            currentFile = {
                folder_path: filePath,
                folder_name: filePath.split('\\').pop() || filePath.split('/').pop()
            };

            // Load audio files for extraction
            loadAudioFiles(filePath);

            updateNotification(`Selected for processing: ${currentFile.folder_name}`, 'info');
        }

        // Select folder for processing (from tree view)
        function selectFolderForProcessing(folderPath) {
            currentFile = {
                folder_path: folderPath,
                folder_name: folderPath.split('\\').pop() || folderPath.split('/').pop()
            };

            // Switch to processing tab
            document.getElementById('processing-tab').click();

            // Load audio files for extraction
            loadAudioFiles(folderPath);

            updateNotification(`Selected folder for processing: ${currentFile.folder_name}`, 'success');

            // Scroll to metadata form
            setTimeout(() => {
                document.getElementById('metadataForm').scrollIntoView({ behavior: 'smooth' });
            }, 300);
        }

        // VLC integration functions
        function openFileInVLC(filePath) {
            fetch('/api/preview-file-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ file_path: filePath })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotification('Opening file in VLC...', 'success');
                } else {
                    updateNotification('Error opening file in VLC: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                updateNotification('Error opening file in VLC', 'danger');
            });
        }

        function openFolderInVLC(folderPath) {
            fetch('/api/preview-folder-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ folder_path: folderPath })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotification('Opening first media file in VLC...', 'success');
                } else {
                    updateNotification('Error opening folder in VLC: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                updateNotification('Error opening folder in VLC', 'danger');
            });
        }
    </script>
</body>
</html>
