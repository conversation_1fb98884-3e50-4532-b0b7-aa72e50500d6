#!/usr/bin/env python3
"""
DIRECT GOOGLE SHEETS API TEST
Test the Google Sheets API directly to see where data is being written
"""

import os
import sys
from datetime import datetime

def test_direct_google_sheets_api():
    print("🔧 DIRECT GOOGLE SHEETS API TEST")
    print("="*60)
    
    try:
        import gspread
        from google.oauth2.service_account import Credentials
        print("✅ Successfully imported Google Sheets modules")
    except ImportError as e:
        print(f"❌ Failed to import Google Sheets modules: {e}")
        return
    
    # Test the exact same logic as log_to_google_sheets function
    try:
        credentials_path = os.path.join(os.path.dirname(__file__), "credentials.json")
        if not os.path.exists(credentials_path):
            print(f"❌ Credentials file not found: {credentials_path}")
            return
        
        print(f"✅ Found credentials file")
        
        # Set up Google Sheets connection (same as log_to_google_sheets)
        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/spreadsheets"
        ]
        
        creds = Credentials.from_service_account_file(credentials_path, scopes=scope)
        client = gspread.authorize(creds)
        
        # Open the specific sheet (same as log_to_google_sheets)
        sheet_id = "13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4"
        sheet_name = "Records"
        sheet = client.open_by_key(sheet_id).worksheet(sheet_name)
        
        print(f"✅ Connected to Google Sheets")
        print(f"✅ Sheet ID: {sheet_id}")
        print(f"✅ Sheet Name: {sheet_name}")
        
        # Create test data (same format as log_to_google_sheets)
        timestamp = datetime.now().strftime("%H%M%S")
        
        row_data = [
            f"DIRECT_API_TEST_{timestamp}",                    # Column A
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),     # Column B
            "Internal video without stems",                    # Column C
            f"https://direct-api-test-{timestamp}.com",       # Column D
            "Executor Public",                                 # Column E
            f"DIRECT API TEST - COLUMNS A-F - {timestamp}"    # Column F
        ]
        
        print(f"\n📝 Test data (should go to columns A-F):")
        print(f"   📊 Column A: {row_data[0]}")
        print(f"   📊 Column B: {row_data[1]}")
        print(f"   📊 Column C: {row_data[2]}")
        print(f"   📊 Column D: {row_data[3]}")
        print(f"   📊 Column E: {row_data[4]}")
        print(f"   📊 Column F: {row_data[5]}")
        
        # Get current sheet data to see where we're appending
        all_values = sheet.get_all_values()
        current_rows = len(all_values)
        next_row = current_rows + 1
        
        print(f"\n📊 Sheet status:")
        print(f"   📋 Current rows: {current_rows}")
        print(f"   📋 Next row: {next_row}")
        
        # Test 1: Use append_row (same as log_to_google_sheets)
        print(f"\n🚀 TEST 1: Using append_row (same as log_to_google_sheets)")
        print("-" * 50)
        
        result = sheet.append_row(row_data)
        print(f"✅ append_row result: {result}")
        
        # Check where the data actually went
        print(f"\n🔍 Checking where data was written...")
        
        # Get the updated sheet data
        updated_values = sheet.get_all_values()
        new_rows = len(updated_values)
        
        if new_rows > current_rows:
            # Get the last row (where our data should be)
            last_row = updated_values[-1]
            print(f"✅ New row added. Total rows: {new_rows}")
            print(f"📊 Last row data:")
            
            # Check each column
            for i, value in enumerate(last_row):
                if value:  # Only show non-empty columns
                    column_letter = chr(65 + i)  # A=65, B=66, etc.
                    print(f"   📊 Column {column_letter} ({i+1}): {value}")
            
            # Check if our test data is in the expected columns
            if len(last_row) >= 6:
                expected_matches = 0
                for i, expected_value in enumerate(row_data):
                    if i < len(last_row) and str(last_row[i]) == str(expected_value):
                        expected_matches += 1
                        column_letter = chr(65 + i)
                        print(f"   ✅ Column {column_letter}: MATCH")
                    else:
                        column_letter = chr(65 + i)
                        actual_value = last_row[i] if i < len(last_row) else "EMPTY"
                        print(f"   ❌ Column {column_letter}: Expected '{expected_value}', Got '{actual_value}'")
                
                if expected_matches == 6:
                    print(f"\n🎉 SUCCESS: Data correctly written to columns A-F!")
                else:
                    print(f"\n❌ ISSUE: Only {expected_matches}/6 columns match expected values")
            else:
                print(f"❌ ISSUE: Row has only {len(last_row)} columns, expected at least 6")
        else:
            print(f"❌ ISSUE: No new row was added")
        
        # Test 2: Check if data appears in columns M-R
        print(f"\n🔍 TEST 2: Checking columns M-R for unexpected data")
        print("-" * 50)
        
        if len(last_row) >= 18:  # Column R is position 18 (0-based)
            columns_m_to_r = last_row[12:18]  # M=12, N=13, O=14, P=15, Q=16, R=17
            has_data_in_m_r = any(col.strip() for col in columns_m_to_r if col)
            
            if has_data_in_m_r:
                print(f"⚠️  WARNING: Found data in columns M-R:")
                for i, value in enumerate(columns_m_to_r):
                    if value.strip():
                        column_letter = chr(77 + i)  # M=77, N=78, etc.
                        print(f"   📊 Column {column_letter} ({i+13}): {value}")
                print(f"❌ This confirms the user's report of data in columns M-R!")
            else:
                print(f"✅ No data found in columns M-R (this is correct)")
        else:
            print(f"✅ Row doesn't extend to columns M-R")
        
    except Exception as e:
        print(f"❌ Error during Google Sheets test: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print(f"\n🎯 CONCLUSION:")
    print("="*60)
    print(f"1. If data appears in columns A-F: ✅ API is working correctly")
    print(f"2. If data appears in columns M-R: ❌ There's a bug in the API or sheet setup")
    print(f"3. Check the Google Sheets manually to verify the results")
    print(f"4. Look for: DIRECT_API_TEST_{timestamp}")
    
    print(f"\n🔍 MANUAL VERIFICATION:")
    print(f"Open: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print(f"Search for: DIRECT_API_TEST_{timestamp}")
    print(f"Check which columns contain the data")
    print("="*60)

if __name__ == "__main__":
    test_direct_google_sheets_api()
