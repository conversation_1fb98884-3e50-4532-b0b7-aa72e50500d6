#!/usr/bin/env python3
"""
TEST BROWSER BEHAVIOR
Simulate exact browser behavior to identify the issue
"""

import requests
import json
import time

def test_browser_behavior():
    print("🔍 TESTING BROWSER BEHAVIOR")
    print("="*50)
    
    session = requests.Session()
    
    # Step 1: <PERSON><PERSON> as assigner
    print("\n1. <PERSON><PERSON> as assigner...")
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
    print(f"Login status: {login_response.status_code}")
    
    # Step 2: Get folders (simulate browser folder loading)
    print("\n2. Get folders...")
    folders_response = session.get('http://127.0.0.1:5001/api/get-folders')
    folders_data = folders_response.json()
    folders = folders_data.get('folders', [])
    test_folder = folders[0]['path'] if folders else None
    print(f"Test folder: {test_folder}")
    
    # Step 3: Simulate exact browser assignment request
    print("\n3. Simulate browser assignment...")
    
    # This is the exact data structure the browser sends
    browser_data = {
        'folder_paths': [test_folder],
        'category': 'Social media outputs with stems',
        'assign_to': 'Executor Public',
        'video_ids': 'TEST001',
        'url': 'https://test.com',
        'remarks': 'Browser simulation test',
        'operation_type': 'copy'
    }
    
    print("Browser request data:")
    print(json.dumps(browser_data, indent=2))
    
    # Make the request exactly like the browser
    assign_response = session.post(
        'http://127.0.0.1:5001/api/add-to-queue',
        json=browser_data,
        headers={
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    )
    
    print(f"\n4. Response analysis:")
    print(f"Status code: {assign_response.status_code}")
    print(f"Headers: {dict(assign_response.headers)}")
    
    try:
        response_data = assign_response.json()
        print(f"\n5. Response data:")
        print(json.dumps(response_data, indent=2))
        
        # Analyze the response like the browser JavaScript would
        print(f"\n6. JavaScript analysis:")
        print(f"data.success: {response_data.get('success')}")
        print(f"data.added: {response_data.get('added')}")
        print(f"data.failed: {response_data.get('failed')}")
        print(f"data.failed_items: {response_data.get('failed_items')}")
        
        # Simulate the exact JavaScript logic
        if response_data.get('success'):
            print(f"✅ SUCCESS MESSAGE: Successfully added {response_data.get('added')} folders to queue!")
            
            # This is the problematic line in the JavaScript
            if response_data.get('failed', 0) > 0:
                print(f"⚠️  WARNING MESSAGE: Failed to add {response_data.get('failed')} folders. Check console for details.")
                print(f"Failed items: {response_data.get('failed_items')}")
            else:
                print("✅ No failed items - no warning message should appear")
        else:
            print(f"❌ ERROR MESSAGE: Error adding to queue: {response_data.get('error')}")
            
    except Exception as e:
        print(f"❌ Failed to parse JSON: {e}")
        print(f"Raw response: {assign_response.text}")
    
    # Step 4: Check if there are any timing issues
    print(f"\n7. Checking for timing issues...")
    time.sleep(1)
    
    # Check queue status immediately after
    queue_response = session.get('http://127.0.0.1:5001/api/queue-status')
    if queue_response.status_code == 200:
        queue_data = queue_response.json()
        if queue_data.get('success'):
            queue_status = queue_data.get('queue_status', {})
            print(f"Queue total: {queue_status.get('total', 0)}")
            print(f"Queue queued: {queue_status.get('queued', 0)}")
            print(f"Queue failed: {queue_status.get('failed', 0)}")
            
            # Check if any items have failed status
            items = queue_status.get('items', [])
            failed_items = [item for item in items if item.get('status') == 'failed']
            if failed_items:
                print(f"⚠️  Found {len(failed_items)} failed items in queue:")
                for item in failed_items:
                    print(f"  - {item.get('folder_name')}: {item.get('error')}")
            else:
                print("✅ No failed items in queue")
    
    print(f"\n8. Conclusion:")
    print("If the API returns success=True, added=1, failed=0, but the browser still shows")
    print("'Failed to add 1 folders', then the issue is in the frontend JavaScript logic.")

if __name__ == "__main__":
    test_browser_behavior()
