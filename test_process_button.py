#!/usr/bin/env python3
"""
Test the Process button functionality in the Enhanced Executive Public Interface
"""

import requests
import json
import time

def test_process_button():
    print("🔧 TESTING PROCESS BUTTON FUNCTIONALITY")
    print("=" * 60)
    
    # Create a session
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    # Step 1: Login
    print("1. 🔐 Logging in as Executor Public...")
    login_data = {
        'username': 'executor_public',
        'password': 'Shiva@123'
    }
    
    try:
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code in [200, 302]:
            print("   ✅ Login successful!")
        else:
            print(f"   ❌ Login failed: {login_response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return
    
    # Step 2: Test Queue API
    print("\n2. 📋 Testing Queue API...")
    try:
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        if queue_response.status_code == 200:
            queue_data = queue_response.json()
            if queue_data.get('success'):
                files = queue_data.get('files', [])
                print(f"   ✅ Queue API working - {len(files)} files found")
                
                if files:
                    # Test with first file
                    test_file = files[0]
                    queue_item_id = test_file.get('queue_item_id')
                    folder_name = test_file.get('folder_name')
                    
                    print(f"   📁 Testing with file: {folder_name}")
                    print(f"   🆔 Queue Item ID: {queue_item_id}")
                    
                    # Step 3: Test File Details API (what Process button calls)
                    print("\n3. 🔍 Testing File Details API...")
                    details_response = session.get(f"{base_url}/api/file-details/{queue_item_id}")
                    
                    if details_response.status_code == 200:
                        details_data = details_response.json()
                        if details_data.get('success'):
                            file_details = details_data.get('file_details', {})
                            print("   ✅ File Details API working!")
                            print(f"      📁 Folder: {file_details.get('folder_name')}")
                            print(f"      📊 Files: {file_details.get('file_count')}")
                            print(f"      💾 Size: {file_details.get('total_size_formatted')}")
                            print(f"      🎬 Duration: {file_details.get('detected_duration')}")
                            print(f"      🔧 Suggested codes: {bool(file_details.get('suggested_codes'))}")
                            
                            # Step 4: Test Metadata Options API
                            print("\n4. 📝 Testing Metadata Options API...")
                            options_response = session.get(f"{base_url}/api/executive-public/metadata-options")
                            
                            if options_response.status_code == 200:
                                options_data = options_response.json()
                                if options_data.get('success'):
                                    options = options_data.get('options', {})
                                    print("   ✅ Metadata Options API working!")
                                    print(f"      🎬 Video Types: {len(options.get('video_types', []))}")
                                    print(f"      🌍 Languages: {len(options.get('languages', []))}")
                                    print(f"      🏷️ Content Tags: {len(options.get('content_tags', []))}")
                                else:
                                    print(f"   ❌ Metadata options error: {options_data.get('error')}")
                            else:
                                print(f"   ❌ Metadata options API failed: {options_response.status_code}")
                            
                            print("\n" + "=" * 60)
                            print("🎉 PROCESS BUTTON TEST RESULTS")
                            print("=" * 60)
                            print("✅ Login working")
                            print("✅ Queue API working")
                            print("✅ File Details API working")
                            print("✅ Metadata Options API working")
                            print("\n🎯 WHAT TO CHECK IN BROWSER:")
                            print("1. Open: http://127.0.0.1:5001/executor-public")
                            print("2. Login with: executor_public / Shiva@123")
                            print("3. Click 'Process' button on any file")
                            print("4. Check browser console (F12) for any JavaScript errors")
                            print("5. The comprehensive metadata modal should open")
                            print("\n🔧 IF PROCESS BUTTON STILL SHOWS ERROR:")
                            print("- Check browser console for JavaScript errors")
                            print("- Ensure all required HTML elements are present")
                            print("- Verify the processFileEnhanced() function is called")
                            
                        else:
                            print(f"   ❌ File details error: {details_data.get('error')}")
                    else:
                        print(f"   ❌ File details API failed: {details_response.status_code}")
                        if details_response.status_code == 403:
                            print("   🔒 Permission denied - check role requirements")
                        elif details_response.status_code == 404:
                            print("   📁 File not found - check queue item ID")
                        elif details_response.status_code == 500:
                            print("   🔥 Server error - check Flask logs")
                else:
                    print("   ℹ️ No files in queue to test")
            else:
                print(f"   ❌ Queue API error: {queue_data.get('error')}")
        else:
            print(f"   ❌ Queue API failed: {queue_response.status_code}")
    except Exception as e:
        print(f"   ❌ Queue API test error: {e}")

if __name__ == "__main__":
    test_process_button()
