<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archives Assignment Console - Assigner Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand { font-weight: bold; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .folder-item { cursor: pointer; transition: all 0.2s; }
        .folder-item:hover { background-color: #f8f9fa; }
        .folder-selected { background-color: #e3f2fd; border-left: 4px solid #2196f3; }
        .progress-container { min-height: 20px; }
        .status-badge { font-size: 0.75rem; }
        .tree-item { padding: 0.25rem 0.5rem; margin: 0.1rem 0; border-radius: 0.25rem; }
        .tree-item:hover { background-color: #f8f9fa; }
        .tree-toggle { cursor: pointer; width: 20px; text-align: center; }
        .tree-content { margin-left: 20px; }
        .file-icon { color: #6c757d; margin-right: 0.5rem; }
        .folder-icon { color: #ffc107; margin-right: 0.5rem; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-archive me-2"></i>Archives Assignment Console
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ session.username }} ({{ session.role }})
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-1">
                                    <i class="fas fa-tasks me-2 text-primary"></i>Archives Assignment Console
                                </h4>
                                <p class="text-muted mb-0">Assign and manage archive processing tasks</p>
                            </div>
                            <div class="text-end">
                                <div class="row text-center">
                                    <div class="col">
                                        <h5 class="mb-0 text-primary" id="totalFolders">0</h5>
                                        <small class="text-muted">Total Folders</small>
                                    </div>
                                    <div class="col">
                                        <h5 class="mb-0 text-success" id="assignedFolders">0</h5>
                                        <small class="text-muted">Assigned</small>
                                    </div>
                                    <div class="col">
                                        <h5 class="mb-0 text-warning" id="pendingFolders">0</h5>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <!-- Folder Tree -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-folder-tree me-2"></i>Folder Structure
                            </h5>
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshFolders()">
                                <i class="fas fa-sync-alt me-1"></i>Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="folderTreeContainer" style="height: 600px; overflow-y: auto;">
                            <div class="text-center py-5" id="loadingState">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-3 text-muted">Loading folder structure...</p>
                            </div>
                            <div id="folderTree" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assignment Panel -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>Assignment Panel
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="assignmentContent">
                            <div class="text-center py-5 text-muted">
                                <i class="fas fa-mouse-pointer fa-4x mb-3"></i>
                                <h5>Select a Folder</h5>
                                <p>Click on a folder from the tree to start assignment</p>
                            </div>
                        </div>

                        <!-- Assignment Form (Hidden initially) -->
                        <div id="assignmentForm" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">Selected Folder</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-folder"></i></span>
                                    <input type="text" class="form-control" id="selectedFolderName" readonly>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="assignTo" class="form-label">Assign To</label>
                                <select class="form-select" id="assignTo" required>
                                    <option value="">Select executor...</option>
                                    <option value="Executor Public">Executor Public</option>
                                    <option value="Executor Private">Executor Private</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select class="form-select" id="priority">
                                    <option value="Normal">Normal</option>
                                    <option value="High">High</option>
                                    <option value="Urgent">Urgent</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category">
                                    <option value="">Select category...</option>
                                    <option value="Internal Only Output">Internal Only Output</option>
                                    <option value="Social Media Only Output">Social Media Only Output</option>
                                    <option value="Internal with Project File">Internal with Project File</option>
                                    <option value="Private one video">Private one video</option>
                                    <option value="Tamil files">Tamil files</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Assignment Notes</label>
                                <textarea class="form-control" id="notes" rows="3" placeholder="Add any special instructions or notes..."></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="deadline" class="form-label">Deadline (Optional)</label>
                                <input type="datetime-local" class="form-control" id="deadline">
                            </div>

                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="assignFolder()">
                                    <i class="fas fa-paper-plane me-1"></i>Assign Folder
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>Clear Selection
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Assignments -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>Recent Assignments
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Folder Name</th>
                                        <th>Assigned To</th>
                                        <th>Category</th>
                                        <th>Priority</th>
                                        <th>Assigned Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="recentAssignmentsTable">
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-2x mb-2"></i>
                                            <br>No recent assignments
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let selectedFolder = null;
        let folderData = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            refreshFolders();
            loadRecentAssignments();
        });

        // Refresh folders
        async function refreshFolders() {
            try {
                document.getElementById('loadingState').style.display = 'block';
                document.getElementById('folderTree').style.display = 'none';

                const response = await fetch('/api/folder-tree');
                const data = await response.json();

                if (data.success) {
                    folderData = data.folders;
                    displayFolderTree(data.folders);
                    updateStats(data.folders);
                } else {
                    showAlert('Error loading folders: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error loading folders:', error);
                showAlert('Failed to load folder structure', 'danger');
            } finally {
                document.getElementById('loadingState').style.display = 'none';
            }
        }

        // Display folder tree
        function displayFolderTree(folders) {
            const container = document.getElementById('folderTree');
            let html = '';

            folders.forEach(folder => {
                html += `
                    <div class="tree-item folder-item" onclick="selectFolder('${folder.name}', '${folder.path}')">
                        <i class="fas fa-folder folder-icon"></i>
                        <strong>${folder.name}</strong>
                        <span class="badge bg-secondary ms-2">${folder.file_count} files</span>
                        <span class="badge bg-info ms-1">${folder.size}</span>
                    </div>
                `;
            });

            container.innerHTML = html;
            container.style.display = 'block';
        }

        // Select folder
        function selectFolder(folderName, folderPath) {
            selectedFolder = { name: folderName, path: folderPath };
            
            // Update UI
            document.querySelectorAll('.folder-item').forEach(item => {
                item.classList.remove('folder-selected');
            });
            event.currentTarget.classList.add('folder-selected');

            // Show assignment form
            document.getElementById('assignmentContent').style.display = 'none';
            document.getElementById('assignmentForm').style.display = 'block';
            document.getElementById('selectedFolderName').value = folderName;
        }

        // Clear selection
        function clearSelection() {
            selectedFolder = null;
            document.querySelectorAll('.folder-item').forEach(item => {
                item.classList.remove('folder-selected');
            });
            document.getElementById('assignmentContent').style.display = 'block';
            document.getElementById('assignmentForm').style.display = 'none';
        }

        // Assign folder
        async function assignFolder() {
            if (!selectedFolder) {
                showAlert('Please select a folder first', 'warning');
                return;
            }

            const assignTo = document.getElementById('assignTo').value;
            const priority = document.getElementById('priority').value;
            const category = document.getElementById('category').value;
            const notes = document.getElementById('notes').value;
            const deadline = document.getElementById('deadline').value;

            if (!assignTo) {
                showAlert('Please select who to assign to', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/assign-folder', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        folder_name: selectedFolder.name,
                        folder_path: selectedFolder.path,
                        assign_to: assignTo,
                        priority: priority,
                        category: category,
                        notes: notes,
                        deadline: deadline
                    })
                });

                const data = await response.json();
                if (data.success) {
                    showAlert('Folder assigned successfully!', 'success');
                    clearSelection();
                    loadRecentAssignments();
                    refreshFolders();
                } else {
                    showAlert('Error assigning folder: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error assigning folder:', error);
                showAlert('Failed to assign folder', 'danger');
            }
        }

        // Load recent assignments
        async function loadRecentAssignments() {
            try {
                const response = await fetch('/api/recent-assignments');
                const data = await response.json();

                if (data.success) {
                    displayRecentAssignments(data.assignments);
                }
            } catch (error) {
                console.error('Error loading recent assignments:', error);
            }
        }

        // Display recent assignments
        function displayRecentAssignments(assignments) {
            const tbody = document.getElementById('recentAssignmentsTable');
            
            if (!assignments || assignments.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <br>No recent assignments
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            assignments.forEach(assignment => {
                const statusClass = assignment.status === 'Completed' ? 'success' : 
                                  assignment.status === 'In Progress' ? 'warning' : 'secondary';
                
                html += `
                    <tr>
                        <td><strong>${assignment.folder_name}</strong></td>
                        <td>${assignment.assigned_to}</td>
                        <td>${assignment.category || 'N/A'}</td>
                        <td><span class="badge bg-${assignment.priority === 'Urgent' ? 'danger' : assignment.priority === 'High' ? 'warning' : 'secondary'}">${assignment.priority}</span></td>
                        <td>${formatDate(assignment.assigned_date)}</td>
                        <td><span class="badge bg-${statusClass}">${assignment.status}</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-info" onclick="viewAssignment('${assignment.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // Update stats
        function updateStats(folders) {
            document.getElementById('totalFolders').textContent = folders.length;
            // These would be calculated based on actual assignment data
            document.getElementById('assignedFolders').textContent = '0';
            document.getElementById('pendingFolders').textContent = folders.length;
        }

        // Utility functions
        function showAlert(message, type) {
            // Create and show Bootstrap alert
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
        }

        function viewAssignment(assignmentId) {
            // Placeholder for viewing assignment details
            showAlert('Assignment details view not implemented yet', 'info');
        }
    </script>
</body>
</html>
