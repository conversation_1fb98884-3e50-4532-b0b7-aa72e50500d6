#!/usr/bin/env python3
"""
Comprehensive Testing Suite for Archives Department File Management System
Tests all interfaces, functionality, and user workflows with real files and folders
"""

import os
import sys
import time
import json
import requests
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

class ArchivesSystemTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.test_files_created = []
        
        # Test user credentials
        self.test_users = {
            'admin': {'username': 'admin', 'password': 'Shiva@123', 'role': 'Main Admin'},
            'assigner': {'username': 'assigner', 'password': 'Shiva@123', 'role': 'Assigner'},
            'executor_public': {'username': 'executor_public', 'password': 'Shiva@123', 'role': 'Executor Public'},
            'executor_private': {'username': 'executor_private', 'password': 'Shiva@123', 'role': 'Executor Private'},
            'cross_checker': {'username': 'crosschecker', 'password': 'Shiva@123', 'role': 'Cross Checker'}
        }
        
        # Test paths
        self.test_paths = {
            'source': r"T:\To_Process\Rough folder\restored files",
            'cross_check': r"T:\To_Process\Rough folder\Folder to be cross checked",
            'audio_tr': r"T:\To_Process\Rough folder\Audio Sent to TR",
            'audio_repo': r"T:\To_Process\Rough folder\Audios Repository",
            'to_reingest': r"T:\To_Process\Rough folder\To Reingest",
            'to_delete': r"T:\To_Process\Rough folder\To Be Deleted"
        }

    def log_test(self, test_name, status, details="", duration=0):
        """Log test results"""
        result = {
            'test_name': test_name,
            'status': status,
            'details': details,
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status} ({duration:.2f}s)")
        if details:
            print(f"   Details: {details}")

    def setup_test_environment(self):
        """Setup test environment with sample files and folders"""
        print("\n🔧 Setting up test environment...")
        
        try:
            # Create test directories
            for path_name, path in self.test_paths.items():
                os.makedirs(path, exist_ok=True)
                self.log_test(f"Create directory: {path_name}", "PASS", path)
            
            # Create sample test files
            test_folder = os.path.join(self.test_paths['source'], "TEST_FOLDER_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
            os.makedirs(test_folder, exist_ok=True)
            self.test_files_created.append(test_folder)
            
            # Create sample video file (dummy)
            test_video = os.path.join(test_folder, "test_video.mp4")
            with open(test_video, 'wb') as f:
                f.write(b'FAKE_VIDEO_DATA' * 1000)  # Create a small fake video file
            
            # Create sample audio file (dummy)
            test_audio = os.path.join(test_folder, "test_audio.wav")
            with open(test_audio, 'wb') as f:
                f.write(b'RIFF\x00\x00\x00\x00WAVEfmt \x00\x00\x00\x00' * 100)  # Fake WAV
            
            self.test_files_created.extend([test_video, test_audio])
            self.log_test("Setup test environment", "PASS", f"Created test folder: {test_folder}")
            
            return test_folder
            
        except Exception as e:
            self.log_test("Setup test environment", "FAIL", str(e))
            return None

    def login_user(self, user_type):
        """Login as specific user type"""
        try:
            user = self.test_users[user_type]
            response = self.session.post(f"{self.base_url}/login", data={
                'username': user['username'],
                'password': user['password']
            })
            
            if response.status_code == 200 and 'dashboard' in response.url:
                self.log_test(f"Login as {user_type}", "PASS", f"Role: {user['role']}")
                return True
            else:
                self.log_test(f"Login as {user_type}", "FAIL", f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test(f"Login as {user_type}", "FAIL", str(e))
            return False

    def test_interface_accessibility(self, user_type, expected_pages):
        """Test that user can access expected pages"""
        try:
            if not self.login_user(user_type):
                return False
            
            accessible_pages = 0
            for page in expected_pages:
                try:
                    response = self.session.get(f"{self.base_url}/{page}")
                    if response.status_code == 200:
                        accessible_pages += 1
                    else:
                        self.log_test(f"Access {page} as {user_type}", "FAIL", f"Status: {response.status_code}")
                except Exception as e:
                    self.log_test(f"Access {page} as {user_type}", "FAIL", str(e))
            
            success_rate = (accessible_pages / len(expected_pages)) * 100
            status = "PASS" if success_rate == 100 else "PARTIAL" if success_rate > 50 else "FAIL"
            self.log_test(f"Interface accessibility for {user_type}", status, 
                         f"{accessible_pages}/{len(expected_pages)} pages accessible ({success_rate:.1f}%)")
            
            return success_rate == 100
            
        except Exception as e:
            self.log_test(f"Interface accessibility for {user_type}", "FAIL", str(e))
            return False

    def test_api_endpoints(self):
        """Test all API endpoints"""
        print("\n🔌 Testing API endpoints...")
        
        # Login as admin for API testing
        if not self.login_user('admin'):
            return False
        
        api_tests = [
            ('GET', '/api/folder-tree', {}),
            ('GET', '/api/executive-public/queue', {}),
            ('GET', '/api/executive-public/metadata-options', {}),
            ('GET', '/api/cross-checker/audio-files', {}),
            ('POST', '/api/open-folder', {'folder_path': self.test_paths['source']}),
        ]
        
        passed_tests = 0
        for method, endpoint, data in api_tests:
            try:
                start_time = time.time()
                if method == 'GET':
                    response = self.session.get(f"{self.base_url}{endpoint}")
                else:
                    response = self.session.post(f"{self.base_url}{endpoint}", json=data)
                
                duration = time.time() - start_time
                
                if response.status_code in [200, 201]:
                    try:
                        json_response = response.json()
                        if json_response.get('success', True):
                            self.log_test(f"API {method} {endpoint}", "PASS", "", duration)
                            passed_tests += 1
                        else:
                            self.log_test(f"API {method} {endpoint}", "FAIL", 
                                        json_response.get('error', 'Unknown error'), duration)
                    except:
                        self.log_test(f"API {method} {endpoint}", "PASS", "Non-JSON response", duration)
                        passed_tests += 1
                else:
                    self.log_test(f"API {method} {endpoint}", "FAIL", 
                                f"Status: {response.status_code}", duration)
                    
            except Exception as e:
                self.log_test(f"API {method} {endpoint}", "FAIL", str(e))
        
        success_rate = (passed_tests / len(api_tests)) * 100
        self.log_test("Overall API Testing", 
                     "PASS" if success_rate == 100 else "PARTIAL" if success_rate > 70 else "FAIL",
                     f"{passed_tests}/{len(api_tests)} endpoints working ({success_rate:.1f}%)")
        
        return success_rate >= 70

    def test_file_operations(self, test_folder):
        """Test file operations with real files"""
        print("\n📁 Testing file operations...")
        
        if not test_folder or not os.path.exists(test_folder):
            self.log_test("File operations test", "FAIL", "Test folder not available")
            return False
        
        try:
            # Test folder scanning
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/folder-tree")
            duration = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('folders'):
                    self.log_test("Folder scanning", "PASS", 
                                f"Found {len(data['folders'])} folders", duration)
                else:
                    self.log_test("Folder scanning", "FAIL", "No folders found", duration)
            else:
                self.log_test("Folder scanning", "FAIL", f"Status: {response.status_code}", duration)
            
            # Test audio file detection
            audio_folder = self.test_paths['cross_check']
            test_audio_file = os.path.join(audio_folder, "test_audio_detection.wav")
            
            # Create test audio file
            with open(test_audio_file, 'wb') as f:
                f.write(b'RIFF\x00\x00\x00\x00WAVEfmt \x00\x00\x00\x00' * 50)
            self.test_files_created.append(test_audio_file)
            
            # Test audio detection API
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/cross-checker/audio-files")
            duration = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    audio_count = len(data.get('audio_files', []))
                    self.log_test("Audio file detection", "PASS", 
                                f"Found {audio_count} audio files", duration)
                else:
                    self.log_test("Audio file detection", "FAIL", data.get('error', 'Unknown'), duration)
            else:
                self.log_test("Audio file detection", "FAIL", f"Status: {response.status_code}", duration)
            
            return True
            
        except Exception as e:
            self.log_test("File operations test", "FAIL", str(e))
            return False

    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("🚀 Starting Comprehensive Archives System Testing")
        print("=" * 60)
        
        start_time = time.time()
        
        # Setup test environment
        test_folder = self.setup_test_environment()
        
        # Test user interface accessibility
        interface_tests = {
            'admin': ['dashboard', 'admin'],
            'assigner': ['dashboard', 'assigner'],
            'executor_public': ['dashboard', 'executor_public'],
            'executor_private': ['dashboard', 'executor_private'],
            'cross_checker': ['dashboard', 'cross_checker']
        }
        
        print("\n👥 Testing user interface accessibility...")
        for user_type, pages in interface_tests.items():
            self.test_interface_accessibility(user_type, pages)
        
        # Test API endpoints
        self.test_api_endpoints()
        
        # Test file operations
        self.test_file_operations(test_folder)
        
        # Calculate overall results
        total_duration = time.time() - start_time
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        partial_tests = len([r for r in self.test_results if r['status'] == 'PARTIAL'])
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST RESULTS")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️ Partial: {partial_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Total Duration: {total_duration:.2f} seconds")
        
        # Save detailed results
        self.save_test_results()
        
        return success_rate >= 80

    def save_test_results(self):
        """Save test results to file"""
        try:
            results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(results_file, 'w') as f:
                json.dump({
                    'summary': {
                        'total_tests': len(self.test_results),
                        'passed': len([r for r in self.test_results if r['status'] == 'PASS']),
                        'failed': len([r for r in self.test_results if r['status'] == 'FAIL']),
                        'partial': len([r for r in self.test_results if r['status'] == 'PARTIAL']),
                        'success_rate': (len([r for r in self.test_results if r['status'] == 'PASS']) / len(self.test_results)) * 100
                    },
                    'detailed_results': self.test_results
                }, f, indent=2)
            
            print(f"\n📄 Detailed test results saved to: {results_file}")
            
        except Exception as e:
            print(f"❌ Failed to save test results: {e}")

    def cleanup_test_environment(self):
        """Clean up test files and folders"""
        print("\n🧹 Cleaning up test environment...")
        
        for file_path in self.test_files_created:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                print(f"✅ Cleaned up: {file_path}")
            except Exception as e:
                print(f"❌ Failed to clean up {file_path}: {e}")

if __name__ == "__main__":
    tester = ArchivesSystemTester()
    
    try:
        success = tester.run_comprehensive_tests()
        
        if success:
            print("\n🎉 COMPREHENSIVE TESTING COMPLETED SUCCESSFULLY!")
            print("✅ System is ready for production use")
        else:
            print("\n⚠️ TESTING COMPLETED WITH ISSUES")
            print("❌ Please review failed tests before production deployment")
            
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n💥 Testing failed with error: {e}")
    finally:
        tester.cleanup_test_environment()
