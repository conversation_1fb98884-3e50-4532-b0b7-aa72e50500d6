#!/usr/bin/env python3
"""
REPRODUCE DATABASE LOCK ERROR NOW
Test the actual database lock error with the running Flask server
"""

import requests
import json
import time
import threading

def create_test_data():
    """Create test data in the database"""
    print("🔧 Creating test data...")
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    # Login as admin to create test data
    login_data = {'username': 'admin', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print("   ❌ Admin login failed!")
        return False
    
    # Create test queue item
    test_data = {
        'folder_path': r'T:\To_Process\Rough folder\Internal video without stems\Test_DB_Lock_Folder',
        'category': 'Internal video without stems',
        'assign_to': 'Executive Public',
        'video_ids': 'DB-LOCK-TEST-001',
        'url': 'https://test.com/db-lock',
        'remarks': 'Database lock error test folder'
    }
    
    # Add to queue via API
    add_response = session.post(f"{base_url}/api/add-to-queue", json=test_data)
    
    if add_response.status_code == 200:
        result = add_response.json()
        if result.get('success'):
            print(f"   ✅ Test data created: Queue ID {result.get('queue_id')}")
            return True
    
    print("   ❌ Failed to create test data")
    return False

def test_concurrent_database_access():
    """Test concurrent database access to reproduce lock error"""
    print("🚨 TESTING CONCURRENT DATABASE ACCESS")
    print("=" * 80)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # STEP 1: Login
        print("1. 🔐 Login to Executor Public...")
        login_data = {
            'username': 'executor_public',
            'password': 'Shiva@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return False
        
        print("   ✅ Login successful!")
        
        # STEP 2: Get queue items
        print("\n2. 📋 Get queue items...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        if queue_response.status_code != 200:
            print("   ❌ Queue load failed!")
            return False
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue error: {queue_data.get('error')}")
            return False
        
        files = queue_data.get('files', [])
        print(f"   ✅ Queue loaded: {len(files)} files")
        
        if not files:
            # Create test data if no files exist
            if create_test_data():
                # Retry getting queue
                queue_response = session.get(f"{base_url}/api/executive-public/queue")
                queue_data = queue_response.json()
                files = queue_data.get('files', [])
        
        if not files:
            print("   ⚠️ No files to test!")
            return False
        
        # STEP 3: Test concurrent processing (this should trigger database lock)
        print("\n3. 🔄 Testing concurrent processing...")
        test_file = files[0]
        queue_item_id = test_file.get('queue_item_id')
        folder_name = test_file.get('folder_name', 'Unknown')
        
        print(f"   📂 Testing: {folder_name}")
        print(f"   🆔 Queue ID: {queue_item_id}")
        
        # Create comprehensive metadata for processing
        test_metadata = {
            'ocd_vp_number': 'DB-LOCK-CONCURRENT-2025-001',
            'edited_file_name': f'DB_Lock_Concurrent_Test_{folder_name[:20]}',
            'language': 'English',
            'edited_year': 2025,
            'trim_closed_date': '2025-01-15',
            'total_duration': '10:15',
            'video_type': 'Talk',
            'edited_location': 'Ashram',
            'published_platforms': 'YouTube',
            'department_name': 'Archives DB Lock Concurrent Test',
            'component': 'Sadhguru',
            'content_tags': 'Spirituality',
            'backup_type': 'Full Backup',
            'access_level': 'Public',
            'software_show_name': f'DB_LOCK_CONCURRENT_RENAMED_{folder_name[:15]}',
            'audio_code': 'DB-LOCK-CONC-AUD-2025-001',
            'audio_file_name': f'DB_Lock_Concurrent_Audio_{folder_name[:15]}',
            'transcription_file_name': 'DB_Lock_Concurrent_Transcription',
            'transcription_status': 'Pending',
            'published_date': '2025-01-15',
            'video_id': 'DB-LOCK-CONC-VID-2025-001',
            'social_media_title': 'DB Lock Concurrent Test Video',
            'description': 'Database lock concurrent test video description',
            'social_media_url': 'https://youtube.com/db-lock-concurrent-test',
            'duration_category': '10 minutes 15 seconds',
            'processing_notes': 'Database lock concurrent error reproduction test'
        }
        
        process_payload = {
            'queue_item_id': queue_item_id,
            'metadata': test_metadata
        }
        
        # STEP 4: Send multiple concurrent requests to trigger database lock
        print("\n4. 🚀 Sending concurrent processing requests...")
        print("   📤 This should trigger the database lock error...")
        
        def send_process_request(session_copy, payload, request_id):
            """Send a processing request"""
            try:
                response = session_copy.post(
                    f"{base_url}/api/executive-public/process-metadata",
                    json=payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=60
                )
                
                print(f"   📥 Request {request_id} Status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"   ✅ Request {request_id}: SUCCESS")
                        return True
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        print(f"   🚨 Request {request_id} ERROR: {error_msg}")
                        
                        if 'database is locked' in error_msg.lower():
                            print(f"   🎯 Request {request_id}: DATABASE LOCK ERROR REPRODUCED!")
                            return False
                        else:
                            print(f"   ⚠️ Request {request_id}: Different error")
                            return False
                else:
                    print(f"   ❌ Request {request_id}: HTTP Error {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"   ❌ Request {request_id} Exception: {e}")
                return False
        
        # Create multiple sessions for concurrent requests
        sessions = []
        for i in range(3):
            new_session = requests.Session()
            # Login each session
            new_session.post(f"{base_url}/login", data=login_data)
            sessions.append(new_session)
        
        # Send concurrent requests
        threads = []
        results = []
        
        for i, sess in enumerate(sessions):
            thread = threading.Thread(
                target=lambda s=sess, p=process_payload, rid=i+1: results.append(
                    send_process_request(s, p, rid)
                )
            )
            threads.append(thread)
        
        # Start all threads simultaneously
        print("   🔄 Starting concurrent requests...")
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Analyze results
        print(f"\n   📊 Results: {len(results)} requests completed")
        successful_requests = sum(1 for r in results if r)
        failed_requests = len(results) - successful_requests
        
        print(f"   ✅ Successful: {successful_requests}")
        print(f"   ❌ Failed: {failed_requests}")
        
        if failed_requests > 0:
            print("   🎯 DATABASE LOCK ERROR LIKELY REPRODUCED!")
            return False
        else:
            print("   ⚠️ No database lock error occurred")
            return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚨 REPRODUCING DATABASE LOCK ERROR WITH CONCURRENT REQUESTS")
    print("=" * 80)
    
    success = test_concurrent_database_access()
    
    print("\n" + "=" * 80)
    print("🎯 DATABASE LOCK ERROR REPRODUCTION RESULTS")
    print("=" * 80)
    
    if not success:
        print("🎯 DATABASE LOCK ERROR REPRODUCED!")
        print("\n🔍 ANALYSIS:")
        print("   The database lock error occurs when multiple requests")
        print("   try to access the SQLite database simultaneously:")
        print("   1. 📊 Google Sheets update")
        print("   2. 💾 Metadata save to database")
        print("   3. 📁 Queue item status update")
        print("   4. 🔄 Folder move operation")
        
        print("\n🚨 ROOT CAUSE:")
        print("   SQLite database connections are not properly managed")
        print("   Multiple operations try to access the database simultaneously")
        print("   Connections are not closed properly or transactions overlap")
        
        print("\n🔧 SOLUTION NEEDED:")
        print("   1. Use proper connection management with 'with' blocks")
        print("   2. Implement database connection pooling")
        print("   3. Add retry logic for temporary locks")
        print("   4. Ensure all connections are properly closed")
        print("   5. Use transactions to batch related operations")
        
    else:
        print("⚠️ DATABASE LOCK ERROR NOT REPRODUCED")
        print("   Either the fix is already working, or")
        print("   the concurrent load wasn't sufficient to trigger the error")
    
    return not success

if __name__ == "__main__":
    main()
