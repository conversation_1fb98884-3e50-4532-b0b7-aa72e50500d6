# 📚 **ARCHIVES DEPARTMENT FILE MANAGEMENT SYSTEM - USER TRAINING GUIDE**

## 🎯 **TRAINING OVERVIEW**

This comprehensive training guide covers all the enhanced features of the Archives Department File Management System. Each user role has specific capabilities and workflows designed to streamline archive processing operations.

## 👥 **USER ROLES & ACCESS**

### **🔐 LOGIN CREDENTIALS**
- **Admin**: `admin` / `<PERSON>@123`
- **Assigner**: `assigner` / `<PERSON>@123`
- **Executor Public**: `executor_public` / `<PERSON>@123`
- **Executor Private**: `executor_private` / `<PERSON>@123`
- **Cross Checker**: `crosschecker` / `<PERSON>@123`

## 🎬 **EXECUTOR PUBLIC/PRIVATE TRAINING**

### **📁 Basic File Processing**
1. **Login** to your executor interface
2. **View Files in Queue** - See all assigned folders
3. **Select a Folder** - Click on folder to start processing
4. **Fill Metadata** - Complete all required fields
5. **Preview Video** - Use VLC integration to preview files
6. **Process Files** - Move to appropriate destination folder

### **🎵 NEW: Enhanced Audio Extraction**

#### **Single Audio Extraction**
1. Navigate to **Processing Tab**
2. Scroll to **Audio File Selection & Extraction** section
3. **Browse Current Folder** - Select audio files from current folder
4. **Select Video Files** - Choose video files from any location
5. **Preview Audio** - Click preview to play in VLC
6. **Extract Audio** - Click "Extract Audio" button
7. **Monitor Progress** - Watch real-time progress with time estimation

#### **🔥 NEW: Batch Audio Extraction**
1. Click **"Batch Audio Extraction"** tab
2. **Select Multiple Videos**:
   - Click "Select Multiple Video Files"
   - Choose multiple video files from any location
   - Click "Add Videos" to add to batch queue
3. **Review Selected Files**:
   - View all selected videos with file sizes
   - Remove individual files if needed
   - Click "Clear All" to start over
4. **Start Batch Processing**:
   - Click "Start Batch Extraction"
   - Monitor overall progress and individual file progress
   - View detailed status log with timestamps
5. **Review Results**:
   - See success/failure counts
   - Download batch report
   - Open cross-check folder to verify extracted audio

### **⚡ Pro Tips for Executors**
- Use **Ctrl+Click** to select multiple files quickly
- **Right-click** on files for context menu options
- **Monitor progress bars** for processing status
- **Check status logs** for detailed operation information
- **Use VLC preview** to verify file quality before processing

## ✅ **CROSS CHECKER TRAINING**

### **📋 Standard Validation Workflow**
1. **Login** to Cross Checker interface
2. **View Pending Folders** - See folders awaiting validation
3. **Select Folder** - Click to view details
4. **Review Metadata** - Verify all information is correct
5. **Validate Files** - Check file quality and completeness

### **🎵 NEW: Audio Check Tab**
1. Click **"Audio Check"** tab
2. **Browse Audio Files**:
   - View all WAV files in cross-check folder
   - See file sizes and modification dates
3. **Audio Actions**:
   - **Play**: Click play button to open in VLC
   - **Details**: View comprehensive file metadata
   - **Validate**: Process audio file
4. **Audio Validation**:
   - Choose **"Send to TR"** for transcription
   - Choose **"Archive"** to save in repository
   - Add optional notes for the decision
5. **Folder Validation**:
   - Choose **"Reingest"** to reprocess folder
   - Choose **"Delete"** to mark for deletion
   - Add notes explaining the decision

### **🔍 Enhanced Validation Options**
- **Reingest**: Moves folder to "To Reingest" for reprocessing
- **Delete**: Moves folder to "To Be Deleted" queue
- **Send to TR**: Moves audio to transcription queue
- **Archive**: Saves audio in permanent repository

## 👑 **ADMIN PANEL TRAINING**

### **📊 Dashboard Overview**
1. **Login** as Main Admin
2. **View Statistics Cards**:
   - Total Users, Completed Files, Pending Files, Total Files
3. **System Health Overview**:
   - Database status, File system health, Storage usage
4. **Quick Actions**:
   - Generate Report, Backup System, Clear Cache, Refresh Stats

### **📈 Reports & Analytics**
1. Click **"Reports"** tab
2. **Set Filters**:
   - Date range (From/To dates)
   - Specific user or all users
   - Role filter (Admin, Executor, etc.)
3. **Generate Reports**:
   - **Activity Report**: User activity and file processing
   - **Performance Report**: Processing times and efficiency
   - **System Report**: Overall system statistics
4. **View Charts**:
   - User Activity Distribution (Doughnut chart)
   - Processing Time Trends (Line chart)
   - Category Distribution (Bar chart)
   - Storage Usage (Pie chart)
5. **Export Reports**:
   - **CSV**: For spreadsheet analysis
   - **PDF**: For formal reporting
   - **Excel**: For advanced data analysis

### **🗂️ Path Configuration**
1. Click **"Path Config"** tab
2. **View Path Categories**:
   - **System Paths**: Core system directories
   - **Destination Paths**: Processing output folders
   - **Audio Paths**: Audio processing directories
   - **Custom Paths**: User-defined directories
3. **Manage Paths**:
   - **Edit**: Click edit button to modify path
   - **Validate**: Check if directory exists and is accessible
   - **Add New**: Create custom path categories
4. **Path Validation**:
   - Green checkmark: Path exists and accessible
   - Red X: Path not found or inaccessible
   - Yellow warning: Path exists but may have issues

### **🏷️ Metadata Management**
1. Click **"Metadata"** tab
2. **Category Management**:
   - View existing categories
   - Add new categories (e.g., "Documentary", "Training Video")
   - Edit or delete categories
3. **Metadata Fields**:
   - **Add New Field**: Define field name, type, validation
   - **Field Types**: Text, dropdown, checkbox, date, number, email, URL
   - **Validation Rules**: Required fields, maximum length
4. **Template Editor**:
   - **Available Fields**: Drag fields from left panel
   - **Template Layout**: Drop fields to build form layout
   - **Preview**: See how the form will look
   - **Save**: Apply template to system

### **👥 User Management**
1. Click **"Users"** tab
2. **View Users**:
   - Filter by role, status, or search by name
   - View activity scores and last login
3. **User Actions**:
   - **Create**: Add new user with role assignment
   - **Edit**: Modify user details and permissions
   - **Reset Password**: Generate new password for user
   - **Activate/Deactivate**: Enable or disable user access
   - **Delete**: Remove user from system (cannot undo)
4. **Activity Monitoring**:
   - View user activity scores
   - Check last login times
   - Monitor user performance

### **⚙️ System Management**
1. Click **"System"** tab
2. **System Health**:
   - Database connectivity status
   - File system accessibility
   - Storage usage warnings
   - Google Sheets integration status
3. **Backup & Restore**:
   - **Create Backup**: Full system backup with options
   - **Schedule Backup**: Set automatic backup intervals
   - **Restore**: Restore from previous backup
4. **Audit Log**:
   - View all admin actions with timestamps
   - Export audit log for compliance
   - Filter by date range or action type

## 🚨 **TROUBLESHOOTING GUIDE**

### **Common Issues & Solutions**

#### **File Upload Issues**
- **Problem**: Large files won't upload
- **Solution**: Check file size limits, ensure stable internet connection
- **Prevention**: Use batch processing for multiple large files

#### **VLC Preview Not Working**
- **Problem**: VLC doesn't open when clicking preview
- **Solution**: Ensure VLC is installed and in system PATH
- **Alternative**: Download file and open manually

#### **Path Not Found Errors**
- **Problem**: System can't find specified directories
- **Solution**: Admin should validate and update paths in Path Config
- **Check**: Ensure T: drive is accessible and mounted

#### **Permission Denied**
- **Problem**: User can't access certain features
- **Solution**: Check user role and permissions with admin
- **Verify**: Ensure user is logged in with correct credentials

#### **Slow Performance**
- **Problem**: System is running slowly
- **Solution**: Admin can clear cache and refresh statistics
- **Check**: Monitor system health for storage and memory issues

## 📞 **SUPPORT & ASSISTANCE**

### **Getting Help**
1. **Check System Health**: Admin can monitor system status
2. **Review Audit Logs**: Check recent actions for issues
3. **Contact Admin**: Report issues to system administrator
4. **Documentation**: Refer to this guide and system documentation

### **Best Practices**
- **Regular Backups**: Admin should backup system regularly
- **Monitor Storage**: Keep an eye on disk space usage
- **Update Paths**: Validate paths when directories change
- **User Training**: Ensure all users understand their roles
- **Security**: Change default passwords on first login

## 🎓 **TRAINING COMPLETION CHECKLIST**

### **For Executors**
- [ ] Can login and access dashboard
- [ ] Can process individual files with metadata
- [ ] Can use VLC preview functionality
- [ ] Can extract audio from single files
- [ ] Can perform batch audio extraction
- [ ] Can monitor progress and view status logs
- [ ] Can download batch reports

### **For Cross Checkers**
- [ ] Can validate folders and metadata
- [ ] Can use Audio Check tab
- [ ] Can play audio files in VLC
- [ ] Can process audio files (TR/Archive)
- [ ] Can reingest or delete folders
- [ ] Can add validation notes

### **For Admins**
- [ ] Can access all dashboard statistics
- [ ] Can generate and export reports
- [ ] Can configure system paths
- [ ] Can manage metadata fields and categories
- [ ] Can create and manage users
- [ ] Can monitor system health
- [ ] Can perform backups and maintenance

## 🎉 **CONGRATULATIONS!**

You have completed the comprehensive training for the Archives Department File Management System. The system is now ready for full production use with all enhanced features operational.

**Remember**: This system is designed to streamline your workflow and improve efficiency. Don't hesitate to explore the features and contact your administrator if you need assistance.

**Happy Processing!** 🚀
