{% extends "base.html" %}

{% block title %}Dashboard - File Management System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 mb-0">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </h1>
        <p class="text-muted">Welcome back, {{ session.role }}!</p>
    </div>
</div>

{% if session.role == 'Main Admin' %}
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_files or 0 }}</div>
            <div>Total Files</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
            <div class="stats-number">{{ stats.completed_files or 0 }}</div>
            <div>Completed</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
            <div class="stats-number">{{ stats.pending_files or 0 }}</div>
            <div>Pending</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
            <div class="stats-number">{{ stats.total_users or 0 }}</div>
            <div>Active Users</div>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h5>
            </div>
            <div class="card-body">
                {% if recent_operations %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Operation</th>
                                <th>File</th>
                                <th>User</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for operation in recent_operations %}
                            <tr>
                                <td>
                                    <i class="fas fa-{{ 'upload' if operation.operation_type == 'assigned' else 'edit' if operation.operation_type == 'renamed' else 'check' }} me-2"></i>
                                    {{ operation.operation_type.replace('_', ' ').title() }}
                                </td>
                                <td>
                                    <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ operation.file_path or 'N/A' }}">
                                        {{ operation.file_path.split('/')[-1] if operation.file_path else 'N/A' }}
                                    </span>
                                </td>
                                <td>{{ operation.username or 'System' }}</td>
                                <td>
                                    <span class="status-badge status-{{ (operation.status or 'pending').lower().replace(' ', '-') }}">
                                        {{ (operation.status or 'pending').title() }}
                                    </span>
                                </td>
                                <td class="format-date">{{ operation.created_at or 'N/A' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No recent activity to display.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if session.role == 'Assigner' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('assigner_interface') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-upload me-2"></i>Assign New Files
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if session.role == 'Cross Checker' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('cross_checker_interface') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-check-double me-2"></i>Review Files
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if session.role == 'Executive Public' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('executive_public') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-user-tie me-2"></i>Executive Public Dashboard
                        </a>
                    </div>
                    {% endif %}

                    {% if session.role == 'Executive Private' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('executive_private') }}" class="btn btn-dark btn-lg w-100">
                            <i class="fas fa-user-lock me-2"></i>Executive Private Dashboard
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if session.role == 'Main Admin' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('admin_interface') }}" class="btn btn-danger btn-lg w-100">
                            <i class="fas fa-users-cog me-2"></i>Manage Users
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Role-specific information cards -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Role Information</h5>
            </div>
            <div class="card-body">
                {% if session.role == 'Assigner' %}
                <div class="alert alert-info">
                    <h6><i class="fas fa-tasks me-2"></i>Your Role: Assigner</h6>
                    <p class="mb-0">You can browse source folders, select video files, and assign them to processing categories. For social media categories, you'll need to provide URLs.</p>
                </div>
                {% elif session.role == 'Cross Checker' %}
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-double me-2"></i>Your Role: Cross Checker</h6>
                    <p class="mb-0">Review files assigned by the Assigner. You can approve or reject assignments before they go to processing.</p>
                </div>
                {% elif session.role == 'Executive Public' %}
                <div class="alert alert-warning">
                    <h6><i class="fas fa-user-tie me-2"></i>Your Role: Executive Public</h6>
                    <p class="mb-0">Process files in queue with metadata entry, file preview with VLC integration, and comprehensive metadata forms. Move processed files to cross-checker queue.</p>
                </div>
                {% elif session.role == 'Executive Private' %}
                <div class="alert alert-dark">
                    <h6><i class="fas fa-user-lock me-2"></i>Your Role: Executive Private</h6>
                    <p class="mb-0">Process all files including private ones with advanced metadata management and batch processing capabilities.</p>
                </div>
                {% elif session.role == 'Main Admin' %}
                <div class="alert alert-danger">
                    <h6><i class="fas fa-users-cog me-2"></i>Your Role: Main Admin</h6>
                    <p class="mb-0">Manage users, view system statistics, and oversee all operations. You have full system access.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function formatDate(dateString) {
        if (!dateString || dateString === 'N/A') return 'N/A';
        try {
            return new Date(dateString).toLocaleString();
        } catch (e) {
            return dateString;
        }
    }

    // Format dates in the table
    $(document).ready(function() {
        $('.format-date').each(function() {
            const text = $(this).text();
            $(this).text(formatDate(text));
        });
    });
</script>
{% endblock %}
