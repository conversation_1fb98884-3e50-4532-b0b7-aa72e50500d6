#!/usr/bin/env python3
"""
TEST FOLDER STRUCTURE CONSISTENCY
Comprehensive test to verify folder structure consistency across all interfaces
"""

import requests
import json
from datetime import datetime

def test_folder_structure_consistency():
    print("🎯 TESTING FOLDER STRUCTURE CONSISTENCY ACROSS ALL INTERFACES")
    print("="*80)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Test data for different user roles
    test_users = [
        {'username': 'assigner', 'password': 'Shiva@123', 'role': 'Assigner', 'interface': 'Archives Assignment Console'},
        {'username': 'executor_public', 'password': 'Shiva@123', 'role': 'Executor Public', 'interface': 'Executor Public'},
        {'username': 'executor_private', 'password': '<PERSON>@123', 'role': 'Executor Private', 'interface': 'Executive Private'},
        {'username': 'cross_checker', 'password': '<PERSON>@123', 'role': 'Cross Checker', 'interface': 'Cross Checker'}
    ]
    
    folder_apis = {
        'Assigner': '/api/get-folders',
        'Executor Public': '/api/executive-public/folder-tree',
        'Executor Private': '/api/executive-private/folder-tree',
        'Cross Checker': '/api/cross-checker/folder-tree'
    }
    
    interface_urls = {
        'Assigner': '/assigner',
        'Executor Public': '/executor-public',
        'Executor Private': '/executive-private',
        'Cross Checker': '/cross-checker'
    }
    
    results = {}
    
    for user in test_users:
        print(f"\n🔍 TESTING {user['interface']} ({user['role']})")
        print("-" * 60)
        
        # Step 1: Login
        login_data = {'username': user['username'], 'password': user['password']}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code not in [200, 302]:
            print(f"❌ Login failed for {user['role']}: {login_response.status_code}")
            results[user['role']] = {'login': False, 'interface': False, 'api': False}
            continue
        
        print(f"✅ Login successful for {user['role']}")
        
        # Step 2: Test interface access
        interface_url = interface_urls.get(user['role'])
        if interface_url:
            interface_response = session.get(f"{base_url}{interface_url}")
            interface_success = interface_response.status_code == 200
            print(f"{'✅' if interface_success else '❌'} Interface access: {interface_response.status_code}")
        else:
            interface_success = False
            print(f"❌ No interface URL defined for {user['role']}")
        
        # Step 3: Test folder API
        api_endpoint = folder_apis.get(user['role'])
        if api_endpoint:
            try:
                api_response = session.get(f"{base_url}{api_endpoint}")
                api_data = api_response.json() if api_response.status_code == 200 else {}
                
                api_success = api_response.status_code == 200 and api_data.get('success', False)
                print(f"{'✅' if api_success else '❌'} Folder API: {api_response.status_code}")
                
                if api_success:
                    # Analyze folder structure
                    if user['role'] == 'Assigner':
                        folders = api_data.get('folders', [])
                        folder_count = len(folders)
                        print(f"   📁 Archives Console: {folder_count} folders found")
                        
                        # Check for tree structure
                        has_tree_structure = any(folder.get('children') for folder in folders)
                        print(f"   🌳 Tree structure: {'✅ Present' if has_tree_structure else '❌ Missing'}")
                        
                    else:
                        folders = api_data.get('folders', [])
                        folder_count = len(folders)
                        print(f"   📁 {user['interface']}: {folder_count} folders found")
                        
                        # Check for tree structure
                        has_tree_structure = any(folder.get('children') for folder in folders)
                        print(f"   🌳 Tree structure: {'✅ Present' if has_tree_structure else '❌ Missing'}")
                        
                        # Check for media count
                        has_media_count = any(folder.get('media_count') is not None for folder in folders)
                        print(f"   🎬 Media count: {'✅ Present' if has_media_count else '❌ Missing'}")
                        
                        # Check for VLC integration data
                        has_vlc_data = any(folder.get('file_count', 0) > 0 for folder in folders)
                        print(f"   🎥 VLC integration data: {'✅ Present' if has_vlc_data else '❌ Missing'}")
                
            except Exception as e:
                api_success = False
                print(f"❌ Folder API error: {e}")
        else:
            api_success = False
            print(f"❌ No API endpoint defined for {user['role']}")
        
        results[user['role']] = {
            'login': True,
            'interface': interface_success,
            'api': api_success
        }
        
        # Logout
        session.get(f"{base_url}/logout")
    
    # Summary
    print(f"\n🎯 FOLDER STRUCTURE CONSISTENCY TEST RESULTS")
    print("="*80)
    
    all_success = True
    for role, result in results.items():
        status = "✅ PASS" if all(result.values()) else "❌ FAIL"
        print(f"{status} {role}:")
        print(f"   Login: {'✅' if result['login'] else '❌'}")
        print(f"   Interface: {'✅' if result['interface'] else '❌'}")
        print(f"   Folder API: {'✅' if result['api'] else '❌'}")
        
        if not all(result.values()):
            all_success = False
    
    print(f"\n🎯 OVERALL RESULT:")
    if all_success:
        print("✅ ALL INTERFACES HAVE CONSISTENT FOLDER STRUCTURE")
        print("✅ Archives Assignment Console folder tree logic successfully applied")
        print("✅ Executor Public, Executive Private, and Cross Checker all match")
    else:
        print("❌ FOLDER STRUCTURE INCONSISTENCY DETECTED")
        print("❌ Some interfaces are missing proper folder tree implementation")
    
    print(f"\n🔍 MANUAL VERIFICATION STEPS:")
    print("="*80)
    print("1. Open each interface in browser:")
    print("   - Archives Assignment Console: http://127.0.0.1:5001/assigner")
    print("   - Executor Public: http://127.0.0.1:5001/executor-public")
    print("   - Executive Private: http://127.0.0.1:5001/executive-private")
    print("   - Cross Checker: http://127.0.0.1:5001/cross-checker")
    
    print("\n2. Check folder tree views:")
    print("   - Archives Console: Main folder tree")
    print("   - Executor Public: Queue tab → Folder Tree View tab")
    print("   - Executive Private: Folder Navigation tab")
    print("   - Cross Checker: Folder tree section")
    
    print("\n3. Verify consistency:")
    print("   ✅ Same tree structure (expand/collapse)")
    print("   ✅ Same folder icons and styling")
    print("   ✅ Same VLC integration buttons")
    print("   ✅ Same file size and count display")
    print("   ✅ Same hover effects and interactions")
    
    print("\n🎉 FOLDER STRUCTURE CONSISTENCY TEST COMPLETE")
    print("="*80)

if __name__ == "__main__":
    test_folder_structure_consistency()
