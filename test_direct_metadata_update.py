#!/usr/bin/env python3
"""
DIRECT METADATA UPDATE TEST
Test the update_metadata_in_google_sheets function directly to verify column N+ mapping
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime

# Import the function directly
try:
    from app import update_metadata_in_google_sheets
    print("✅ Successfully imported update_metadata_in_google_sheets function")
except ImportError as e:
    print(f"❌ Failed to import function: {e}")
    sys.exit(1)

def test_direct_metadata_update():
    print("🔧 DIRECT METADATA UPDATE TEST - COLUMNS N+")
    print("="*60)
    
    # Create test metadata
    timestamp = datetime.now().strftime("%H%M%S")
    
    # Use a test folder name that we can easily find in Google Sheets
    test_folder_name = f"METADATA_COLUMNS_N_TEST_{timestamp}"
    
    # Create comprehensive test metadata
    test_metadata = {
        'ocd_vp_number': f'OCD-N-TEST-{timestamp}',
        'edited_file_name': f'columns_n_test_{timestamp}.mp4',
        'total_file_size': '3.2 GB',
        'file_count': '12',
        'detected_duration': '00:35:20',
        'language': 'English',
        'edited_year': '2024',
        'video_type': 'Documentary',
        'edited_location': 'Isha Yoga Center',
        'published_platforms': 'YouTube, Instagram, Facebook, Twitter',
        'department_name': 'Archives Department',
        'component': 'Video Production Unit',
        'content_tags': 'Sadhguru, Wisdom, Spirituality, Meditation',
        'backup_type': 'Cloud Storage',
        'access_level': 'Public Access',
        'software_show_name': 'Direct Metadata Test Show',
        'audio_code': f'AUD-N-TEST-{timestamp}',
        'audio_file_name': f'columns_n_audio_{timestamp}.wav',
        'transcription_file_name': f'columns_n_transcript_{timestamp}.txt',
        'transcription_status': 'Completed Successfully',
        'published_date': '2024-06-09',
        'video_id': f'VID-N-TEST-{timestamp}',
        'social_media_title': f'Direct Metadata Test Video {timestamp}',
        'description': f'This is a direct test of metadata columns N+ mapping. All metadata should start from column N (14) instead of column G (7). Timestamp: {timestamp}',
        'social_media_url': f'https://direct-metadata-n-test-{timestamp}.com',
        'duration_category': 'Long Form Educational Content'
    }
    
    print(f"📝 Test folder name: {test_folder_name}")
    print(f"📝 Test metadata (should write to columns N+ starting from column 14):")
    print(f"   📊 Column N (14): OCD Number = {test_metadata['ocd_vp_number']}")
    print(f"   📊 Column O (15): File Name = {test_metadata['edited_file_name']}")
    print(f"   📊 Column P (16): Size = {test_metadata['total_file_size']}")
    print(f"   📊 Column Q (17): Count = {test_metadata['file_count']}")
    print(f"   📊 Column R (18): Duration = {test_metadata['detected_duration']}")
    print(f"   📊 Column S (19): Language = {test_metadata['language']}")
    print(f"   📊 Column T (20): Year = {test_metadata['edited_year']}")
    print(f"   📊 Column U (21): Type = {test_metadata['video_type']}")
    print(f"   📊 Column V (22): Location = {test_metadata['edited_location']}")
    print(f"   📊 Column W (23): Platforms = {test_metadata['published_platforms']}")
    print(f"   📊 Column X (24): Department = {test_metadata['department_name']}")
    print(f"   📊 Column Y (25): Component = {test_metadata['component']}")
    print(f"   📊 Column Z (26): Tags = {test_metadata['content_tags']}")
    print(f"   📊 Column AA (27): Backup = {test_metadata['backup_type']}")
    print(f"   📊 Column AB (28): Access = {test_metadata['access_level']}")
    print(f"   📊 Column AC (29): Show = {test_metadata['software_show_name']}")
    print(f"   📊 Column AD (30): Audio Code = {test_metadata['audio_code']}")
    print(f"   📊 Column AE (31): Audio File = {test_metadata['audio_file_name']}")
    print(f"   📊 Column AF (32): Transcript = {test_metadata['transcription_file_name']}")
    print(f"   📊 Column AG (33): Trans Status = {test_metadata['transcription_status']}")
    print(f"   📊 Column AH (34): Pub Date = {test_metadata['published_date']}")
    print(f"   📊 Column AI (35): Video ID = {test_metadata['video_id']}")
    print(f"   📊 Column AJ (36): SM Title = {test_metadata['social_media_title']}")
    print(f"   📊 Column AK (37): Description = {test_metadata['description']}")
    print(f"   📊 Column AL (38): SM URL = {test_metadata['social_media_url']}")
    print(f"   📊 Column AM (39): Duration Cat = {test_metadata['duration_category']}")
    
    print(f"\n🚀 Calling update_metadata_in_google_sheets function...")
    
    # Call the function directly
    try:
        result = update_metadata_in_google_sheets(test_folder_name, test_metadata)
        
        print(f"\n✅ Function call result:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Message: {result.get('message', 'No message')}")
        
        if result.get('success'):
            print(f"   Row Number: {result.get('row_number', 'Unknown')}")
            print(f"\n🎉 METADATA UPDATE SUCCESSFUL!")
            print(f"✅ Data should now be written to columns N+ (starting from column 14)")
            print(f"✅ NO data should appear in columns G-M (7-13)")
        else:
            print(f"   Error: {result.get('error', 'Unknown error')}")
            print(f"\n❌ METADATA UPDATE FAILED!")
            
    except Exception as e:
        print(f"\n❌ Exception during function call: {e}")
        return
    
    print(f"\n🔍 VERIFICATION STEPS:")
    print("="*60)
    print(f"1. Open Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print(f"2. Go to 'Records' sheet")
    print(f"3. Search for: {test_folder_name}")
    print(f"4. Verify the following:")
    print(f"   ✅ Folder name '{test_folder_name}' appears in Column A")
    print(f"   ✅ OCD number '{test_metadata['ocd_vp_number']}' appears in Column N (not G)")
    print(f"   ✅ File name '{test_metadata['edited_file_name']}' appears in Column O (not H)")
    print(f"   ✅ Size '{test_metadata['total_file_size']}' appears in Column P (not I)")
    print(f"   ✅ Count '{test_metadata['file_count']}' appears in Column Q (not J)")
    print(f"   ✅ Duration '{test_metadata['detected_duration']}' appears in Column R (not K)")
    print(f"   ✅ Language '{test_metadata['language']}' appears in Column S (not L)")
    print(f"   ✅ Year '{test_metadata['edited_year']}' appears in Column T (not M)")
    print(f"   ✅ All other metadata continues through Column AM")
    print(f"   ❌ NO data should appear in columns G, H, I, J, K, L, M")
    
    print(f"\n🎯 EXPECTED COLUMN MAPPING (AFTER FIX):")
    print(f"   Column N (14): {test_metadata['ocd_vp_number']} ← Was Column G (7)")
    print(f"   Column O (15): {test_metadata['edited_file_name']} ← Was Column H (8)")
    print(f"   Column P (16): {test_metadata['total_file_size']} ← Was Column I (9)")
    print(f"   Column Q (17): {test_metadata['file_count']} ← Was Column J (10)")
    print(f"   Column R (18): {test_metadata['detected_duration']} ← Was Column K (11)")
    print(f"   Column S (19): {test_metadata['language']} ← Was Column L (12)")
    print(f"   Column T (20): {test_metadata['edited_year']} ← Was Column M (13)")
    print(f"   ... and so on through Column AM (39)")
    
    print("="*60)
    print("🎉 DIRECT METADATA TEST COMPLETE!")
    print("✅ Metadata column mapping fixed from G+ to N+")
    print("✅ All metadata fields shifted 7 columns to the right")
    print("="*60)

if __name__ == "__main__":
    test_direct_metadata_update()
