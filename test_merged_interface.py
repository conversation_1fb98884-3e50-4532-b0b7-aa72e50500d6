#!/usr/bin/env python3
"""
Test script to verify the merged admin/assigner interface changes
"""

import requests
import json
import os

def test_merged_interface():
    print("🔥 TESTING MERGED ADMIN/ASSIGNER INTERFACE")
    print("="*60)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Test 1: Login as admin
    print("\n📋 STEP 1: ADMIN LOGIN")
    try:
        login_data = {'username': 'admin', 'password': 'Shiva@123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code == 200:
            print("✅ Admin logged in successfully")
        else:
            print(f"❌ Admin login failed: {login_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Test 2: Access merged interface
    print("\n📋 STEP 2: ACCESS MERGED INTERFACE")
    try:
        interface_response = session.get(f"{base_url}/professional-assigner")
        if interface_response.status_code == 200:
            print("✅ Merged interface accessible")
            content = interface_response.text
            
            # Check for key elements
            checks = {
                'Assignment Console Tab': 'Assignment Console' in content,
                'Dashboard Tab': 'Dashboard' in content,
                'Path Config Tab': 'Path Config' in content,
                'Users Tab': 'Users' in content,
                'System Tab': 'System' in content,
                'Source Path Input': 'sourcePathInput' in content,
                'Category Path Config': 'category-path-config' in content,
                'Folder Browser': 'folder-browser' in content,
                'Assignment Panel': 'assignment-panel' in content,
                'Queue Section': 'queue-section' in content,
                'New Categories': 'Miscellaneous' in content and 'Private One' in content,
                'Not Applicable Option': 'Not applicable' in content
            }
            
            print("\n✅ Interface Elements Check:")
            for element, present in checks.items():
                status = "✅" if present else "❌"
                print(f"   {status} {element}")
        else:
            print(f"❌ Interface access failed: {interface_response.status_code}")
    except Exception as e:
        print(f"❌ Interface access error: {e}")
    
    # Test 3: Test API endpoints
    print("\n📋 STEP 3: API ENDPOINTS TEST")
    
    # Test get-category-paths
    try:
        paths_response = session.get(f"{base_url}/api/get-category-paths")
        if paths_response.status_code == 200:
            paths_data = paths_response.json()
            if paths_data.get('success'):
                print("✅ Category paths API working")
                print(f"   Categories: {paths_data.get('categories', [])}")
                print(f"   Source path: {paths_data.get('source_path', 'Not set')}")
                
                # Check new categories
                expected_categories = ['Miscellaneous', 'Private One', 'Private Two', 'To Be Processed']
                actual_categories = paths_data.get('categories', [])
                if all(cat in actual_categories for cat in expected_categories):
                    print("✅ New categories configured correctly")
                else:
                    print(f"❌ Categories mismatch. Expected: {expected_categories}, Got: {actual_categories}")
            else:
                print(f"❌ Category paths API error: {paths_data.get('error')}")
        else:
            print(f"❌ Category paths API failed: {paths_response.status_code}")
    except Exception as e:
        print(f"❌ Category paths API error: {e}")
    
    # Test get-folders with new source path
    try:
        folders_response = session.get(f"{base_url}/api/get-folders")
        if folders_response.status_code == 200:
            folders_data = folders_response.json()
            if folders_data.get('success'):
                print("✅ Folders API working")
                print(f"   Source path: {folders_data.get('source_path')}")
                print(f"   Folders found: {len(folders_data.get('folders', []))}")
            else:
                print(f"❌ Folders API error: {folders_data.get('error')}")
        else:
            print(f"❌ Folders API failed: {folders_response.status_code}")
    except Exception as e:
        print(f"❌ Folders API error: {e}")
    
    # Test 4: Test redirects
    print("\n📋 STEP 4: REDIRECT TESTS")
    
    redirect_tests = [
        ('/admin', 'Admin redirect'),
        ('/assigner', 'Assigner redirect'),
        ('/working-assigner', 'Working assigner redirect'),
        ('/tree-assigner', 'Tree assigner redirect')
    ]
    
    for path, description in redirect_tests:
        try:
            response = session.get(f"{base_url}{path}", allow_redirects=False)
            if response.status_code in [302, 301]:
                print(f"✅ {description} working (redirects to {response.headers.get('Location', 'unknown')})")
            else:
                print(f"❌ {description} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {description} error: {e}")
    
    # Test 5: Test path configuration
    print("\n📋 STEP 5: PATH CONFIGURATION TEST")
    
    # Test updating source path
    try:
        test_path = r"T:\To_Process\Rough folder\To Process\restore Files"
        update_data = {'path': test_path}
        update_response = session.post(f"{base_url}/api/update-source-path", 
                                     json=update_data,
                                     headers={'Content-Type': 'application/json'})
        
        if update_response.status_code == 200:
            update_result = update_response.json()
            if update_result.get('success'):
                print("✅ Source path update API working")
                print(f"   New path: {update_result.get('new_path')}")
            else:
                print(f"❌ Source path update error: {update_result.get('error')}")
        else:
            print(f"❌ Source path update failed: {update_response.status_code}")
    except Exception as e:
        print(f"❌ Source path update error: {e}")
    
    # Test 6: Verify directory creation
    print("\n📋 STEP 6: DIRECTORY VERIFICATION")
    
    expected_dirs = [
        r"T:\To_Process\Rough folder\To Check\Miscellaneous",
        r"T:\To_Process\Rough folder\To Check\Private one video", 
        r"T:\To_Process\Rough folder\To Check\Private Two",
        r"T:\To_Process\Rough folder\To Process\restore Files"
    ]
    
    for directory in expected_dirs:
        if os.path.exists(directory):
            print(f"✅ Directory exists: {directory}")
        else:
            print(f"❌ Directory missing: {directory}")
    
    print("\n" + "="*60)
    print("🎉 MERGED INTERFACE TESTING COMPLETE!")
    print("="*60)

if __name__ == "__main__":
    test_merged_interface()
