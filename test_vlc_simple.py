#!/usr/bin/env python3
"""
Simple VLC test with the exact failing path
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5001"

def test_vlc_simple():
    """Test VLC with the exact failing path"""
    print("🎬 SIMPLE VLC TEST - EXACT FAILING PATH")
    print("=" * 60)
    
    # Login
    session = requests.Session()
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if response.status_code == 200:
        print("✅ Login successful")
    else:
        print("❌ Login failed")
        return
    
    # Test with the exact failing path from your error
    failing_path = "estored filesZ6486_Glimpses_Cauvery-Calling_Media-Support_English_01Min-30Secs_Stems_04-Oct-2019OutputCaCa-Media-Glimpses-All.mov"
    
    print(f"\n🧪 Testing with exact failing path:")
    print(f"📥 Input: {failing_path}")
    
    # Test VLC integration
    vlc_data = {
        'file_path': failing_path
    }
    
    try:
        response = session.post(f"{BASE_URL}/api/open-vlc",
                              headers={'Content-Type': 'application/json'},
                              data=json.dumps(vlc_data))
        
        print(f"\n📥 Response:")
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ SUCCESS: {result.get('message')}")
            else:
                print(f"   ❌ FAILED: {result.get('error')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🔍 CHECK FLASK LOGS FOR DEBUG OUTPUT")
    print("Look for:")
    print("🔧 Original path: ...")
    print("🔧 Fixed path: ...")
    print("✅ File exists: ...")
    print("🚀 Launching VLC: ...")

    print("\n" + "=" * 60)
    print("🧪 NOW TESTING BOTH BATCH PROCESSING ENDPOINTS")
    print("=" * 60)

    # Test ENHANCED batch processing with the same failing path
    enhanced_batch_data = {
        'file_paths': [failing_path],
        'category': 'Social media single output with stems',
        'assign_to': 'Executor Public',
        'video_ids': 'TEST123',
        'url': 'https://test.com',
        'remarks': 'Test enhanced batch processing'
    }

    # Test OLD batch processing with the same failing path
    old_batch_data = {
        'files': [failing_path],
        'category': 'Tamil files',
        'user': 'Executor Public',
        'move_files': True
    }

    # Test 1: Enhanced batch processing
    print("\n🧪 Testing ENHANCED batch processing:")
    try:
        response = session.post(f"{BASE_URL}/api/enhanced-batch-assign",
                              headers={'Content-Type': 'application/json'},
                              data=json.dumps(enhanced_batch_data))

        print(f"   📥 Enhanced Batch Response:")
        print(f"   Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ ENHANCED BATCH SUCCESS: Processed {result.get('processed', 0)} files")
                print(f"   ❌ ENHANCED BATCH FAILED: {result.get('failed', 0)} files")
                if result.get('failed_files'):
                    for failed in result['failed_files']:
                        print(f"      - {failed.get('error')}: {failed.get('path')}")
            else:
                print(f"   ❌ ENHANCED BATCH FAILED: {result.get('error')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")

    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 2: Old batch processing (the one you're using)
    print("\n🧪 Testing OLD batch processing (the one causing issues):")
    try:
        response = session.post(f"{BASE_URL}/api/batch-assign",
                              headers={'Content-Type': 'application/json'},
                              data=json.dumps(old_batch_data))

        print(f"   📥 Old Batch Response:")
        print(f"   Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ OLD BATCH SUCCESS: Processed {result.get('processed', 0)} files")
                print(f"   ❌ OLD BATCH FAILED: {result.get('failed', 0)} files")
                if result.get('failed_files'):
                    for failed in result['failed_files']:
                        print(f"      - {failed.get('error')}: {failed.get('file')}")
            else:
                print(f"   ❌ OLD BATCH FAILED: {result.get('error')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")

    except Exception as e:
        print(f"   ❌ Exception: {e}")

    print("\n" + "=" * 60)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 60)
    print("✅ VLC integration should work")
    print("✅ Enhanced batch processing should work")
    print("✅ OLD batch processing should NOW work (FIXED!)")
    print("✅ All should use the same path fixing")
    print("🔧 Check Flask logs for:")
    print("   - 'Batch processing - Original/Fixed path' (enhanced)")
    print("   - 'OLD Batch processing - Original/Fixed path' (old)")
    print("\n🎉 BOTH BATCH ENDPOINTS NOW HAVE PATH FIXING!")

if __name__ == "__main__":
    test_vlc_simple()
