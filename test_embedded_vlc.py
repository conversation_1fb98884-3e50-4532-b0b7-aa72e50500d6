#!/usr/bin/env python3
"""
Test script for embedded VLC media player functionality
Verifies all VLC integration features in the Archives Management System
"""

import requests
import json
import os
import time
import sys
from pathlib import Path

class EmbeddedVLCTester:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.vlc_session_id = None
        self.test_results = []
    
    def print_status(self, message, status="INFO"):
        """Print status message with formatting"""
        symbols = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "ERROR": "❌",
            "WARNING": "⚠️",
            "TEST": "🧪"
        }
        print(f"{symbols.get(status, 'ℹ️')} {message}")
    
    def add_result(self, test_name, success, message=""):
        """Add test result"""
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
        
        status = "SUCCESS" if success else "ERROR"
        self.print_status(f"{test_name}: {message}", status)
    
    def login(self, username="admin", password="Shiva@123"):
        """Login to the system"""
        try:
            login_data = {'username': username, 'password': password}
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            
            if response.status_code == 200 and 'dashboard' in response.url:
                self.add_result("Admin Login", True, "Successfully logged in")
                return True
            else:
                self.add_result("Admin Login", False, f"Login failed: {response.status_code}")
                return False
        except Exception as e:
            self.add_result("Admin Login", False, f"Login error: {e}")
            return False
    
    def test_vlc_info(self):
        """Test VLC information endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/vlc/info")
            data = response.json()
            
            if data.get('success'):
                info = data.get('info', {})
                vlc_available = info.get('vlc_available', False)
                vlc_version = info.get('version', 'Unknown')
                
                self.add_result("VLC Info API", True, 
                              f"VLC Available: {vlc_available}, Version: {vlc_version}")
                return vlc_available
            else:
                self.add_result("VLC Info API", False, data.get('error', 'Unknown error'))
                return False
        except Exception as e:
            self.add_result("VLC Info API", False, f"Request error: {e}")
            return False
    
    def test_create_vlc_session(self):
        """Test VLC session creation"""
        try:
            response = self.session.post(f"{self.base_url}/api/vlc/create-session",
                                       headers={'Content-Type': 'application/json'})
            data = response.json()
            
            if data.get('success'):
                self.vlc_session_id = data.get('session_id')
                self.add_result("VLC Session Creation", True, 
                              f"Session ID: {self.vlc_session_id[:8]}...")
                return True
            else:
                self.add_result("VLC Session Creation", False, data.get('error', 'Unknown error'))
                return False
        except Exception as e:
            self.add_result("VLC Session Creation", False, f"Request error: {e}")
            return False
    
    def test_load_media(self, test_file_path=None):
        """Test loading media into VLC"""
        if not self.vlc_session_id:
            self.add_result("Load Media", False, "No VLC session available")
            return False
        
        # Try to find a test media file
        if not test_file_path:
            test_paths = [
                r"T:\To_Process\Rough folder\To Process\restore Files",
                r"T:\To_Process\Rough folder\restored files",
                "."
            ]
            
            for path in test_paths:
                if os.path.exists(path):
                    for root, dirs, files in os.walk(path):
                        for file in files:
                            if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                                test_file_path = os.path.join(root, file)
                                break
                        if test_file_path:
                            break
                    if test_file_path:
                        break
        
        if not test_file_path or not os.path.exists(test_file_path):
            self.add_result("Load Media", False, "No test media file found")
            return False
        
        try:
            payload = {
                'session_id': self.vlc_session_id,
                'file_path': test_file_path
            }
            
            response = self.session.post(f"{self.base_url}/api/vlc/load-media",
                                       headers={'Content-Type': 'application/json'},
                                       json=payload)
            data = response.json()
            
            if data.get('success'):
                media_info = data.get('media_info', {})
                self.add_result("Load Media", True, 
                              f"Loaded: {media_info.get('title', 'Unknown')}")
                return True
            else:
                self.add_result("Load Media", False, data.get('error', 'Unknown error'))
                return False
        except Exception as e:
            self.add_result("Load Media", False, f"Request error: {e}")
            return False
    
    def test_vlc_controls(self):
        """Test VLC playback controls"""
        if not self.vlc_session_id:
            self.add_result("VLC Controls", False, "No VLC session available")
            return False
        
        controls_to_test = ['play', 'pause', 'stop', 'volume']
        
        for control in controls_to_test:
            try:
                payload = {
                    'session_id': self.vlc_session_id,
                    'action': control
                }
                
                if control == 'volume':
                    payload['volume'] = 50
                
                response = self.session.post(f"{self.base_url}/api/vlc/control",
                                           headers={'Content-Type': 'application/json'},
                                           json=payload)
                data = response.json()
                
                if data.get('success'):
                    self.add_result(f"VLC Control ({control})", True, data.get('message', ''))
                else:
                    self.add_result(f"VLC Control ({control})", False, data.get('error', 'Unknown error'))
                
                # Small delay between controls
                time.sleep(0.5)
                
            except Exception as e:
                self.add_result(f"VLC Control ({control})", False, f"Request error: {e}")
        
        return True
    
    def test_vlc_status(self):
        """Test VLC status retrieval"""
        if not self.vlc_session_id:
            self.add_result("VLC Status", False, "No VLC session available")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/api/vlc/status",
                                      params={'session_id': self.vlc_session_id})
            data = response.json()
            
            if data.get('success'):
                status = data.get('status', {})
                state = status.get('state', 'unknown')
                volume = status.get('volume', 0)
                
                self.add_result("VLC Status", True, 
                              f"State: {state}, Volume: {volume}%")
                return True
            else:
                self.add_result("VLC Status", False, data.get('error', 'Unknown error'))
                return False
        except Exception as e:
            self.add_result("VLC Status", False, f"Request error: {e}")
            return False
    
    def test_embedded_vlc_page(self):
        """Test embedded VLC player page"""
        try:
            response = self.session.get(f"{self.base_url}/embedded-vlc-player")
            
            if response.status_code == 200:
                content = response.text
                
                # Check for key elements
                checks = [
                    ('VLC Player Container', 'vlc-player-container' in content),
                    ('Video Display', 'videoDisplay' in content),
                    ('Control Panel', 'controls-panel' in content),
                    ('File Browser', 'fileBrowser' in content),
                    ('JavaScript Functions', 'initializeVLCPlayer' in content)
                ]
                
                all_passed = True
                for check_name, passed in checks:
                    if passed:
                        self.add_result(f"Page Element ({check_name})", True, "Found")
                    else:
                        self.add_result(f"Page Element ({check_name})", False, "Missing")
                        all_passed = False
                
                self.add_result("Embedded VLC Page", all_passed, 
                              "Page loaded successfully" if all_passed else "Some elements missing")
                return all_passed
            else:
                self.add_result("Embedded VLC Page", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.add_result("Embedded VLC Page", False, f"Request error: {e}")
            return False
    
    def test_cleanup_session(self):
        """Test VLC session cleanup"""
        if not self.vlc_session_id:
            self.add_result("Session Cleanup", False, "No VLC session to clean up")
            return False
        
        try:
            payload = {'session_id': self.vlc_session_id}
            
            response = self.session.post(f"{self.base_url}/api/vlc/cleanup",
                                       headers={'Content-Type': 'application/json'},
                                       json=payload)
            data = response.json()
            
            if data.get('success'):
                self.add_result("Session Cleanup", True, "Session cleaned up successfully")
                self.vlc_session_id = None
                return True
            else:
                self.add_result("Session Cleanup", False, data.get('error', 'Unknown error'))
                return False
        except Exception as e:
            self.add_result("Session Cleanup", False, f"Request error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all VLC tests"""
        self.print_status("Starting Embedded VLC Tests", "TEST")
        print("=" * 60)
        
        # Login first
        if not self.login():
            self.print_status("Cannot proceed without login", "ERROR")
            return False
        
        # Test VLC availability
        vlc_available = self.test_vlc_info()
        
        # Test embedded VLC page
        self.test_embedded_vlc_page()
        
        if vlc_available:
            # Test VLC session management
            if self.test_create_vlc_session():
                # Test media loading
                self.test_load_media()
                
                # Test controls
                self.test_vlc_controls()
                
                # Test status
                self.test_vlc_status()
                
                # Cleanup
                self.test_cleanup_session()
        else:
            self.print_status("VLC not available - skipping VLC-specific tests", "WARNING")
        
        # Print summary
        self.print_summary()
        
        return True
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        self.print_status("Test Summary", "TEST")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        self.print_status(f"Total Tests: {total_tests}", "INFO")
        self.print_status(f"Passed: {passed_tests}", "SUCCESS")
        if failed_tests > 0:
            self.print_status(f"Failed: {failed_tests}", "ERROR")
        
        # Show failed tests
        if failed_tests > 0:
            print("\nFailed Tests:")
            for result in self.test_results:
                if not result['success']:
                    self.print_status(f"{result['test']}: {result['message']}", "ERROR")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        self.print_status(f"Success Rate: {success_rate:.1f}%", 
                         "SUCCESS" if success_rate >= 80 else "WARNING")

def main():
    """Main function"""
    print("🎬 Archives Management System - Embedded VLC Tests")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:5001", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not running or not accessible")
            print("Please start the Archives Management System first")
            sys.exit(1)
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server at http://127.0.0.1:5001")
        print("Please start the Archives Management System first")
        sys.exit(1)
    
    tester = EmbeddedVLCTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
