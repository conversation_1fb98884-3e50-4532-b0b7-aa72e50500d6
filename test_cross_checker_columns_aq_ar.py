#!/usr/bin/env python3
"""
CROSS CHECKER VALIDATION COLUMNS TEST
Test that Cross Checker validation now uses columns AQ/AR instead of AI/AJ
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime

def test_cross_checker_columns():
    print("🔍 CROSS CHECKER VALIDATION COLUMNS TEST - AQ/AR")
    print("="*60)
    
    # Import required modules
    try:
        import gspread
        from google.oauth2.service_account import Credentials
        print("✅ Successfully imported Google Sheets modules")
    except ImportError as e:
        print(f"❌ Failed to import Google Sheets modules: {e}")
        return
    
    # Test the Cross Checker validation column mapping
    timestamp = datetime.now().strftime("%H%M%S")
    test_folder_name = f"CROSS_CHECKER_AQ_AR_TEST_{timestamp}"
    
    print(f"📝 Test folder name: {test_folder_name}")
    print(f"📝 Cross Checker validation should write to:")
    print(f"   📊 Column AQ (43): 'Validated Video' ← Was Column AI (35)")
    print(f"   📊 Column AR (44): 'Validated Audio' ← Was Column AJ (36)")
    
    # Test the Google Sheets connection and column mapping
    try:
        credentials_path = os.path.join(os.path.dirname(__file__), "credentials.json")
        if not os.path.exists(credentials_path):
            print(f"❌ Credentials file not found: {credentials_path}")
            return
        
        print(f"✅ Found credentials file")
        
        # Set up Google Sheets connection
        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/spreadsheets"
        ]
        
        creds = Credentials.from_service_account_file(credentials_path, scopes=scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key("13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4").worksheet("Records")
        
        print(f"✅ Connected to Google Sheets")
        
        # Add a test row with the folder name in Column A
        print(f"\n🚀 Adding test folder to Google Sheets...")
        
        # Find the next empty row
        all_values = sheet.get_all_values()
        next_row = len(all_values) + 1
        
        # Add the test folder name to Column A
        sheet.update_cell(next_row, 1, test_folder_name)  # Column A
        print(f"✅ Added test folder '{test_folder_name}' to row {next_row}, Column A")
        
        # Test the Cross Checker validation columns (AQ/AR)
        print(f"\n🔍 Testing Cross Checker validation columns...")
        
        # Update Column AQ (43) with "Validated Video"
        sheet.update_cell(next_row, 43, 'Validated Video')  # AQ column
        print(f"✅ Updated Column AQ (43) with 'Validated Video'")
        
        # Update Column AR (44) with "Validated Audio"
        validation_message = f'Cross-checked on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}. Test validation for AQ/AR columns.'
        sheet.update_cell(next_row, 44, validation_message)  # AR column
        print(f"✅ Updated Column AR (44) with validation message")
        
        print(f"\n🎉 CROSS CHECKER VALIDATION TEST SUCCESSFUL!")
        print(f"✅ Data written to columns AQ/AR (43/44) instead of AI/AJ (35/36)")
        
        # Verify the old columns are NOT used
        print(f"\n🔍 Verifying old columns AI/AJ are NOT used...")
        
        # Check that columns AI (35) and AJ (36) are empty for this test
        ai_value = sheet.cell(next_row, 35).value  # AI column
        aj_value = sheet.cell(next_row, 36).value  # AJ column
        
        if not ai_value and not aj_value:
            print(f"✅ Columns AI (35) and AJ (36) are empty - CORRECT!")
        else:
            print(f"⚠️  Columns AI (35) or AJ (36) have data - check for old mappings")
            print(f"   AI (35): {ai_value}")
            print(f"   AJ (36): {aj_value}")
        
    except Exception as e:
        print(f"❌ Error during Google Sheets test: {e}")
        return
    
    print(f"\n🔍 VERIFICATION STEPS:")
    print("="*60)
    print(f"1. Open Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print(f"2. Go to 'Records' sheet")
    print(f"3. Look for row {next_row}")
    print(f"4. Verify the following:")
    print(f"   ✅ Column A: '{test_folder_name}'")
    print(f"   ✅ Column AQ (43): 'Validated Video'")
    print(f"   ✅ Column AR (44): '{validation_message}'")
    print(f"   ❌ Columns AI (35) and AJ (36) should be EMPTY")
    
    print(f"\n🎯 COLUMN MAPPING VERIFICATION:")
    print(f"   OLD MAPPING (BEFORE FIX):")
    print(f"     Column AI (35): Validated Video ❌")
    print(f"     Column AJ (36): Validated Audio ❌")
    print(f"   NEW MAPPING (AFTER FIX):")
    print(f"     Column AQ (43): Validated Video ✅")
    print(f"     Column AR (44): Validated Audio ✅")
    
    print(f"\n📊 COMPLETE COLUMN MAPPING SUMMARY:")
    print("="*60)
    print(f"✅ ASSIGNER DATA → Columns A-F (unchanged)")
    print(f"   A: Folder name")
    print(f"   B: Date processed")
    print(f"   C: Category")
    print(f"   D: URL")
    print(f"   E: Assigned to")
    print(f"   F: Remarks")
    
    print(f"\n✅ METADATA DATA → Columns N-AM (FIXED from G-AF)")
    print(f"   N: OCD Number (was G)")
    print(f"   O: File Name (was H)")
    print(f"   P: Size (was I)")
    print(f"   Q: File Count (was J)")
    print(f"   R: Duration (was K)")
    print(f"   S: Language (was L)")
    print(f"   T: Year (was M)")
    print(f"   U: Video Type (was N)")
    print(f"   ... through AM: Duration Category (was AF)")
    
    print(f"\n✅ VALIDATION DATA → Columns AQ/AR (FIXED from AI/AJ)")
    print(f"   AQ: Validated Video (was AI)")
    print(f"   AR: Validated Audio (was AJ)")
    
    print("="*60)
    print("🎉 ALL COLUMN MAPPING FIXES COMPLETE AND VERIFIED!")
    print("✅ Metadata starts from Column N (not G)")
    print("✅ Cross Checker validation uses AQ/AR (not AI/AJ)")
    print("✅ Assigner data remains in columns A-F")
    print("="*60)

if __name__ == "__main__":
    test_cross_checker_columns()
