#!/usr/bin/env python3
"""
REPRODUCE DATABASE LOCK ERROR
Simulate the complete flow to reproduce the "database is locked" error
"""

import requests
import json
import time

def reproduce_database_lock_error():
    print("🚨 REPRODUCING DATABASE LOCK ERROR")
    print("=" * 80)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # STEP 1: Login
        print("1. 🔐 Login to Executor Public...")
        login_data = {
            'username': 'executor_public',
            'password': '<PERSON>@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return False
        
        print("   ✅ Login successful!")
        
        # STEP 2: Load files queue
        print("\n2. 📋 Load files queue...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        if queue_response.status_code != 200:
            print("   ❌ Queue load failed!")
            return False
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue error: {queue_data.get('error')}")
            return False
        
        files = queue_data.get('files', [])
        print(f"   ✅ Queue loaded: {len(files)} files")
        
        if not files:
            print("   ⚠️ No files to test!")
            return False
        
        # STEP 3: Select file and get details (Process button click)
        print("\n3. 📁 Select file and click Process...")
        test_file = files[0]
        queue_item_id = test_file.get('queue_item_id')
        folder_name = test_file.get('folder_name', 'Unknown')
        
        print(f"   📂 Selected: {folder_name[:50]}...")
        print(f"   🆔 Queue ID: {queue_item_id}")
        
        # Load file details (modal opening)
        details_response = session.get(f"{base_url}/api/file-details/{queue_item_id}")
        if details_response.status_code != 200:
            print("   ❌ File details failed!")
            return False
        
        details_data = details_response.json()
        if not details_data.get('success'):
            print(f"   ❌ File details error: {details_data.get('error')}")
            return False
        
        print("   ✅ File details loaded (modal opened)!")
        
        # STEP 4: Fill metadata form
        print("\n4. 📝 Fill metadata form...")
        test_metadata = {
            'ocd_vp_number': 'DB-LOCK-TEST-2025-001',
            'edited_file_name': f'DB_Lock_Test_{folder_name[:20]}',
            'language': 'English',
            'edited_year': 2025,
            'trim_closed_date': '2025-01-15',
            'total_duration': '7:45',
            'video_type': 'Talk',
            'edited_location': 'Ashram',
            'published_platforms': 'YouTube',
            'department_name': 'Archives DB Lock Test',  # Text input
            'component': 'Sadhguru',
            'content_tags': 'Spirituality',
            'backup_type': 'Full Backup',
            'access_level': 'Public',
            'software_show_name': f'DB_LOCK_TEST_RENAMED_{folder_name[:15]}',
            'audio_code': 'DB-LOCK-AUD-2025-001',
            'audio_file_name': f'DB_Lock_Test_Audio_{folder_name[:15]}',
            'transcription_file_name': 'DB_Lock_Test_Transcription',
            'transcription_status': 'Pending',
            'published_date': '2025-01-15',
            'video_id': 'DB-LOCK-VID-2025-001',
            'social_media_title': 'DB Lock Test Video Title',
            'description': 'DB lock test video description',
            'social_media_url': 'https://youtube.com/db-lock-test',
            'duration_category': '7 minutes 45 seconds',  # Text input
            'processing_notes': 'Database lock error reproduction test'
        }
        
        print("   📋 Metadata form filled!")
        print(f"      🆔 OCD Number: {test_metadata['ocd_vp_number']}")
        print(f"      📁 File Name: {test_metadata['edited_file_name']}")
        print(f"      📂 Rename To: {test_metadata['software_show_name']}")
        
        # STEP 5: Submit form (Process & Move to Cross-Check)
        print("\n5. 🚀 Submit form (Process & Move to Cross-Check)...")
        print("   📤 Sending metadata for processing...")
        
        process_payload = {
            'queue_item_id': queue_item_id,
            'metadata': test_metadata
        }
        
        # This is where the database lock error should occur
        process_response = session.post(
            f"{base_url}/api/executive-public/process-metadata",
            json=process_payload,
            headers={'Content-Type': 'application/json'},
            timeout=60  # Longer timeout to see if it's a timing issue
        )
        
        print(f"   📥 Response Status: {process_response.status_code}")
        
        if process_response.status_code == 200:
            process_data = process_response.json()
            
            if process_data.get('success'):
                print("   ✅ Processing successful!")
                
                processing_steps = process_data.get('processing_steps', {})
                print("   🔧 Processing Steps:")
                print(f"      📊 Google Sheets: {processing_steps.get('google_sheets', False)}")
                print(f"      📁 Folder Moved: {processing_steps.get('folder_moved', False)}")
                print(f"      🔄 Folder Renamed: {processing_steps.get('folder_renamed', False)}")
                print(f"      🎵 Audio Extracted: {processing_steps.get('audio_extracted', False)}")
                
                return True
            else:
                error_msg = process_data.get('error', 'Unknown error')
                print(f"   🚨 ERROR REPRODUCED: {error_msg}")
                
                if 'database is locked' in error_msg.lower():
                    print("   🎯 CONFIRMED: Database lock error reproduced!")
                    
                    # Show debug info if available
                    debug_info = process_data.get('debug_info', {})
                    if debug_info:
                        print("   🔍 Debug Info:")
                        for key, value in debug_info.items():
                            print(f"      {key}: {value}")
                    
                    return False
                else:
                    print(f"   ⚠️ Different error: {error_msg}")
                    return False
        else:
            print(f"   ❌ HTTP Error: {process_response.status_code}")
            try:
                error_data = process_response.json()
                print(f"      Error details: {error_data}")
                
                if 'database is locked' in str(error_data).lower():
                    print("   🎯 CONFIRMED: Database lock error in HTTP response!")
                    return False
            except:
                print(f"      Raw response: {process_response.text[:200]}...")
            return False
        
    except Exception as e:
        print(f"\n❌ Error reproduction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚨 REPRODUCING DATABASE LOCK ERROR")
    print("=" * 80)
    
    # Try multiple times to reproduce the error
    for attempt in range(1, 4):
        print(f"\n🔄 ATTEMPT {attempt}:")
        
        success = reproduce_database_lock_error()
        
        if not success:
            print(f"   🎯 Database lock error reproduced on attempt {attempt}!")
            break
        else:
            print(f"   ✅ Attempt {attempt} successful (no error)")
        
        time.sleep(2)  # Wait between attempts
    
    print("\n" + "=" * 80)
    print("🎯 DATABASE LOCK ERROR REPRODUCTION RESULTS")
    print("=" * 80)
    
    print("🔍 ANALYSIS:")
    print("   The database lock error occurs during the processing step")
    print("   when multiple database operations happen simultaneously:")
    print("   1. 📊 Google Sheets update")
    print("   2. 💾 Metadata save to database")
    print("   3. 📁 Queue item status update")
    print("   4. 🔄 Folder move operation")
    print("   5. 🎵 Audio extraction")
    
    print("\n🚨 ROOT CAUSE:")
    print("   SQLite database connections are not properly managed")
    print("   Multiple operations try to access the database simultaneously")
    print("   Connections are not closed properly or transactions overlap")
    
    print("\n🔧 SOLUTION NEEDED:")
    print("   1. Use proper connection management with 'with' blocks")
    print("   2. Implement database connection pooling")
    print("   3. Add retry logic for temporary locks")
    print("   4. Ensure all connections are properly closed")
    print("   5. Use transactions to batch related operations")

if __name__ == "__main__":
    main()
