#!/usr/bin/env python3
"""
DEBUG ARCHIVES CONSOLE GOOGLE SHEETS MAPPING
Investigate exactly where data is being written when using Archives Assignment Console
"""

import requests
import json
from datetime import datetime
import time

def debug_archives_console_mapping():
    print("🚨 DEBUGGING ARCHIVES CONSOLE GOOGLE SHEETS MAPPING")
    print("="*70)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Step 1: Login as Assigner
    print("\n📋 STEP 1: Login as Assigner")
    print("-" * 40)
    
    login_data = {'username': 'assigner', 'password': '<PERSON>@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Assigner login failed: {login_response.status_code}")
        return
    
    print("✅ Logged in as Assigner")
    
    # Step 2: Get folders
    print("\n📁 STEP 2: Getting folders")
    print("-" * 40)
    
    folders_response = session.get(f"{base_url}/api/get-folders")
    if folders_response.status_code != 200:
        print(f"❌ Folders API failed: {folders_response.status_code}")
        return
    
    folders_data = folders_response.json()
    if not folders_data.get('success') or not folders_data.get('folders'):
        print("❌ No folders available")
        return
    
    # Use first folder
    test_folder = folders_data['folders'][0]
    folder_path = test_folder['path']
    folder_name = test_folder['name']
    
    print(f"✅ Using test folder: {folder_name}")
    print(f"✅ Folder path: {folder_path}")
    
    # Step 3: Add to queue with unique identifier
    print("\n📋 STEP 3: Adding folder to queue")
    print("-" * 40)
    
    timestamp = datetime.now().strftime("%H%M%S")
    queue_data = {
        'folder_paths': [folder_path],
        'category': 'Internal video without stems',
        'assign_to': 'Executor Public',
        'video_ids': f'DEBUG_MAPPING_{timestamp}',
        'url': f'https://debug-mapping-{timestamp}.com',
        'remarks': f'DEBUG MAPPING TEST - COLUMNS M-R ISSUE - {timestamp}'
    }
    
    print(f"📝 Queue data:")
    print(f"   📁 Folder: {folder_name}")
    print(f"   📂 Category: {queue_data['category']}")
    print(f"   👤 Assign to: {queue_data['assign_to']}")
    print(f"   🆔 Video IDs: {queue_data['video_ids']}")
    print(f"   🔗 URL: {queue_data['url']}")
    print(f"   📝 Remarks: {queue_data['remarks']}")
    
    add_response = session.post(f"{base_url}/api/add-to-queue", json=queue_data)
    if add_response.status_code != 200:
        print(f"❌ Add to queue failed: {add_response.status_code}")
        print(f"❌ Response: {add_response.text}")
        return
    
    add_result = add_response.json()
    print(f"✅ Added to queue: {add_result.get('success', False)}")
    print(f"✅ Added {add_result.get('added', 0)} items")
    
    # Step 4: Process queue (THIS IS WHERE THE ISSUE OCCURS)
    print(f"\n🚀 STEP 4: Processing queue (CRITICAL - WHERE MAPPING ISSUE OCCURS)")
    print("-" * 40)
    
    print(f"⚠️  This step calls log_to_google_sheets which should write to columns A-F")
    print(f"⚠️  But user reports data going to columns M-R")
    print(f"⚠️  Let's see what actually happens...")
    
    process_response = session.post(f"{base_url}/api/process-queue")
    
    if process_response.status_code == 200:
        process_result = process_response.json()
        print(f"✅ Queue processed: {process_result.get('success', False)}")
        print(f"✅ Processed {process_result.get('processed', 0)} items")
        print(f"❌ Failed {process_result.get('failed', 0)} items")
        
        if process_result.get('processed_items'):
            for item in process_result['processed_items']:
                print(f"   📁 Processed: {item.get('folder_name')}")
                print(f"   📂 Category: {item.get('category')}")
                print(f"   🔄 Operation: {item.get('operation')}")
    else:
        print(f"❌ Process queue failed: {process_response.status_code}")
        print(f"❌ Response: {process_response.text}")
        return
    
    # Step 5: Analysis
    print(f"\n🔍 STEP 5: ANALYSIS")
    print("="*70)
    
    print(f"📊 EXPECTED BEHAVIOR:")
    print(f"   ✅ Archives Console calls /api/process-queue")
    print(f"   ✅ process_queue() calls log_to_google_sheets()")
    print(f"   ✅ log_to_google_sheets() writes to columns A-F")
    print(f"   ✅ Data should appear in columns A-F")
    
    print(f"\n🚨 REPORTED ISSUE:")
    print(f"   ❌ User sees data in columns M-R")
    print(f"   ❌ This suggests wrong function is being called")
    print(f"   ❌ Or log_to_google_sheets has wrong column mapping")
    
    print(f"\n🔍 INVESTIGATION NEEDED:")
    print(f"1. Check Google Sheets for folder: {folder_name}")
    print(f"2. Look for Video IDs: DEBUG_MAPPING_{timestamp}")
    print(f"3. Check which columns contain the data:")
    print(f"   - If in A-F: log_to_google_sheets is working correctly")
    print(f"   - If in M-R: There's a bug in the column mapping")
    print(f"4. Verify the exact function being called")
    
    print(f"\n🎯 VERIFICATION STEPS:")
    print("="*70)
    print(f"1. Open Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print(f"2. Go to 'Records' sheet")
    print(f"3. Search for: DEBUG_MAPPING_{timestamp}")
    print(f"4. Check which columns contain the data:")
    print(f"   📊 Column A: Should have folder name")
    print(f"   📊 Column B: Should have date")
    print(f"   📊 Column C: Should have 'Internal video without stems'")
    print(f"   📊 Column D: Should have 'https://debug-mapping-{timestamp}.com'")
    print(f"   📊 Column E: Should have 'Executor Public'")
    print(f"   📊 Column F: Should have 'DEBUG MAPPING TEST...'")
    print(f"   ❌ Columns M-R: Should be EMPTY")
    
    print(f"\n🔧 IF DATA IS IN COLUMNS M-R:")
    print(f"   1. The log_to_google_sheets function has wrong column mapping")
    print(f"   2. OR a different function is being called")
    print(f"   3. OR there's a bug in the append_row logic")
    
    print("="*70)
    print("🎯 DEBUG TEST COMPLETE - MANUAL VERIFICATION REQUIRED")
    print("="*70)

if __name__ == "__main__":
    debug_archives_console_mapping()
