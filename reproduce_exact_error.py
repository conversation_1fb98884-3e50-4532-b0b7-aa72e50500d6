#!/usr/bin/env python3
"""
REPRODUCE THE EXACT ERROR
Simulate the exact user flow:
1. <PERSON><PERSON> to Executor Public
2. Select a folder from queue
3. Click Process
4. Fill metadata form
5. Click "Process & Move to Cross-Check"
6. Reproduce the "source folder not found" error
"""

import requests
import json
import os

def reproduce_exact_error():
    print("🚨 REPRODUCING EXACT ERROR - BROWSER SIMULATION")
    print("=" * 80)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # STEP 1: Login (exactly like user)
        print("1. 🔐 Login to Executor Public...")
        login_data = {
            'username': 'executor_public',
            'password': '<PERSON>@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return False
        
        print("   ✅ Login successful!")
        
        # STEP 2: Load the Executor Public page
        print("\n2. 🌐 Load Executor Public page...")
        page_response = session.get(f"{base_url}/executor-public")
        if page_response.status_code != 200:
            print("   ❌ Page load failed!")
            return False
        
        print("   ✅ Page loaded!")
        
        # STEP 3: Get files in queue (like the page loading)
        print("\n3. 📋 Load files queue...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        if queue_response.status_code != 200:
            print("   ❌ Queue load failed!")
            return False
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue error: {queue_data.get('error')}")
            return False
        
        files = queue_data.get('files', [])
        print(f"   ✅ Queue loaded: {len(files)} files")
        
        if not files:
            print("   ⚠️ No files to test!")
            return False
        
        # STEP 4: Select first file (like user clicking on a file)
        print("\n4. 📁 Select file for processing...")
        test_file = files[0]
        queue_item_id = test_file.get('queue_item_id')
        folder_name = test_file.get('folder_name', 'Unknown')
        folder_path = test_file.get('folder_path', '')
        category = test_file.get('category', 'Unknown')
        
        print(f"   📂 Selected: {folder_name}")
        print(f"   📍 Path: {folder_path}")
        print(f"   📊 Category: {category}")
        print(f"   🆔 Queue ID: {queue_item_id}")
        
        # Check if folder actually exists
        folder_exists = os.path.exists(folder_path) if folder_path else False
        print(f"   📁 Folder exists: {'✅ YES' if folder_exists else '❌ NO'}")
        
        if not folder_exists:
            print(f"   🚨 FOUND THE ISSUE! Folder doesn't exist: {folder_path}")
        
        # STEP 5: Get file details (like clicking Process button)
        print("\n5. 🔍 Get file details...")
        details_response = session.get(f"{base_url}/api/file-details/{queue_item_id}")
        if details_response.status_code != 200:
            print("   ❌ File details failed!")
            return False
        
        details_data = details_response.json()
        if not details_data.get('success'):
            print(f"   ❌ File details error: {details_data.get('error')}")
            return False
        
        file_details = details_data.get('file_details', {})
        print("   ✅ File details loaded!")
        print(f"      📊 File Count: {file_details.get('file_count', 0)}")
        print(f"      💾 Total Size: {file_details.get('total_size_formatted', 'Unknown')}")
        print(f"      🆔 Video IDs: {file_details.get('video_ids', 'Not specified')}")
        print(f"      💬 Remarks: {file_details.get('remarks', 'No remarks')}")
        
        # STEP 6: Fill metadata form (like user filling the form)
        print("\n6. 📝 Fill metadata form...")
        test_metadata = {
            'ocd_vp_number': 'ERROR-TEST-2025-001',
            'edited_file_name': f'Error_Test_{folder_name[:20]}',
            'language': 'English',
            'edited_year': 2025,
            'trim_closed_date': '2025-01-15',
            'total_duration': '8:30',
            'video_type': 'Talk',
            'edited_location': 'Ashram',
            'published_platforms': 'YouTube',
            'department_name': 'Archives Error Test',
            'component': 'Sadhguru',
            'content_tags': 'Spirituality',
            'backup_type': 'Full Backup',
            'access_level': 'Public',
            'software_show_name': f'ERROR_TEST_RENAMED_{folder_name[:15]}',  # This will be the new folder name
            'audio_code': 'ERROR-AUD-2025-001',
            'audio_file_name': f'Error_Test_Audio_{folder_name[:15]}',
            'transcription_file_name': 'Error_Test_Transcription',
            'transcription_status': 'Pending',
            'published_date': '2025-01-15',
            'video_id': 'ERROR-VID-2025-001',
            'social_media_title': 'Error Test Video Title',
            'description': 'Error test video description',
            'social_media_url': 'https://youtube.com/error-test',
            'duration_category': '8 minutes 30 seconds',
            'processing_notes': 'Error reproduction test'
        }
        
        print("   📋 Metadata prepared:")
        print(f"      🆔 OCD Number: {test_metadata['ocd_vp_number']}")
        print(f"      📁 File Name: {test_metadata['edited_file_name']}")
        print(f"      📂 Rename To: {test_metadata['software_show_name']}")
        print(f"      🎵 Audio File: {test_metadata['audio_file_name']}")
        
        # STEP 7: Submit metadata (like clicking "Process & Move to Cross-Check")
        print("\n7. 🚀 Submit metadata form (Process & Move to Cross-Check)...")
        
        process_payload = {
            'queue_item_id': queue_item_id,
            'metadata': test_metadata
        }
        
        print(f"   📤 Sending payload:")
        print(f"      🆔 Queue Item ID: {queue_item_id}")
        print(f"      📊 Metadata fields: {len(test_metadata)}")
        
        process_response = session.post(
            f"{base_url}/api/executive-public/process-metadata",
            json=process_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   📥 Response Status: {process_response.status_code}")
        
        if process_response.status_code == 200:
            process_data = process_response.json()
            
            if process_data.get('success'):
                print("   ✅ Processing successful!")
                
                processing_steps = process_data.get('processing_steps', {})
                print("   🔧 Processing Steps:")
                print(f"      📊 Google Sheets: {processing_steps.get('google_sheets', False)}")
                print(f"      📁 Folder Moved: {processing_steps.get('folder_moved', False)}")
                print(f"      🔄 Folder Renamed: {processing_steps.get('folder_renamed', False)}")
                print(f"      🎵 Audio Extracted: {processing_steps.get('audio_extracted', False)}")
                
                print("   📋 Results:")
                print(f"      📂 Original: {process_data.get('original_folder', 'Unknown')}")
                print(f"      📂 Renamed: {process_data.get('renamed_folder', 'Unknown')}")
                print(f"      📍 Destination: {process_data.get('destination', 'Unknown')}")
                
                return True
            else:
                error_msg = process_data.get('error', 'Unknown error')
                print(f"   🚨 REPRODUCED THE ERROR: {error_msg}")
                
                # Analyze the error
                if 'source folder not found' in error_msg.lower():
                    print("   🔍 ERROR ANALYSIS:")
                    print(f"      📂 Expected folder: {folder_path}")
                    print(f"      📁 Folder exists: {folder_exists}")
                    print(f"      🆔 Queue item ID: {queue_item_id}")
                    
                    # Check what the backend is actually looking for
                    print("   🔧 DEBUGGING INFO:")
                    print(f"      📊 Queue data folder_path: {test_file.get('folder_path')}")
                    print(f"      📊 File details: {file_details}")
                
                return False
        else:
            print(f"   ❌ HTTP Error: {process_response.status_code}")
            try:
                error_data = process_response.json()
                print(f"      Error details: {error_data}")
            except:
                print(f"      Raw response: {process_response.text[:200]}...")
            return False
        
    except Exception as e:
        print(f"\n❌ Error reproduction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚨 REPRODUCING EXACT USER ERROR")
    print("=" * 80)
    
    success = reproduce_exact_error()
    
    print("\n" + "=" * 80)
    print("🎯 ERROR REPRODUCTION RESULTS")
    print("=" * 80)
    
    if success:
        print("✅ No error found - system working correctly!")
    else:
        print("🚨 ERROR REPRODUCED SUCCESSFULLY!")
        print("   Now we can diagnose and fix the issue...")
    
    return success

if __name__ == "__main__":
    main()
