#!/usr/bin/env python3
"""
TEST REAL ASSIGNMENT WORKFLOW
Test the actual assignment workflow to see where data goes in Google Sheets
"""

import requests
import json
from datetime import datetime

def test_real_assignment():
    print("🚨 REAL ASSIGNMENT WORKFLOW TEST")
    print("="*60)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Login as assigner
    print("🔐 Logging in as assigner...")
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    print("✅ Login successful")
    
    # Get folders
    print("\n📁 Getting available folders...")
    folders_response = session.get(f"{base_url}/api/get-folders")
    
    if folders_response.status_code != 200:
        print(f"❌ Folders API failed: {folders_response.status_code}")
        return
    
    folders_data = folders_response.json()
    if not folders_data.get('success') or not folders_data.get('folders'):
        print("❌ No folders available")
        return
    
    # Use first folder for testing
    test_folder = folders_data['folders'][0]
    folder_path = test_folder['path']
    folder_name = test_folder['name']
    
    print(f"✅ Using test folder: {folder_name}")
    
    # Step 1: Add to queue (this should NOT write to Google Sheets yet)
    print(f"\n📋 STEP 1: Adding folder to queue...")
    
    queue_data = {
        'folder_paths': [folder_path],
        'category': 'Internal video without stems',
        'assign_to': 'Executor Public',
        'video_ids': f'VID-TEST-{datetime.now().strftime("%H%M%S")}',
        'url': 'https://assignment-test.com',
        'remarks': f'Assignment test at {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
    }
    
    add_response = session.post(f"{base_url}/api/add-to-queue", json=queue_data)
    
    if add_response.status_code == 200:
        add_result = add_response.json()
        print(f"✅ Added to queue: {add_result.get('success', False)}")
        print(f"✅ Added {add_result.get('added', 0)} items")
    else:
        print(f"❌ Add to queue failed: {add_response.status_code}")
        return
    
    # Step 2: Process queue (this SHOULD write to Google Sheets columns A-F)
    print(f"\n🚀 STEP 2: Processing queue (should write to columns A-F)...")
    
    process_response = session.post(f"{base_url}/api/process-queue")
    
    if process_response.status_code == 200:
        process_result = process_response.json()
        print(f"✅ Queue processed: {process_result.get('success', False)}")
        print(f"✅ Processed {process_result.get('processed', 0)} items")
        
        if process_result.get('processed_items'):
            for item in process_result['processed_items']:
                print(f"   📁 Processed: {item.get('folder_name')}")
                print(f"   📂 Category: {item.get('category')}")
    else:
        print(f"❌ Process queue failed: {process_response.status_code}")
        print(f"❌ Response: {process_response.text}")
    
    print("\n" + "="*60)
    print("🎯 EXPECTED RESULT:")
    print("1. Data should appear in Google Sheets columns A-F")
    print("2. Column A: Folder name")
    print("3. Column B: Date processed")
    print("4. Column C: Category (Internal video without stems)")
    print("5. Column D: URL (https://assignment-test.com)")
    print("6. Column E: Assigned to (Executor Public)")
    print("7. Column F: Remarks with Video IDs")
    print("\n🔍 MANUAL VERIFICATION:")
    print("Open: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print("Check 'Records' sheet for the test data")
    print("="*60)

if __name__ == "__main__":
    test_real_assignment()
