#!/usr/bin/env python3
"""
COMPREHENSIVE COLUMN MAPPING FIXES TEST
Test the fixed Google Sheets column mappings:
1. Metadata should start from Column N (not G)
2. Cross Checker validation should use AQ/AR (not AI/AJ)
"""

import requests
import json
from datetime import datetime
import time

def test_column_mapping_fixes():
    print("🚨 TESTING GOOGLE SHEETS COLUMN MAPPING FIXES")
    print("="*70)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Test 1: Verify Assigner workflow still writes to A-F
    print("\n✅ TEST 1: ASSIGNER WORKFLOW (Should write to columns A-F)")
    print("-" * 50)
    
    # Login as assigner
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Assigner login failed: {login_response.status_code}")
        return
    
    print("✅ Logged in as Assigner")
    
    # Test Assigner Google Sheets API (should write to A-F)
    timestamp = datetime.now().strftime("%H%M%S")
    assigner_data = {
        'folder_name': f'ASSIGNER_COLUMNS_AF_{timestamp}',
        'date_processed': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'moved_to_folder': 'Internal video without stems',
        'social_media_url': f'https://assigner-af-{timestamp}.com',
        'assigned_to': 'Executor Public',
        'remarks': f'ASSIGNER TEST - COLUMNS A-F - {timestamp}'
    }
    
    print(f"📝 Testing Assigner data (should go to A-F):")
    print(f"   📁 Column A: {assigner_data['folder_name']}")
    print(f"   📅 Column B: {assigner_data['date_processed']}")
    print(f"   📂 Column C: {assigner_data['moved_to_folder']}")
    print(f"   🔗 Column D: {assigner_data['social_media_url']}")
    print(f"   👤 Column E: {assigner_data['assigned_to']}")
    print(f"   📝 Column F: {assigner_data['remarks']}")
    
    assigner_response = session.post(f"{base_url}/api/test-google-sheets", json=assigner_data)
    
    if assigner_response.status_code == 200:
        result = assigner_response.json()
        print(f"✅ Assigner test result: {result.get('success', False)}")
        print(f"✅ Message: {result.get('message', 'No message')}")
    else:
        print(f"❌ Assigner test failed: {assigner_response.status_code}")
    
    # Test 2: Test metadata update (should now write to N onward)
    print(f"\n🔧 TEST 2: METADATA UPDATE (Should write to columns N onward)")
    print("-" * 50)
    
    # Login as executor public
    login_data = {'username': 'executor_public', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Executor login failed: {login_response.status_code}")
        return
    
    print("✅ Logged in as Executor Public")
    
    # Create test metadata (this should write to columns N onward)
    metadata_timestamp = datetime.now().strftime("%H%M%S")
    test_metadata = {
        'ocd_vp_number': f'OCD-TEST-{metadata_timestamp}',
        'edited_file_name': f'test_file_{metadata_timestamp}.mp4',
        'total_file_size': '1.5 GB',
        'file_count': '5',
        'detected_duration': '00:15:30',
        'language': 'English',
        'edited_year': '2024',
        'video_type': 'Talk',
        'edited_location': 'Isha Yoga Center',
        'published_platforms': 'YouTube, Instagram',
        'department_name': 'Archives',
        'component': 'Video Production',
        'content_tags': 'Sadhguru, Wisdom',
        'backup_type': 'Cloud',
        'access_level': 'Public',
        'software_show_name': 'Test Show',
        'audio_code': f'AUD-TEST-{metadata_timestamp}',
        'audio_file_name': f'test_audio_{metadata_timestamp}.wav',
        'transcription_file_name': f'test_transcript_{metadata_timestamp}.txt',
        'transcription_status': 'Completed',
        'published_date': '2024-01-15',
        'video_id': f'VID-TEST-{metadata_timestamp}',
        'social_media_title': f'Test Video {metadata_timestamp}',
        'description': f'Test description for metadata columns N+ test {metadata_timestamp}',
        'social_media_url': f'https://metadata-test-{metadata_timestamp}.com',
        'duration_category': 'Medium'
    }
    
    print(f"📝 Testing metadata update (should go to columns N onward):")
    print(f"   📊 Column N (OCD): {test_metadata['ocd_vp_number']}")
    print(f"   📊 Column O (File): {test_metadata['edited_file_name']}")
    print(f"   📊 Column P (Size): {test_metadata['total_file_size']}")
    print(f"   📊 Column Q (Count): {test_metadata['file_count']}")
    print(f"   📊 Column R (Duration): {test_metadata['detected_duration']}")
    print(f"   📊 And more through Column AM...")
    
    # Note: We need to test this through the actual metadata update workflow
    # For now, we'll create a direct test
    print(f"⚠️  Metadata update test requires actual queue item - creating test entry first")
    
    # Test 3: Cross Checker validation (should write to AQ/AR)
    print(f"\n🔍 TEST 3: CROSS CHECKER VALIDATION (Should write to columns AQ/AR)")
    print("-" * 50)
    
    # Login as cross checker
    login_data = {'username': 'crosschecker', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Cross Checker login failed: {login_response.status_code}")
        return
    
    print("✅ Logged in as Cross Checker")
    print(f"📝 Cross Checker validation should write to:")
    print(f"   📊 Column AQ (43): 'Validated Video'")
    print(f"   📊 Column AR (44): 'Validated Audio'")
    print(f"⚠️  Cross Checker test requires actual folder in cross-check queue")
    
    # Summary
    print(f"\n🎯 EXPECTED RESULTS AFTER FIXES:")
    print("="*70)
    print(f"✅ ASSIGNER DATA → Columns A-F (unchanged)")
    print(f"   A: Folder name")
    print(f"   B: Date processed")
    print(f"   C: Category")
    print(f"   D: URL")
    print(f"   E: Assigned to")
    print(f"   F: Remarks")
    
    print(f"\n🔧 METADATA DATA → Columns N-AM (FIXED from G-AF)")
    print(f"   N: OCD Number (was G)")
    print(f"   O: File Name (was H)")
    print(f"   P: Size (was I)")
    print(f"   Q: File Count (was J)")
    print(f"   R: Duration (was K)")
    print(f"   S: Language (was L)")
    print(f"   T: Year (was M)")
    print(f"   U: Video Type (was N)")
    print(f"   ... and so on through AM")
    
    print(f"\n🔍 VALIDATION DATA → Columns AQ/AR (FIXED from AI/AJ)")
    print(f"   AQ: Validated Video (was AI)")
    print(f"   AR: Validated Audio (was AJ)")
    
    print(f"\n🔍 MANUAL VERIFICATION REQUIRED:")
    print(f"1. Open Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print(f"2. Go to 'Records' sheet")
    print(f"3. Look for test entries:")
    print(f"   - 'ASSIGNER_COLUMNS_AF_{timestamp}' should be in Column A")
    print(f"   - Assigner data should be in columns A-F only")
    print(f"   - NO new data should appear in columns G-M")
    print(f"   - Metadata should start from column N (when tested)")
    print(f"   - Validation should use columns AQ/AR (when tested)")
    
    print("="*70)
    print("🎉 COLUMN MAPPING FIXES IMPLEMENTED!")
    print("✅ Metadata now starts from Column N (not G)")
    print("✅ Cross Checker validation now uses AQ/AR (not AI/AJ)")
    print("="*70)

if __name__ == "__main__":
    test_column_mapping_fixes()
