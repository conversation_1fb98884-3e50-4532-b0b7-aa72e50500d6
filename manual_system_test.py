#!/usr/bin/env python3
"""
Manual System Test - Tests system components without requiring server
"""

import os
import sys
import sqlite3
import json
import shutil
import tempfile
from datetime import datetime
from pathlib import Path

class ManualSystemTester:
    def __init__(self):
        self.test_results = []
        self.test_files_created = []
        
        # Test paths
        self.test_paths = {
            'source': r"T:\To_Process\Rough folder\restored files",
            'cross_check': r"T:\To_Process\Rough folder\Folder to be cross checked",
            'audio_tr': r"T:\To_Process\Rough folder\Audio Sent to TR",
            'audio_repo': r"T:\To_Process\Rough folder\Audios Repository",
            'to_reingest': r"T:\To_Process\Rough folder\To Reingest",
            'to_delete': r"T:\To_Process\Rough folder\To Be Deleted"
        }

    def log_test(self, test_name, status, details=""):
        """Log test results"""
        result = {
            'test_name': test_name,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")

    def test_database_functionality(self):
        """Test database operations"""
        print("\n🗄️ Testing Database Functionality...")
        
        try:
            # Test database connection
            conn = sqlite3.connect('archives.db')
            cursor = conn.cursor()
            
            # Test tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [t[0] for t in cursor.fetchall()]
            
            required_tables = ['users', 'operations_log', 'metadata_entries']
            missing_tables = [t for t in required_tables if t not in tables]
            
            if missing_tables:
                self.log_test("Database tables", "FAIL", f"Missing tables: {missing_tables}")
            else:
                self.log_test("Database tables", "PASS", f"All required tables present: {required_tables}")
            
            # Test user data
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            if user_count >= 5:
                self.log_test("User accounts", "PASS", f"Found {user_count} users")
            else:
                self.log_test("User accounts", "FAIL", f"Only {user_count} users found, expected at least 5")
            
            # Test sample user login
            cursor.execute("SELECT username, role FROM users WHERE username = 'admin'")
            admin_user = cursor.fetchone()
            
            if admin_user:
                self.log_test("Admin user", "PASS", f"Admin user exists with role: {admin_user[1]}")
            else:
                self.log_test("Admin user", "FAIL", "Admin user not found")
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_test("Database functionality", "FAIL", str(e))
            return False

    def test_file_system_operations(self):
        """Test file system operations"""
        print("\n📁 Testing File System Operations...")
        
        try:
            # Test directory creation and access
            all_dirs_ok = True
            for name, path in self.test_paths.items():
                if os.path.exists(path) and os.access(path, os.W_OK):
                    self.log_test(f"Directory access: {name}", "PASS", path)
                else:
                    self.log_test(f"Directory access: {name}", "FAIL", f"Cannot access: {path}")
                    all_dirs_ok = False
            
            # Test file operations
            test_folder = os.path.join(self.test_paths['source'], f"TEST_MANUAL_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            os.makedirs(test_folder, exist_ok=True)
            self.test_files_created.append(test_folder)
            
            # Create test files
            test_video = os.path.join(test_folder, "test_video.mp4")
            test_audio = os.path.join(test_folder, "test_audio.wav")
            test_metadata = os.path.join(test_folder, "metadata.json")
            
            # Create dummy files
            with open(test_video, 'wb') as f:
                f.write(b'FAKE_VIDEO_DATA' * 100)
            
            with open(test_audio, 'wb') as f:
                f.write(b'RIFF\x00\x00\x00\x00WAVEfmt \x00\x00\x00\x00' * 50)
            
            with open(test_metadata, 'w') as f:
                json.dump({
                    'folder_name': 'TEST_MANUAL',
                    'video_type': 'Internal Only Output',
                    'department': 'Archives',
                    'created': datetime.now().isoformat()
                }, f)
            
            self.test_files_created.extend([test_video, test_audio, test_metadata])
            
            # Test file operations
            if all([os.path.exists(f) for f in [test_video, test_audio, test_metadata]]):
                self.log_test("File creation", "PASS", f"Created test files in {test_folder}")
            else:
                self.log_test("File creation", "FAIL", "Failed to create test files")
            
            # Test file movement
            dest_folder = os.path.join(self.test_paths['cross_check'], f"MOVED_TEST_{datetime.now().strftime('%H%M%S')}")
            shutil.move(test_folder, dest_folder)
            self.test_files_created.append(dest_folder)
            
            if os.path.exists(dest_folder):
                self.log_test("File movement", "PASS", f"Successfully moved folder to {dest_folder}")
            else:
                self.log_test("File movement", "FAIL", "Failed to move test folder")
            
            return all_dirs_ok
            
        except Exception as e:
            self.log_test("File system operations", "FAIL", str(e))
            return False

    def test_template_files(self):
        """Test template files exist and are valid"""
        print("\n📄 Testing Template Files...")
        
        template_files = [
            'templates/login.html',
            'templates/dashboard.html',
            'templates/assigner.html',
            'templates/executor_public.html',
            'templates/executor_private.html',
            'templates/cross_checker.html',
            'templates/admin.html'
        ]
        
        all_templates_ok = True
        for template in template_files:
            if os.path.exists(template):
                # Check file size (should not be empty)
                size = os.path.getsize(template)
                if size > 1000:  # Reasonable size for HTML templates
                    self.log_test(f"Template: {os.path.basename(template)}", "PASS", f"Size: {size} bytes")
                else:
                    self.log_test(f"Template: {os.path.basename(template)}", "FAIL", f"Too small: {size} bytes")
                    all_templates_ok = False
            else:
                self.log_test(f"Template: {os.path.basename(template)}", "FAIL", "File not found")
                all_templates_ok = False
        
        return all_templates_ok

    def test_static_files(self):
        """Test static files exist"""
        print("\n🎨 Testing Static Files...")
        
        # Check if static directory exists
        if os.path.exists('static'):
            static_files = []
            for root, dirs, files in os.walk('static'):
                static_files.extend([os.path.join(root, f) for f in files])
            
            if static_files:
                self.log_test("Static files", "PASS", f"Found {len(static_files)} static files")
                return True
            else:
                self.log_test("Static files", "FAIL", "No static files found")
                return False
        else:
            self.log_test("Static directory", "FAIL", "Static directory not found")
            return False

    def test_application_imports(self):
        """Test application can be imported without errors"""
        print("\n🐍 Testing Application Imports...")
        
        try:
            import app
            self.log_test("App import", "PASS", "Application imported successfully")
            
            # Test Flask app object
            if hasattr(app, 'app') and app.app:
                self.log_test("Flask app object", "PASS", f"App name: {app.app.name}")
            else:
                self.log_test("Flask app object", "FAIL", "Flask app object not found")
            
            # Test route registration
            routes = [str(rule) for rule in app.app.url_map.iter_rules()]
            if len(routes) > 10:  # Should have many routes
                self.log_test("Route registration", "PASS", f"Found {len(routes)} routes")
            else:
                self.log_test("Route registration", "FAIL", f"Only {len(routes)} routes found")
            
            return True
            
        except Exception as e:
            self.log_test("Application imports", "FAIL", str(e))
            return False

    def test_google_sheets_integration(self):
        """Test Google Sheets integration setup"""
        print("\n📊 Testing Google Sheets Integration...")
        
        credentials_path = r"D:\Dashboard\Edited Backlog Project\credentials.json"
        
        if os.path.exists(credentials_path):
            try:
                with open(credentials_path, 'r') as f:
                    creds = json.load(f)
                
                required_fields = ['type', 'project_id', 'client_email']
                if all(field in creds for field in required_fields):
                    self.log_test("Google Sheets credentials", "PASS", "Credentials file valid")
                else:
                    self.log_test("Google Sheets credentials", "FAIL", "Invalid credentials format")
            except Exception as e:
                self.log_test("Google Sheets credentials", "FAIL", f"Error reading credentials: {e}")
        else:
            self.log_test("Google Sheets credentials", "FAIL", f"Credentials not found at: {credentials_path}")

    def run_manual_tests(self):
        """Run all manual tests"""
        print("🧪 Starting Manual System Testing")
        print("=" * 50)
        
        # Run all tests
        tests = [
            ("Database Functionality", self.test_database_functionality),
            ("File System Operations", self.test_file_system_operations),
            ("Template Files", self.test_template_files),
            ("Static Files", self.test_static_files),
            ("Application Imports", self.test_application_imports),
            ("Google Sheets Integration", self.test_google_sheets_integration)
        ]
        
        passed_tests = 0
        for test_name, test_func in tests:
            if test_func():
                passed_tests += 1
        
        # Calculate results
        total_tests = len(self.test_results)
        passed_individual = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_individual = len([r for r in self.test_results if r['status'] == 'FAIL'])
        
        success_rate = (passed_individual / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 50)
        print("📊 MANUAL TEST RESULTS")
        print("=" * 50)
        print(f"Test Categories: {len(tests)}")
        print(f"✅ Passed Categories: {passed_tests}")
        print(f"❌ Failed Categories: {len(tests) - passed_tests}")
        print(f"Individual Tests: {total_tests}")
        print(f"✅ Passed Individual: {passed_individual}")
        print(f"❌ Failed Individual: {failed_individual}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        # Save results
        self.save_test_results()
        
        return success_rate >= 80

    def save_test_results(self):
        """Save test results to file"""
        try:
            results_file = f"manual_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(results_file, 'w') as f:
                json.dump({
                    'summary': {
                        'total_tests': len(self.test_results),
                        'passed': len([r for r in self.test_results if r['status'] == 'PASS']),
                        'failed': len([r for r in self.test_results if r['status'] == 'FAIL']),
                        'success_rate': (len([r for r in self.test_results if r['status'] == 'PASS']) / len(self.test_results)) * 100
                    },
                    'detailed_results': self.test_results
                }, f, indent=2)
            
            print(f"\n📄 Detailed test results saved to: {results_file}")
            
        except Exception as e:
            print(f"❌ Failed to save test results: {e}")

    def cleanup_test_files(self):
        """Clean up test files"""
        print("\n🧹 Cleaning up test files...")
        
        for file_path in self.test_files_created:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                print(f"✅ Cleaned up: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"❌ Failed to clean up {file_path}: {e}")

if __name__ == "__main__":
    tester = ManualSystemTester()
    
    try:
        success = tester.run_manual_tests()
        
        if success:
            print("\n🎉 MANUAL TESTING COMPLETED SUCCESSFULLY!")
            print("✅ System components are working correctly")
        else:
            print("\n⚠️ MANUAL TESTING COMPLETED WITH ISSUES")
            print("❌ Please review failed tests before proceeding")
            
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n💥 Testing failed with error: {e}")
    finally:
        tester.cleanup_test_files()
