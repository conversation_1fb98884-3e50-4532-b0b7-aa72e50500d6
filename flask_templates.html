<!-- templates/base.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}File Management System{% endblock %}

<!-- templates/assigner.html -->
{% extends "base.html" %}

{% block title %}Assigner - File Management System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 mb-0">
            <i class="fas fa-tasks me-2"></i>File Assignment
        </h1>
        <p class="text-muted">Select files and assign them for processing</p>
    </div>
</div>

<div class="row">
    <!-- Left Panel - Folder Browser -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-folder-tree me-2"></i>Source Folders</h5>
            </div>
            <div class="card-body">
                <div class="folder-tree" id="folderTree">
                    <div class="text-center py-4">
                        <div class="loading"></div>
                        <p class="mt-2">Loading folders...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Panel - File Details and Processing Options -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-file-video me-2"></i>Files & Processing</h5>
            </div>
            <div class="card-body">
                <!-- Selected Files -->
                <div class="mb-4">
                    <h6><i class="fas fa-list me-2"></i>Selected Files</h6>
                    <div id="fileList" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                        <p class="text-muted mb-0">Select a folder to view files</p>
                    </div>
                </div>

                <!-- URL Input for Social Media -->
                <div id="urlSection" class="mb-4" style="display: none;">
                    <h6><i class="fas fa-link me-2"></i>Social Media URL</h6>
                    <input type="url" class="form-control" id="urlInput" placeholder="Enter social media URL...">
                </div>

                <!-- Processing Options -->
                <div class="mb-4">
                    <h6><i class="fas fa-cog me-2"></i>Processing Options</h6>
                    <div class="row">
                        {% for option in processing_options %}
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-primary btn-sm w-100 processing-option" 
                                    data-option="{{ option }}"
                                    data-social="{{ 'true' if option in social_media_options else 'false' }}">
                                <i class="fas fa-{{ 'share-alt' if 'social' in option.lower() else 'video' if 'video' in option.lower() else 'file' }} me-1"></i>
                                {{ option }}
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-lg" id="processBtn" disabled>
                        <i class="fas fa-play me-2"></i>Process Selected Files
                    </button>
                    <button class="btn btn-info" id="previewBtn" disabled>
                        <i</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --light-color: #ecf0f1;
            --dark-color: #34495e;
        }

        body {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .sidebar {
            background: linear-gradient(180deg, var(--primary-color), var(--dark-color));
            min-height: calc(100vh - 56px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--secondary-color), var(--info-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: bold;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border: none;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending { background-color: var(--warning-color); color: white; }
        .status-completed { background-color: var(--success-color); color: white; }
        .status-rejected { background-color: var(--danger-color); color: white; }
        .status-assigned { background-color: var(--info-color); color: white; }

        .file-item {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-item:hover {
            border-color: var(--secondary-color);
            background-color: rgba(52, 152, 219, 0.05);
        }

        .file-item.selected {
            border-color: var(--success-color);
            background-color: rgba(39, 174, 96, 0.1);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .stats-card {
            background: linear-gradient(135deg, var(--secondary-color), var(--info-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .folder-tree {
            max-height: 500px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
        }

        .folder-item {
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .folder-item:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }

        .folder-item.selected {
            background-color: var(--secondary-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-folder-open me-2"></i>File Management System
            </a>
            
            {% if session.user_id %}
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }} ({{ session.role }})
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
            {% endif %}
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if session.user_id %}
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <nav class="nav flex-column py-3">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        
                        {% if session.role == 'Assigner' %}
                        <a class="nav-link" href="{{ url_for('assigner_interface') }}">
                            <i class="fas fa-tasks me-2"></i>Assign Files
                        </a>
                        {% endif %}
                        
                        {% if session.role == 'Cross Checker' %}
                        <a class="nav-link" href="{{ url_for('cross_checker_interface') }}">
                            <i class="fas fa-check-double me-2"></i>Cross Check
                        </a>
                        {% endif %}
                        
                        {% if session.role == 'Executor Public' %}
                        <a class="nav-link" href="{{ url_for('executor_public_interface') }}">
                            <i class="fas fa-cog me-2"></i>Execute Tasks
                        </a>
                        {% endif %}
                        
                        {% if session.role == 'Executor Private' %}
                        <a class="nav-link" href="{{ url_for('executor_private_interface') }}">
                            <i class="fas fa-lock me-2"></i>Private Tasks
                        </a>
                        {% endif %}
                        
                        {% if session.role == 'Main Admin' %}
                        <a class="nav-link" href="{{ url_for('admin_interface') }}">
                            <i class="fas fa-users-cog me-2"></i>Admin Panel
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10">
                <div class="main-content">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    {% block content %}{% endblock %}
                </div>
            </div>
            {% else %}
            <!-- Full width for login page -->
            <div class="col-12">
                {% block login_content %}{% endblock %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js"></script>
    
    <script>
        // Global utility functions
        function showAlert(message, type = 'info') {
            const alertDiv = $(`
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            
            $('.main-content').prepend(alertDiv);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.alert('close');
            }, 5000);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleString();
        }

        // AJAX error handler
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            console.error('AJAX Error:', xhr.responseText);
            showAlert('An error occurred. Please try again.', 'danger');
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>

<!-- templates/login.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - File Management System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            max-width: 400px;
            margin: 0 auto;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .login-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
        }

        .login-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .login-body {
            padding: 40px;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 10;
        }

        .form-control.with-icon {
            padding-left: 45px;
        }

        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
        }

        .demo-credentials {
            background: rgba(52, 152, 219, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
        }

        .demo-credentials h6 {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .demo-credentials .credential-item {
            margin: 5px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <i class="fas fa-folder-open fa-2x mb-3"></i>
                    <h1>File Management</h1>
                    <p>Secure Access Portal</p>
                </div>
                
                <div class="login-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                                    {{ message }}
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST">
                        <div class="input-group">
                            <i class="fas fa-user input-icon"></i>
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control with-icon" id="username" name="username" required autocomplete="username">
                        </div>

                        <div class="input-group">
                            <i class="fas fa-lock input-icon"></i>
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control with-icon" id="password" name="password" required autocomplete="current-password">
                        </div>

                        <button type="submit" class="btn btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </button>
                    </form>

                    <div class="demo-credentials">
                        <h6><i class="fas fa-info-circle me-2"></i>Demo Credentials:</h6>
                        <div class="credential-item"><strong>Assigner:</strong> assigner / assign123</div>
                        <div class="credential-item"><strong>Cross Checker:</strong> crosschecker / check123</div>
                        <div class="credential-item"><strong>Executor Public:</strong> executor_public / exec123</div>
                        <div class="credential-item"><strong>Executor Private:</strong> executor_private / execpriv123</div>
                        <div class="credential-item"><strong>Main Admin:</strong> admin / admin123</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<!-- templates/dashboard.html -->
{% extends "base.html" %}

{% block title %}Dashboard - File Management System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 mb-0">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </h1>
        <p class="text-muted">Welcome back, {{ session.role }}!</p>
    </div>
</div>

{% if session.role == 'Main Admin' %}
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_files }}</div>
            <div>Total Files</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
            <div class="stats-number">{{ stats.completed_files }}</div>
            <div>Completed</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
            <div class="stats-number">{{ stats.pending_files }}</div>
            <div>Pending</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
            <div class="stats-number">{{ stats.total_users }}</div>
            <div>Active Users</div>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h5>
            </div>
            <div class="card-body">
                {% if recent_operations %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Operation</th>
                                <th>File</th>
                                <th>User</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for operation in recent_operations %}
                            <tr>
                                <td>
                                    <i class="fas fa-{{ 'upload' if operation.operation_type == 'assigned' else 'edit' if operation.operation_type == 'renamed' else 'check' }} me-2"></i>
                                    {{ operation.operation_type.title() }}
                                </td>
                                <td>
                                    <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ operation.file_path }}">
                                        {{ operation.file_path.split('/')[-1] if operation.file_path else 'N/A' }}
                                    </span>
                                </td>
                                <td>{{ operation.username or 'System' }}</td>
                                <td>
                                    <span class="status-badge status-{{ operation.status or 'pending' }}">
                                        {{ (operation.status or 'pending').title() }}
                                    </span>
                                </td>
                                <td>{{ formatDate(operation.created_at) if operation.created_at else 'N/A' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No recent activity to display.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if session.role == 'Assigner' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('assigner_interface') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-upload me-2"></i>Assign New Files
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if session.role == 'Cross Checker' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('cross_checker_interface') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-check-double me-2"></i>Review Files
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if session.role in ['Executor Public', 'Executor Private'] %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('executor_public_interface') if session.role == 'Executor Public' else url_for('executor_private_interface') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-cog me-2"></i>Process Files
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if session.role == 'Main Admin' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('admin_interface') }}" class="btn btn-danger btn-lg w-100">
                            <i class="fas fa-users-cog me-2"></i>Manage Users
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString();
    }

    // Format dates in the table
    $(document).ready(function() {
        $('td').each(function() {
            const text = $(this).text();
            if (text.match(/^\d{4}-\d{2}-\d{2}/)) {
                $(this).text(formatDate(text));
            }
        });
    });
</script>
{% endblock %}