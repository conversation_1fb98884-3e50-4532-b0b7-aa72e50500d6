#!/usr/bin/env python3
"""
COMPREHENSIVE SYSTEM INVESTIGATION
Test all implemented fixes and identify any remaining issues
"""

import requests
import json
import sqlite3
import os
import time

def test_database_status():
    """Check database status and content"""
    print("🗄️ DATABASE STATUS INVESTIGATION")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('archives_management.db')
        cursor = conn.cursor()
        
        # Check users
        cursor.execute('SELECT username, role, is_active FROM users WHERE is_active = 1')
        users = cursor.fetchall()
        print(f"👥 Active Users: {len(users)}")
        for user in users:
            print(f"   - {user[0]} ({user[1]})")
        
        # Check queue items
        cursor.execute('SELECT COUNT(*) FROM queue_items')
        total_queue = cursor.fetchone()[0]
        print(f"\n📋 Queue Items: {total_queue}")
        
        if total_queue > 0:
            # Check by status
            cursor.execute('SELECT status, COUNT(*) FROM queue_items GROUP BY status')
            status_counts = cursor.fetchall()
            print("   Status breakdown:")
            for status, count in status_counts:
                print(f"     {status}: {count}")
            
            # Check by assignment
            cursor.execute('SELECT assign_to, COUNT(*) FROM queue_items GROUP BY assign_to')
            assign_counts = cursor.fetchall()
            print("   Assignment breakdown:")
            for assign_to, count in assign_counts:
                print(f"     {assign_to}: {count}")
        
        # Check file metadata
        cursor.execute('SELECT COUNT(*) FROM file_metadata')
        metadata_count = cursor.fetchone()[0]
        print(f"\n📊 File Metadata Records: {metadata_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_flask_server():
    """Test Flask server basic functionality"""
    print("\n🌐 FLASK SERVER INVESTIGATION")
    print("=" * 60)
    
    try:
        # Test basic connectivity
        response = requests.get('http://127.0.0.1:5001/login', timeout=5)
        print(f"✅ Server Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Flask server is running and responding")
            return True
        else:
            print(f"❌ Server returned status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Server connection error: {e}")
        return False

def test_user_authentication():
    """Test user authentication for all roles"""
    print("\n🔐 USER AUTHENTICATION INVESTIGATION")
    print("=" * 60)
    
    test_users = [
        ('assigner', 'Shiva@123', 'Assigner'),
        ('crosschecker', 'Shiva@123', 'Cross Checker'),
        ('executor_public', 'Shiva@123', 'Executor Public'),
        ('executor_private', 'Shiva@123', 'Executor Private'),
        ('editor', 'Shiva@123', 'Editor'),
        ('admin', 'Shiva@123', 'Main Admin')
    ]
    
    auth_results = {}
    
    for username, password, role in test_users:
        try:
            session = requests.Session()
            login_data = {'username': username, 'password': password}
            response = session.post('http://127.0.0.1:5001/login', data=login_data)
            
            if response.status_code in [200, 302]:
                print(f"✅ {role}: Login successful")
                auth_results[role] = True
            else:
                print(f"❌ {role}: Login failed ({response.status_code})")
                auth_results[role] = False
                
        except Exception as e:
            print(f"❌ {role}: Login error - {e}")
            auth_results[role] = False
    
    return auth_results

def test_assigner_interface():
    """Test Assigner interface and functionality"""
    print("\n📋 ASSIGNER INTERFACE INVESTIGATION")
    print("=" * 60)
    
    try:
        session = requests.Session()
        login_data = {'username': 'assigner', 'password': 'Shiva@123'}
        login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
        
        if login_response.status_code not in [200, 302]:
            print("❌ Assigner login failed")
            return False
        
        # Test assigner interface access
        assigner_response = session.get('http://127.0.0.1:5001/assigner-interface')
        print(f"📄 Assigner Interface: {assigner_response.status_code}")
        
        if assigner_response.status_code == 200:
            # Check for updated labels
            if 'Archives Assignment Console' in assigner_response.text:
                print("✅ Sidebar label updated correctly")
            else:
                print("❌ Sidebar label not updated")
            
            # Test folder API
            folders_response = session.get('http://127.0.0.1:5001/api/get-folders')
            if folders_response.status_code == 200:
                folders_data = folders_response.json()
                if folders_data.get('success'):
                    folders = folders_data.get('folders', [])
                    print(f"✅ Folder API: {len(folders)} folders found")
                else:
                    print(f"❌ Folder API error: {folders_data.get('error')}")
            
            # Test queue status
            queue_response = session.get('http://127.0.0.1:5001/api/queue-status')
            if queue_response.status_code == 200:
                queue_data = queue_response.json()
                if queue_data.get('success'):
                    print(f"✅ Queue Status API working")
                    print(f"   Queued: {queue_data.get('queued', 0)}")
                    print(f"   Completed: {queue_data.get('completed', 0)}")
                else:
                    print(f"❌ Queue Status error: {queue_data.get('error')}")
            
            return True
        else:
            print(f"❌ Assigner interface access failed: {assigner_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Assigner test error: {e}")
        return False

def test_executor_interfaces():
    """Test Executor Public and Private interfaces"""
    print("\n🎬 EXECUTOR INTERFACES INVESTIGATION")
    print("=" * 60)
    
    executor_results = {}
    
    # Test Executor Public
    try:
        session = requests.Session()
        login_data = {'username': 'executor_public', 'password': 'Shiva@123'}
        login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
        
        if login_response.status_code in [200, 302]:
            # Test dashboard access
            dashboard_response = session.get('http://127.0.0.1:5001/executive-public')
            print(f"📄 Executor Public Dashboard: {dashboard_response.status_code}")
            
            if dashboard_response.status_code == 200:
                # Test queue API
                queue_response = session.get('http://127.0.0.1:5001/api/executive-public/queue')
                if queue_response.status_code == 200:
                    queue_data = queue_response.json()
                    if queue_data.get('success'):
                        files = queue_data.get('files', [])
                        print(f"✅ Executor Public Queue: {len(files)} files")
                        executor_results['public_queue'] = len(files)
                    else:
                        print(f"❌ Executor Public Queue error: {queue_data.get('error')}")
                        executor_results['public_queue'] = 0
                else:
                    print(f"❌ Executor Public Queue API failed: {queue_response.status_code}")
                    executor_results['public_queue'] = 0
                
                executor_results['public_dashboard'] = True
            else:
                print(f"❌ Executor Public dashboard failed: {dashboard_response.status_code}")
                executor_results['public_dashboard'] = False
        else:
            print(f"❌ Executor Public login failed: {login_response.status_code}")
            executor_results['public_dashboard'] = False
            
    except Exception as e:
        print(f"❌ Executor Public test error: {e}")
        executor_results['public_dashboard'] = False
    
    # Test Executor Private
    try:
        session = requests.Session()
        login_data = {'username': 'executor_private', 'password': 'Shiva@123'}
        login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
        
        if login_response.status_code in [200, 302]:
            # Test dashboard access
            dashboard_response = session.get('http://127.0.0.1:5001/executive-private')
            print(f"📄 Executor Private Dashboard: {dashboard_response.status_code}")
            
            if dashboard_response.status_code == 200:
                # Test queue API
                queue_response = session.get('http://127.0.0.1:5001/api/executive-private/queue')
                if queue_response.status_code == 200:
                    queue_data = queue_response.json()
                    if queue_data.get('success'):
                        files = queue_data.get('files', [])
                        print(f"✅ Executor Private Queue: {len(files)} files")
                        executor_results['private_queue'] = len(files)
                    else:
                        print(f"❌ Executor Private Queue error: {queue_data.get('error')}")
                        executor_results['private_queue'] = 0
                else:
                    print(f"❌ Executor Private Queue API failed: {queue_response.status_code}")
                    executor_results['private_queue'] = 0
                
                executor_results['private_dashboard'] = True
            else:
                print(f"❌ Executor Private dashboard failed: {dashboard_response.status_code}")
                executor_results['private_dashboard'] = False
        else:
            print(f"❌ Executor Private login failed: {login_response.status_code}")
            executor_results['private_dashboard'] = False
            
    except Exception as e:
        print(f"❌ Executor Private test error: {e}")
        executor_results['private_dashboard'] = False
    
    return executor_results

def test_cross_checker_interface():
    """Test Cross Checker interface"""
    print("\n✅ CROSS CHECKER INTERFACE INVESTIGATION")
    print("=" * 60)
    
    try:
        session = requests.Session()
        login_data = {'username': 'crosschecker', 'password': 'Shiva@123'}
        login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
        
        if login_response.status_code not in [200, 302]:
            print("❌ Cross Checker login failed")
            return False
        
        # Test cross checker interface
        cc_response = session.get('http://127.0.0.1:5001/cross-checker')
        print(f"📄 Cross Checker Interface: {cc_response.status_code}")
        
        if cc_response.status_code == 200:
            # Check for updated interface
            if 'pending_folders' in cc_response.text:
                print("✅ Updated Cross Checker interface detected")
            else:
                print("⚠️ Cross Checker interface may need updates")
            
            # Check crosscheck directory
            crosscheck_path = r"T:\To_Process\Rough folder\Folder to be cross checked"
            if os.path.exists(crosscheck_path):
                folders = [f for f in os.listdir(crosscheck_path) if os.path.isdir(os.path.join(crosscheck_path, f))]
                print(f"✅ Cross Check Directory: {len(folders)} folders pending")
            else:
                print("⚠️ Cross Check Directory not found")
            
            return True
        else:
            print(f"❌ Cross Checker interface failed: {cc_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cross Checker test error: {e}")
        return False

def check_file_system_structure():
    """Check file system structure and paths"""
    print("\n📁 FILE SYSTEM STRUCTURE INVESTIGATION")
    print("=" * 60)
    
    base_path = r"T:\To_Process\Rough folder"
    
    required_paths = [
        "restored files",
        "Internal video with stems",
        "Internal video without stems", 
        "Miscellaneous",
        "Internal with Project File",
        "Internal Only Output",
        "Social Media Only Output",
        "Internal with Project Files",
        "Private one video",
        "Social media outputs with stems",
        "Social media outputs without stems",
        "Tamil files",
        "To Be Deleted",
        "Folder to be cross checked",
        "To be Ingested\\Videos",
        "To be Ingested\\Audios"
    ]
    
    structure_status = {}
    
    for path in required_paths:
        full_path = os.path.join(base_path, path)
        exists = os.path.exists(full_path)
        structure_status[path] = exists
        
        if exists:
            if os.path.isdir(full_path):
                folder_count = len([f for f in os.listdir(full_path) if os.path.isdir(os.path.join(full_path, f))])
                print(f"✅ {path}: {folder_count} folders")
            else:
                print(f"✅ {path}: exists (file)")
        else:
            print(f"❌ {path}: missing")
    
    return structure_status

def main():
    """Run comprehensive system investigation"""
    print("🔍 COMPREHENSIVE ARCHIVES MANAGEMENT SYSTEM INVESTIGATION")
    print("=" * 80)
    print(f"🕐 Investigation Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Test all components
    db_status = test_database_status()
    server_status = test_flask_server()
    auth_results = test_user_authentication()
    assigner_status = test_assigner_interface()
    executor_results = test_executor_interfaces()
    cc_status = test_cross_checker_interface()
    fs_structure = check_file_system_structure()
    
    # Generate summary report
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE INVESTIGATION SUMMARY")
    print("=" * 80)
    
    print(f"🗄️ Database Status: {'✅ Working' if db_status else '❌ Issues'}")
    print(f"🌐 Flask Server: {'✅ Running' if server_status else '❌ Down'}")
    print(f"📋 Assigner Interface: {'✅ Working' if assigner_status else '❌ Issues'}")
    print(f"✅ Cross Checker: {'✅ Working' if cc_status else '❌ Issues'}")
    
    print(f"\n👥 User Authentication:")
    for role, status in auth_results.items():
        print(f"   {role}: {'✅' if status else '❌'}")
    
    print(f"\n🎬 Executor Interfaces:")
    print(f"   Public Dashboard: {'✅' if executor_results.get('public_dashboard') else '❌'}")
    print(f"   Public Queue: {executor_results.get('public_queue', 0)} files")
    print(f"   Private Dashboard: {'✅' if executor_results.get('private_dashboard') else '❌'}")
    print(f"   Private Queue: {executor_results.get('private_queue', 0)} files")
    
    print(f"\n📁 File System Structure:")
    working_paths = sum(1 for status in fs_structure.values() if status)
    total_paths = len(fs_structure)
    print(f"   {working_paths}/{total_paths} required paths exist")
    
    # Overall system health
    critical_components = [
        db_status,
        server_status,
        auth_results.get('Assigner', False),
        auth_results.get('Executor Public', False),
        auth_results.get('Cross Checker', False)
    ]
    
    system_health = sum(critical_components) / len(critical_components) * 100
    
    print(f"\n🎯 OVERALL SYSTEM HEALTH: {system_health:.1f}%")
    
    if system_health >= 90:
        print("🎉 SYSTEM STATUS: EXCELLENT - Ready for production!")
    elif system_health >= 75:
        print("✅ SYSTEM STATUS: GOOD - Minor issues to address")
    elif system_health >= 50:
        print("⚠️ SYSTEM STATUS: FAIR - Several issues need attention")
    else:
        print("🚨 SYSTEM STATUS: POOR - Major issues require immediate attention")
    
    print("\n🌐 ACCESS URLS:")
    print("   Login: http://127.0.0.1:5001/login")
    print("   Assigner: http://127.0.0.1:5001/assigner-interface")
    print("   Executor Public: http://127.0.0.1:5001/executive-public")
    print("   Executor Private: http://127.0.0.1:5001/executive-private")
    print("   Cross Checker: http://127.0.0.1:5001/cross-checker")
    print("   Admin Dashboard: http://127.0.0.1:5001/admin")

if __name__ == "__main__":
    main()
