#!/usr/bin/env python3
"""
Test the Executive Public API endpoint directly
"""

import requests
import json

def test_executive_public_api():
    print("🔧 TESTING EXECUTIVE PUBLIC API ENDPOINT")
    print("=" * 50)
    
    # Create a session
    session = requests.Session()
    
    # Step 1: Login as Executor Public
    print("1. 🔐 Logging in as Executor Public...")
    login_data = {
        'username': 'executor_public',
        'password': 'Shiva@123'
    }
    
    login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
    print(f"   Login status: {login_response.status_code}")
    
    if login_response.status_code in [200, 302]:
        print("   ✅ Login successful!")
        
        # Step 2: Test the API endpoint
        print("\n2. 📡 Testing API endpoint...")
        api_response = session.get('http://127.0.0.1:5001/api/executive-public/queue')
        print(f"   API status: {api_response.status_code}")
        
        if api_response.status_code == 200:
            try:
                data = api_response.json()
                print(f"   ✅ API response received!")
                print(f"   📊 Success: {data.get('success')}")
                
                if data.get('success'):
                    files = data.get('files', [])
                    total_count = data.get('total_count', 0)
                    
                    print(f"   📁 Total files in queue: {total_count}")
                    
                    if files:
                        print("   📋 Files in queue:")
                        for i, file in enumerate(files, 1):
                            print(f"      {i}. {file['folder_name']}")
                            print(f"         📂 Category: {file.get('category', 'Unknown')}")
                            print(f"         📅 Assigned: {file.get('assigned_date', 'Unknown')}")
                            print(f"         📊 Files: {file.get('file_count', 0)}")
                            print(f"         💾 Size: {file.get('total_size', 'Unknown')}")
                            print()
                    else:
                        print("   📭 No files found in queue")
                        
                else:
                    print(f"   ❌ API Error: {data.get('error')}")
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON decode error: {e}")
                print(f"   📄 Raw response: {api_response.text[:200]}...")
                
        else:
            print(f"   ❌ API request failed: {api_response.status_code}")
            print(f"   📄 Response: {api_response.text[:200]}...")
            
    else:
        print("   ❌ Login failed!")
        print(f"   📄 Response: {login_response.text[:200]}...")
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS:")
    print("If you see files listed above, the API is working correctly!")
    print("If not, there might be an issue with file paths or database queries.")
    print("=" * 50)

if __name__ == "__main__":
    test_executive_public_api()
