{"summary": {"total_tests": 22, "passed": 18, "failed": 4, "success_rate": 81.81818181818183}, "detailed_results": [{"test_name": "Database tables", "status": "FAIL", "details": "Missing tables: ['users', 'operations_log', 'metadata_entries']", "timestamp": "2025-06-11T12:16:36.352513"}, {"test_name": "Database functionality", "status": "FAIL", "details": "no such table: users", "timestamp": "2025-06-11T12:16:36.352727"}, {"test_name": "Directory access: source", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\restored files", "timestamp": "2025-06-11T12:16:36.353467"}, {"test_name": "Directory access: cross_check", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\Folder to be cross checked", "timestamp": "2025-06-11T12:16:36.356062"}, {"test_name": "Directory access: audio_tr", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\Audio Sent to TR", "timestamp": "2025-06-11T12:16:36.358301"}, {"test_name": "Directory access: audio_repo", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\Audios Repository", "timestamp": "2025-06-11T12:16:36.360727"}, {"test_name": "Directory access: to_reingest", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\To Reingest", "timestamp": "2025-06-11T12:16:36.363989"}, {"test_name": "Directory access: to_delete", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\To Be Deleted", "timestamp": "2025-06-11T12:16:36.365264"}, {"test_name": "File creation", "status": "PASS", "details": "Created test files in T:\\To_Process\\Rough folder\\restored files\\TEST_MANUAL_20250611_121636", "timestamp": "2025-06-11T12:16:36.391765"}, {"test_name": "File movement", "status": "PASS", "details": "Successfully moved folder to T:\\To_Process\\Rough folder\\Folder to be cross checked\\MOVED_TEST_121636", "timestamp": "2025-06-11T12:16:36.402809"}, {"test_name": "Template: login.html", "status": "PASS", "details": "Size: 8439 bytes", "timestamp": "2025-06-11T12:16:36.406302"}, {"test_name": "Template: dashboard.html", "status": "PASS", "details": "Size: 9272 bytes", "timestamp": "2025-06-11T12:16:36.407448"}, {"test_name": "Template: assigner.html", "status": "FAIL", "details": "File not found", "timestamp": "2025-06-11T12:16:36.407868"}, {"test_name": "Template: executor_public.html", "status": "PASS", "details": "Size: 122078 bytes", "timestamp": "2025-06-11T12:16:36.408468"}, {"test_name": "Template: executor_private.html", "status": "PASS", "details": "Size: 84144 bytes", "timestamp": "2025-06-11T12:16:36.410523"}, {"test_name": "Template: cross_checker.html", "status": "PASS", "details": "Size: 62477 bytes", "timestamp": "2025-06-11T12:16:36.411260"}, {"test_name": "Template: admin.html", "status": "PASS", "details": "Size: 13442 bytes", "timestamp": "2025-06-11T12:16:36.411817"}, {"test_name": "Static directory", "status": "FAIL", "details": "Static directory not found", "timestamp": "2025-06-11T12:16:36.412348"}, {"test_name": "App import", "status": "PASS", "details": "Application imported successfully", "timestamp": "2025-06-11T12:16:39.975118"}, {"test_name": "Flask app object", "status": "PASS", "details": "App name: app", "timestamp": "2025-06-11T12:16:39.975437"}, {"test_name": "Route registration", "status": "PASS", "details": "Found 78 routes", "timestamp": "2025-06-11T12:16:39.975831"}, {"test_name": "Google Sheets credentials", "status": "PASS", "details": "Credentials file valid", "timestamp": "2025-06-11T12:16:39.977422"}]}