#!/usr/bin/env python3
"""
COMPREHENSIVE ARCHIVES ASSIGNMENT CONSOLE TESTING
Test all categories and complete end-to-end workflow
"""

import requests
import json
import os
from datetime import datetime

def test_archives_assignment_console():
    print("🔥 COMPREHENSIVE ARCHIVES ASSIGNMENT CONSOLE TESTING")
    print("="*80)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # All processing categories to test
    PROCESSING_CATEGORIES = [
        "Internal video with stems",
        "Internal video without stems", 
        "Miscellaneous",
        "Internal with Project File",
        "Internal Only Output",
        "Social Media Only Output",
        "Internal with Project Files",
        "Private one video",
        "Social media outputs with stems",
        "Social media outputs without stems",
        "Tamil files",
        "To Be Deleted"
    ]
    
    # Login as Assigner
    print("\n🔐 LOGGING IN AS ASSIGNER")
    print("-" * 60)
    login_data = {'username': 'assigner', 'password': '<PERSON>@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    print("✅ Assigner login successful")
    
    # Test 1: Check folder tree loading
    print("\n📂 TESTING FOLDER TREE LOADING")
    print("-" * 60)
    
    folders_response = session.get(f"{base_url}/api/get-folders")
    if folders_response.status_code == 200:
        folders_data = folders_response.json()
        if folders_data.get('success'):
            folders = folders_data.get('folders', [])
            print(f"✅ Folder tree loaded: {len(folders)} folders found")
            
            # Check if we have test folders
            test_folders = []
            for folder in folders[:3]:  # Test with first 3 folders
                if folder.get('type') == 'folder':
                    test_folders.append(folder['path'])
            
            print(f"✅ Test folders identified: {len(test_folders)}")
            for i, folder in enumerate(test_folders):
                print(f"   {i+1}. {folder}")
                
        else:
            print(f"❌ Folder tree API error: {folders_data.get('error')}")
            return
    else:
        print(f"❌ Folder tree API failed: {folders_response.status_code}")
        return
    
    # Test 2: Test each category with folder assignment
    print(f"\n🎯 TESTING ALL {len(PROCESSING_CATEGORIES)} CATEGORIES")
    print("-" * 60)
    
    working_categories = []
    failing_categories = []
    
    for category in PROCESSING_CATEGORIES:
        print(f"\n🔍 Testing Category: {category}")
        print("-" * 40)
        
        # Prepare test data
        test_data = {
            'folder_paths': test_folders[:1] if test_folders else [],  # Test with 1 folder
            'category': category,
            'assign_to': 'Executor Public' if category != 'Private one video' else 'Executor Private',
            'video_ids': 'TEST001',
            'url': 'https://test.com' if 'social media' in category.lower() else '',
            'remarks': f'Test assignment for {category}',
            'operation_type': 'copy'  # Use copy to avoid moving actual files
        }
        
        # Test folder assignment
        try:
            assign_response = session.post(
                f"{base_url}/api/add-to-queue",
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if assign_response.status_code == 200:
                assign_data = assign_response.json()
                if assign_data.get('success'):
                    added = assign_data.get('added', 0)
                    failed = assign_data.get('failed', 0)
                    
                    if added > 0 and failed == 0:
                        print(f"✅ {category}: SUCCESS - {added} folders added")
                        working_categories.append(category)
                    else:
                        failed_items = assign_data.get('failed_items', [])
                        error_details = []
                        for item in failed_items:
                            error_details.append(f"{item.get('folder', 'Unknown')}: {item.get('error', 'Unknown error')}")
                        
                        print(f"❌ {category}: FAILED - {failed} failures")
                        print(f"   Errors: {'; '.join(error_details)}")
                        failing_categories.append({
                            'category': category,
                            'errors': error_details
                        })
                else:
                    error_msg = assign_data.get('error', 'Unknown error')
                    print(f"❌ {category}: API ERROR - {error_msg}")
                    failing_categories.append({
                        'category': category,
                        'errors': [error_msg]
                    })
            else:
                print(f"❌ {category}: HTTP ERROR - {assign_response.status_code}")
                failing_categories.append({
                    'category': category,
                    'errors': [f'HTTP {assign_response.status_code}']
                })
                
        except Exception as e:
            print(f"❌ {category}: EXCEPTION - {str(e)}")
            failing_categories.append({
                'category': category,
                'errors': [str(e)]
            })
    
    # Test 3: Check queue status
    print(f"\n📋 TESTING QUEUE STATUS")
    print("-" * 60)
    
    queue_response = session.get(f"{base_url}/api/queue-status")
    if queue_response.status_code == 200:
        queue_data = queue_response.json()
        if queue_data.get('success'):
            queue_status = queue_data.get('queue_status', {})
            items = queue_status.get('items', [])
            print(f"✅ Queue status loaded: {len(items)} items in queue")
            
            # Show queue breakdown
            status_counts = {}
            for item in items:
                status = item.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            for status, count in status_counts.items():
                print(f"   {status}: {count} items")
        else:
            print(f"❌ Queue status error: {queue_data.get('error')}")
    else:
        print(f"❌ Queue status failed: {queue_response.status_code}")
    
    # Test 4: Test Executor interfaces
    print(f"\n👥 TESTING EXECUTOR INTERFACES")
    print("-" * 60)
    
    # Test Executor Public
    session.get(f"{base_url}/logout")
    login_data = {'username': 'executor_public', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code in [200, 302]:
        print("✅ Executor Public login successful")
        
        # Test interface access
        interface_response = session.get(f"{base_url}/executor-public")
        if interface_response.status_code == 200:
            print("✅ Executor Public interface accessible")
        else:
            print(f"❌ Executor Public interface failed: {interface_response.status_code}")
    else:
        print(f"❌ Executor Public login failed: {login_response.status_code}")
    
    # Test Executive Private
    session.get(f"{base_url}/logout")
    login_data = {'username': 'executor_private', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code in [200, 302]:
        print("✅ Executive Private login successful")
        
        # Test interface access
        interface_response = session.get(f"{base_url}/executive-private")
        if interface_response.status_code == 200:
            print("✅ Executive Private interface accessible")
        else:
            print(f"❌ Executive Private interface failed: {interface_response.status_code}")
    else:
        print(f"❌ Executive Private login failed: {login_response.status_code}")
    
    # Test Cross Checker
    session.get(f"{base_url}/logout")
    login_data = {'username': 'crosschecker', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code in [200, 302]:
        print("✅ Cross Checker login successful")
        
        # Test interface access
        interface_response = session.get(f"{base_url}/cross-checker")
        if interface_response.status_code == 200:
            print("✅ Cross Checker interface accessible")
        else:
            print(f"❌ Cross Checker interface failed: {interface_response.status_code}")
    else:
        print(f"❌ Cross Checker login failed: {login_response.status_code}")
    
    # Final Summary
    print(f"\n🎯 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("="*80)
    
    print(f"\n✅ WORKING CATEGORIES ({len(working_categories)}/{len(PROCESSING_CATEGORIES)}):")
    for category in working_categories:
        print(f"   ✅ {category}")
    
    print(f"\n❌ FAILING CATEGORIES ({len(failing_categories)}/{len(PROCESSING_CATEGORIES)}):")
    for item in failing_categories:
        category = item['category']
        errors = item['errors']
        print(f"   ❌ {category}")
        for error in errors:
            print(f"      → {error}")
    
    print(f"\n📊 CATEGORY TEST STATISTICS:")
    print(f"   Total Categories: {len(PROCESSING_CATEGORIES)}")
    print(f"   Working: {len(working_categories)} ({len(working_categories)/len(PROCESSING_CATEGORIES)*100:.1f}%)")
    print(f"   Failing: {len(failing_categories)} ({len(failing_categories)/len(PROCESSING_CATEGORIES)*100:.1f}%)")
    
    print(f"\n🔧 RECOMMENDATIONS:")
    if failing_categories:
        print("   1. Check folder paths and permissions")
        print("   2. Verify source folder exists and is accessible")
        print("   3. Check database connectivity")
        print("   4. Validate category configurations")
        print("   5. Review path fixing logic")
    else:
        print("   ✅ All categories working correctly!")
    
    print(f"\n🌐 BROWSER VERIFICATION:")
    print("   Archives Assignment Console: http://127.0.0.1:5001/professional-assigner")
    print("   Executor Public: http://127.0.0.1:5001/executor-public")
    print("   Executive Private: http://127.0.0.1:5001/executive-private")
    print("   Cross Checker: http://127.0.0.1:5001/cross-checker")
    
    print("\n" + "="*80)
    print("🎉 COMPREHENSIVE ARCHIVES ASSIGNMENT CONSOLE TESTING COMPLETE!")
    print("="*80)

if __name__ == "__main__":
    test_archives_assignment_console()
