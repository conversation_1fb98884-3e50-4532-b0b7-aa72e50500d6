#!/usr/bin/env python3
"""
Test the Executive Public interface to verify files are showing up
"""

import requests
import sqlite3
import json

def test_executive_interface():
    print("🎬 TESTING EXECUTIVE PUBLIC INTERFACE")
    print("=" * 60)
    
    # Step 1: Check database directly
    print("1. 💾 CHECKING DATABASE QUEUE ITEMS...")
    try:
        conn = sqlite3.connect('archives_management.db')
        cursor = conn.cursor()
        
        # Check total queue items
        cursor.execute('SELECT COUNT(*) FROM queue_items WHERE assign_to = "Executor Public" AND status = "completed"')
        count = cursor.fetchone()[0]
        print(f"   📊 Executor Public completed items: {count}")
        
        if count > 0:
            cursor.execute('''
                SELECT folder_name, category, video_ids, url, remarks, processed_at
                FROM queue_items 
                WHERE assign_to = "Executor Public" 
                AND status = "completed" 
                AND category != "Private one video"
                ORDER BY processed_at DESC
            ''')
            items = cursor.fetchall()
            print(f"   📋 Items that should appear in Executive Public:")
            for i, item in enumerate(items, 1):
                print(f"      {i}. {item[0]}")
                print(f"         📂 Category: {item[1]}")
                print(f"         🎬 Video IDs: {item[2]}")
                print(f"         🔗 URL: {item[3]}")
                print(f"         📝 Remarks: {item[4]}")
                print(f"         📅 Processed: {item[5]}")
                print()
        
        conn.close()
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # Step 2: Test login and page access
    print("2. 🔐 TESTING LOGIN AND PAGE ACCESS...")
    session = requests.Session()
    
    # Login as Executor Public
    login_data = {'username': 'executor_public', 'password': 'Shiva@123'}
    login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
    
    if login_response.status_code in [200, 302]:
        print("   ✅ Login successful!")
        
        # Access the Executive Public page
        page_response = session.get('http://127.0.0.1:5001/executor-public')
        print(f"   📄 Page access status: {page_response.status_code}")
        
        if page_response.status_code == 200:
            print("   ✅ Executive Public page loaded successfully!")
            
            # Check if the page contains file information
            page_content = page_response.text
            
            # Look for key indicators
            if 'Z6093_Promo_Nanmai-Uruvam' in page_content:
                print("   ✅ Found Z6093 file in page content!")
            else:
                print("   ❌ Z6093 file NOT found in page content")
            
            if 'Z5941_Promo_Isha-Yoga-Center' in page_content:
                print("   ✅ Found Z5941 file in page content!")
            else:
                print("   ❌ Z5941 file NOT found in page content")
            
            if 'No Files to Process' in page_content:
                print("   ❌ Page shows 'No Files to Process' message")
            else:
                print("   ✅ Page does NOT show 'No Files to Process' message")
            
            # Count how many files are shown
            file_count = page_content.count('<tr id="file-row-')
            print(f"   📊 Number of files displayed: {file_count}")
            
        else:
            print(f"   ❌ Failed to load Executive Public page: {page_response.status_code}")
            
    else:
        print(f"   ❌ Login failed: {login_response.status_code}")
    
    # Step 3: Test the route directly (simulate what Flask does)
    print("\n3. 🔧 TESTING FLASK ROUTE LOGIC DIRECTLY...")
    try:
        conn = sqlite3.connect('archives_management.db')
        cursor = conn.cursor()
        
        # Execute the exact query from the Flask route
        cursor.execute('''
            SELECT id, folder_name, folder_path, category, video_ids, url, remarks,
                   created_at, processed_at, assign_to
            FROM queue_items
            WHERE assign_to = 'Executor Public'
            AND status = 'completed'
            AND category != 'Private one video'
            ORDER BY processed_at DESC
        ''')
        
        rows = cursor.fetchall()
        print(f"   📊 Flask route query returned: {len(rows)} rows")
        
        available_files = []
        for row in rows:
            folder_path = row[2]
            folder_name = row[1]
            
            file_data = {
                'id': row[0], 
                'filename': folder_name, 
                'file_path': folder_path,
                'category': row[3], 
                'url': row[5], 
                'cross_checked_at': row[8],  # processed_at
                'cross_checked_by': 'Assigner',
                'video_ids': row[4],
                'remarks': row[6]
            }
            available_files.append(file_data)
            
            print(f"   📁 File {len(available_files)}: {file_data['filename']}")
            print(f"      📂 Category: {file_data['category']}")
            print(f"      🎬 Video IDs: {file_data['video_ids']}")
        
        print(f"   ✅ Flask route would pass {len(available_files)} files to template")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Flask route simulation error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 TEST RESULTS SUMMARY:")
    print("=" * 60)
    
    print("✅ **WHAT TO CHECK IN BROWSER:**")
    print("   1. Open: http://127.0.0.1:5001/executor-public")
    print("   2. Login with: executor_public / Shiva@123")
    print("   3. Look for files in the 'Available Files for Processing' section")
    print("   4. You should see the assigned folders with Process/Details buttons")
    
    print("\n🔍 **IF FILES ARE NOT SHOWING:**")
    print("   - Check the Flask logs for any errors")
    print("   - Verify the database has completed queue items")
    print("   - Check if the template is rendering correctly")
    
    print("\n🎉 **IF FILES ARE SHOWING:**")
    print("   - The queue system is working correctly!")
    print("   - You can now process files through the Executive Public interface")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    test_executive_interface()
