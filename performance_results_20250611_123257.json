{"summary": {"total_tests": 5, "passed": 4, "failed": 1, "warnings": 0, "skipped": 0, "success_rate": 80.0, "avg_duration": 0.1716312885284424}, "detailed_results": [{"test_name": "Create 10 test files", "status": "PASS", "duration": 0.22030115127563477, "details": "Created 40 files and folders", "timestamp": "2025-06-11T12:32:56.758495"}, {"test_name": "Concurrent operations (5 threads)", "status": "PASS", "duration": 0.534895658493042, "details": "Completed 5 concurrent file operations", "timestamp": "2025-06-11T12:32:57.293650"}, {"test_name": "Large file handling", "status": "PASS", "duration": 0.025210857391357422, "details": "Processed 1025KB file successfully", "timestamp": "2025-06-11T12:32:57.319215"}, {"test_name": "Database performance", "status": "FAIL", "duration": 0.006040811538696289, "details": "no such table: users", "timestamp": "2025-06-11T12:32:57.325763"}, {"test_name": "Memory usage", "status": "PASS", "duration": 0.07170796394348145, "details": "Memory increase: 0.0MB (Initial: 19.3MB, Final: 19.3MB)", "timestamp": "2025-06-11T12:32:57.397940"}]}