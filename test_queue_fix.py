#!/usr/bin/env python3
"""
COMPREHENSIVE QUEUE FUNCTIONALITY TEST AND FIX
This script will test and demonstrate the complete queue workflow
"""

import requests
import json
import time
import sqlite3

BASE_URL = "http://127.0.0.1:5001"

def test_and_fix_queue():
    """Test and fix the complete queue workflow"""
    print("🔧 COMPREHENSIVE QUEUE FUNCTIONALITY TEST AND FIX")
    print("=" * 80)
    
    # Step 1: Check current database status
    print("1. 💾 CHECKING DATABASE STATUS...")
    try:
        conn = sqlite3.connect('archives_management.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM queue_items')
        count = cursor.fetchone()[0]
        print(f"   📊 Total queue items in database: {count}")
        
        if count > 0:
            cursor.execute('SELECT folder_name, assign_to, status FROM queue_items ORDER BY created_at DESC LIMIT 3')
            items = cursor.fetchall()
            print("   📋 Recent queue items:")
            for item in items:
                print(f"      - {item[0]} → {item[1]} (Status: {item[2]})")
        
        conn.close()
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # Step 2: Login as Assigner and add test items
    print("\n2. 📋 TESTING ASSIGNER WORKFLOW...")
    session = requests.Session()
    
    # Login as Assigner
    login_data = {'username': 'assigner', 'password': 'assign123'}
    login_response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if login_response.status_code == 200:
        print("   ✅ Assigner login successful")
        
        # Add test items to queue
        test_folders = [
            {
                'folder_path': r'T:\To_Process\Rough folder\restored files\Z6271_Daily-Mystic-Quote_IGTV_06-Aug-19_English_01Min-24Secs_Consolidated_19-Aug-2019',
                'category': 'Social media outputs without stems',
                'assign_to': 'Executive Public',
                'video_ids': 'TEST-PUB-001',
                'url': 'https://example.com/public',
                'remarks': 'Test assignment for Executive Public'
            },
            {
                'folder_path': r'T:\To_Process\Rough folder\restored files\Z6466_Daily-Mystic-Quote_15-Aug-2019_English_Consolidated_04-Oct-2019',
                'category': 'Private one video',
                'assign_to': 'Executive Private',
                'video_ids': 'TEST-PRIV-001',
                'url': 'https://example.com/private',
                'remarks': 'Test assignment for Executive Private'
            }
        ]
        
        for i, folder_data in enumerate(test_folders):
            print(f"   📁 Adding test folder {i+1}: {folder_data['assign_to']}")
            
            # Add to queue
            add_response = session.post(f"{BASE_URL}/api/add-to-queue", data=folder_data)
            if add_response.status_code == 200:
                print(f"      ✅ Added to queue successfully")
            else:
                print(f"      ❌ Failed to add to queue: {add_response.status_code}")
                continue
            
            # Process the queue
            process_response = session.post(f"{BASE_URL}/api/process-queue")
            if process_response.status_code == 200:
                print(f"      ✅ Queue processed successfully")
                time.sleep(1)  # Wait for processing
            else:
                print(f"      ❌ Queue processing failed: {process_response.status_code}")
    else:
        print("   ❌ Assigner login failed")
        return False
    
    # Step 3: Check database after processing
    print("\n3. 💾 CHECKING DATABASE AFTER PROCESSING...")
    try:
        conn = sqlite3.connect('archives_management.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM queue_items WHERE status = "completed"')
        completed_count = cursor.fetchone()[0]
        print(f"   📊 Completed queue items: {completed_count}")
        
        # Check Executive Public assignments
        cursor.execute('''
            SELECT folder_name, category FROM queue_items 
            WHERE assign_to = 'Executive Public' AND status = 'completed'
        ''')
        public_items = cursor.fetchall()
        print(f"   🎬 Executive Public assignments: {len(public_items)}")
        for item in public_items:
            print(f"      - {item[0]} ({item[1]})")
        
        # Check Executive Private assignments
        cursor.execute('''
            SELECT folder_name, category FROM queue_items 
            WHERE assign_to = 'Executive Private' AND status = 'completed'
        ''')
        private_items = cursor.fetchall()
        print(f"   🔒 Executive Private assignments: {len(private_items)}")
        for item in private_items:
            print(f"      - {item[0]} ({item[1]})")
        
        conn.close()
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # Step 4: Test Executive Public API
    print("\n4. 🎬 TESTING EXECUTIVE PUBLIC API...")
    exec_public_session = requests.Session()
    
    # Login as Executive Public
    login_data = {'username': 'executor_public', 'password': 'Shiva@123'}
    login_response = exec_public_session.post(f"{BASE_URL}/login", data=login_data)
    
    if login_response.status_code in [200, 302]:
        print("   ✅ Executive Public login successful")
        
        # Test API
        try:
            queue_response = exec_public_session.get(f"{BASE_URL}/api/executive-public/queue")
            print(f"   📡 API Response Status: {queue_response.status_code}")
            
            if queue_response.status_code == 200:
                try:
                    queue_data = queue_response.json()
                    if queue_data.get('success'):
                        files = queue_data.get('files', [])
                        print(f"   📊 Executive Public Queue: {len(files)} files")
                        
                        for i, file in enumerate(files):
                            print(f"      {i+1}. {file['folder_name']}")
                            print(f"         📂 Category: {file.get('category', 'Unknown')}")
                            print(f"         📅 Assigned: {file['assigned_date']}")
                    else:
                        print(f"   ❌ API Error: {queue_data.get('error')}")
                except json.JSONDecodeError:
                    print(f"   ❌ Invalid JSON response")
            else:
                print(f"   ❌ API request failed: {queue_response.status_code}")
                print(f"   📄 Response: {queue_response.text[:200]}...")
        except Exception as e:
            print(f"   ❌ API test error: {e}")
    else:
        print("   ❌ Executive Public login failed")
    
    # Step 5: Test Executive Private API
    print("\n5. 🔒 TESTING EXECUTIVE PRIVATE API...")
    exec_private_session = requests.Session()
    
    # Login as Executive Private
    login_data = {'username': 'executor_private', 'password': 'Shiva@123'}
    login_response = exec_private_session.post(f"{BASE_URL}/login", data=login_data)
    
    if login_response.status_code in [200, 302]:
        print("   ✅ Executive Private login successful")
        
        # Test API
        try:
            queue_response = exec_private_session.get(f"{BASE_URL}/api/executive-private/queue")
            print(f"   📡 API Response Status: {queue_response.status_code}")
            
            if queue_response.status_code == 200:
                try:
                    queue_data = queue_response.json()
                    if queue_data.get('success'):
                        files = queue_data.get('files', [])
                        print(f"   📊 Executive Private Queue: {len(files)} files")
                        
                        for i, file in enumerate(files):
                            print(f"      {i+1}. {file['folder_name']}")
                            print(f"         📂 Category: {file.get('category', 'Unknown')}")
                            print(f"         🔒 Private: {file.get('is_private', False)}")
                            print(f"         📅 Assigned: {file['assigned_date']}")
                    else:
                        print(f"   ❌ API Error: {queue_data.get('error')}")
                except json.JSONDecodeError:
                    print(f"   ❌ Invalid JSON response")
            else:
                print(f"   ❌ API request failed: {queue_response.status_code}")
                print(f"   📄 Response: {queue_response.text[:200]}...")
        except Exception as e:
            print(f"   ❌ API test error: {e}")
    else:
        print("   ❌ Executive Private login failed")
    
    # Results Summary
    print("\n" + "=" * 80)
    print("🎉 QUEUE FUNCTIONALITY TEST RESULTS")
    print("=" * 80)
    
    print("✅ **WHAT WAS TESTED:**")
    print("   📋 Assigner workflow (add to queue + process)")
    print("   💾 Database queue tracking")
    print("   🎬 Executive Public API endpoint")
    print("   🔒 Executive Private API endpoint")
    print("   🔄 Complete workflow from assignment to queue display")
    
    print("\n🌐 **HOW TO ACCESS THE INTERFACES:**")
    print("   🔗 Assigner: http://127.0.0.1:5001/assigner-interface")
    print("   🔗 Executive Public: http://127.0.0.1:5001/executive-public")
    print("   🔗 Executive Private: http://127.0.0.1:5001/executive-private")
    
    print("\n🔑 **LOGIN CREDENTIALS:**")
    print("   📋 Assigner: assigner / assign123")
    print("   🎬 Executive Public: executor_public / Shiva@123")
    print("   🔒 Executive Private: executor_private / Shiva@123")
    
    print("\n🚀 **NEXT STEPS:**")
    print("   1. Login to Executive Public/Private interfaces via browser")
    print("   2. Check if assigned files appear in the queue")
    print("   3. If files appear, the system is working correctly!")
    print("   4. If not, check the Flask logs for errors")
    
    print("\n🎉 **QUEUE SYSTEM TEST COMPLETED!**")
    
    return True

if __name__ == "__main__":
    print("🔧 COMPREHENSIVE QUEUE FUNCTIONALITY TEST")
    print("This test will verify and fix the complete queue workflow")
    print("")
    
    success = test_and_fix_queue()
    
    if success:
        print("\n🚀 QUEUE TEST COMPLETED!")
        print("🎉 Check the results above to see if the system is working!")
    else:
        print("\n❌ QUEUE TEST FAILED")
        print("🔍 Check the Flask logs for more details")
    
    print("=" * 80)
