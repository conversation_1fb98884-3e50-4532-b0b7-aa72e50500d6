#!/usr/bin/env python3
"""
Test the Process button functionality in browser by simulating exact user workflow
"""

import requests
import json
import time

def test_browser_process_button():
    print("🌐 TESTING PROCESS BUTTON IN BROWSER SIMULATION")
    print("=" * 60)
    
    # Create a session to maintain login state
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # Step 1: Login (simulating browser login)
        print("1. 🔐 Simulating Browser Login...")
        login_data = {
            'username': 'executor_public',
            'password': 'Shiva@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        print(f"   Login Response: {login_response.status_code}")
        
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return
        
        print("   ✅ Login successful!")
        
        # Step 2: Load Executive Public Dashboard (simulating page load)
        print("\n2. 📄 Loading Executive Public Dashboard...")
        dashboard_response = session.get(f"{base_url}/executor-public")
        print(f"   Dashboard Response: {dashboard_response.status_code}")
        
        if dashboard_response.status_code != 200:
            print("   ❌ Dashboard failed to load!")
            return
        
        # Check if enhanced interface elements are present
        dashboard_content = dashboard_response.text
        has_metadata_modal = 'metadataModal' in dashboard_content
        has_tabbed_interface = 'nav-tabs' in dashboard_content
        has_process_function = 'processFileEnhanced' in dashboard_content
        
        print(f"   ✅ Dashboard loaded successfully!")
        print(f"   📋 Enhanced metadata modal: {'✅' if has_metadata_modal else '❌'}")
        print(f"   📑 Tabbed interface: {'✅' if has_tabbed_interface else '❌'}")
        print(f"   🔧 Enhanced process function: {'✅' if has_process_function else '❌'}")
        
        # Step 3: Load Queue (simulating JavaScript queue loading)
        print("\n3. 📋 Loading Files Queue (simulating JavaScript call)...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        print(f"   Queue API Response: {queue_response.status_code}")
        
        if queue_response.status_code != 200:
            print("   ❌ Queue API failed!")
            return
        
        queue_data = queue_response.json()
        if not queue_data.get('success'):
            print(f"   ❌ Queue API error: {queue_data.get('error')}")
            return
        
        files = queue_data.get('files', [])
        print(f"   ✅ Queue loaded successfully!")
        print(f"   📁 Files in queue: {len(files)}")
        
        if not files:
            print("   ℹ️ No files in queue to test Process button")
            return
        
        # Step 4: Test Process Button Click (simulating user clicking Process)
        test_file = files[0]
        queue_item_id = test_file.get('queue_item_id')
        folder_name = test_file.get('folder_name', 'Unknown')
        
        print(f"\n4. 🖱️ Simulating Process Button Click...")
        print(f"   📁 Testing file: {folder_name}")
        print(f"   🆔 Queue Item ID: {queue_item_id}")
        
        if not queue_item_id:
            print("   ❌ Queue Item ID is missing!")
            return
        
        # Step 5: Load File Details (simulating processFileEnhanced function)
        print("\n5. 🔍 Loading File Details (simulating processFileEnhanced)...")
        details_response = session.get(f"{base_url}/api/file-details/{queue_item_id}")
        print(f"   File Details API Response: {details_response.status_code}")
        
        if details_response.status_code != 200:
            print(f"   ❌ File Details API failed: {details_response.status_code}")
            if details_response.status_code == 404:
                print("   📁 File not found - check queue item ID")
            elif details_response.status_code == 403:
                print("   🔒 Access denied - check permissions")
            elif details_response.status_code == 500:
                print("   🔥 Server error - check Flask logs")
            return
        
        details_data = details_response.json()
        if not details_data.get('success'):
            print(f"   ❌ File Details error: {details_data.get('error')}")
            return
        
        file_details = details_data.get('file_details', {})
        print("   ✅ File Details loaded successfully!")
        print(f"   📁 Folder: {file_details.get('folder_name', 'Unknown')}")
        print(f"   📊 File Count: {file_details.get('file_count', 0)}")
        print(f"   💾 Size: {file_details.get('total_size_formatted', 'Unknown')}")
        print(f"   🎬 Duration: {file_details.get('detected_duration', 'Unknown')}")
        print(f"   📺 Resolution: {file_details.get('detected_resolution', 'Unknown')}")
        print(f"   🔧 Has Suggested Codes: {bool(file_details.get('suggested_codes'))}")
        
        # Step 6: Load Metadata Options (simulating modal opening)
        print("\n6. 📝 Loading Metadata Options (simulating modal opening)...")
        options_response = session.get(f"{base_url}/api/executive-public/metadata-options")
        print(f"   Metadata Options API Response: {options_response.status_code}")
        
        if options_response.status_code != 200:
            print("   ❌ Metadata Options API failed!")
            return
        
        options_data = options_response.json()
        if not options_data.get('success'):
            print(f"   ❌ Metadata Options error: {options_data.get('error')}")
            return
        
        options = options_data.get('options', {})
        print("   ✅ Metadata Options loaded successfully!")
        print(f"   🎬 Video Types: {len(options.get('video_types', []))}")
        print(f"   🌍 Languages: {len(options.get('languages', []))}")
        print(f"   🏢 Departments: {len(options.get('departments', []))}")
        print(f"   🏷️ Content Tags: {len(options.get('content_tags', []))}")
        print(f"   💾 Backup Types: {len(options.get('backup_types', []))}")
        
        # Step 7: Test Draft Saving (simulating user saving draft)
        print("\n7. 💾 Testing Draft Saving...")
        sample_metadata = {
            'ocd_vp_number': 'OCD-2025-001',
            'edited_file_name': folder_name,
            'language': 'English',
            'edited_year': '2025',
            'video_type': 'Promotional',
            'edited_location': 'Ashram',
            'component': 'Sadhguru',
            'published_platforms': 'YouTube',
            'department_name': 'Media',
            'access_level': 'Public',
            'content_tags': 'Wisdom',
            'backup_type': 'Cloud',
            'audio_code': 'AUD-2025-001',
            'video_id': 'VID-2025-001',
            'social_media_title': folder_name
        }
        
        draft_response = session.post(
            f"{base_url}/api/save-metadata-draft",
            json={
                'queue_item_id': queue_item_id,
                'metadata': sample_metadata
            },
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Draft Save Response: {draft_response.status_code}")
        
        if draft_response.status_code == 200:
            draft_data = draft_response.json()
            if draft_data.get('success'):
                print("   ✅ Draft saving working!")
            else:
                print(f"   ⚠️ Draft save error: {draft_data.get('error')}")
        else:
            print(f"   ⚠️ Draft save failed: {draft_response.status_code}")
        
        print("\n" + "=" * 60)
        print("🎉 BROWSER PROCESS BUTTON TEST RESULTS")
        print("=" * 60)
        print("✅ Login simulation successful")
        print("✅ Dashboard loading successful")
        print("✅ Enhanced interface elements detected")
        print("✅ Queue loading successful")
        print("✅ Process button simulation successful")
        print("✅ File details loading successful")
        print("✅ Metadata options loading successful")
        print("✅ Draft saving tested")
        
        print("\n🎯 WHAT THIS MEANS:")
        print("• The Process button should work correctly in the browser")
        print("• All API endpoints are responding properly")
        print("• The comprehensive metadata modal should open")
        print("• File details should be auto-populated")
        print("• All dropdown options should be available")
        
        print("\n🌐 TO TEST IN ACTUAL BROWSER:")
        print("1. Open: http://127.0.0.1:5001/executor-public")
        print("2. Login with: executor_public / Shiva@123")
        print("3. Click 'Process' button on any file")
        print("4. The comprehensive metadata modal should open")
        print("5. Check that all tabs and fields are working")
        
        print("\n🔧 IF STILL HAVING ISSUES:")
        print("• Open browser Developer Tools (F12)")
        print("• Check Console tab for JavaScript errors")
        print("• Check Network tab for failed API calls")
        print("• Verify all HTML elements are present")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_browser_process_button()
