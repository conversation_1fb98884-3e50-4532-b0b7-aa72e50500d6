#!/usr/bin/env python3
"""
TEST PATH FIX
Verify the path fix is working
"""

import requests
import json

def test_path_fix():
    print("🔧 TESTING PATH FIX")
    print("="*40)
    
    session = requests.Session()
    
    # Login
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
    print(f"Login: {login_response.status_code}")
    
    # Get folders
    folders_response = session.get('http://127.0.0.1:5001/api/get-folders')
    folders_data = folders_response.json()
    
    if folders_data.get('success'):
        test_folder = folders_data['folders'][0]['path']
        print(f"Test folder: {repr(test_folder)}")
        print(f"Contains backslashes: {'\\\\' in test_folder}")
        print(f"Starts with T:: {test_folder.startswith('T:')}")
        
        # Test assignment
        assign_data = {
            'folder_paths': [test_folder],
            'category': 'Social media outputs with stems',
            'assign_to': 'Executor Public',
            'video_ids': 'PATH-FIX-TEST',
            'url': 'https://pathfix.com',
            'remarks': 'Path fix test',
            'operation_type': 'copy'
        }
        
        assign_response = session.post('http://127.0.0.1:5001/api/add-to-queue', json=assign_data)
        assign_result = assign_response.json()
        
        print(f"\nAssignment result:")
        print(f"Success: {assign_result.get('success')}")
        print(f"Added: {assign_result.get('added')}")
        print(f"Failed: {assign_result.get('failed')}")
        
        if assign_result.get('failed_items'):
            print(f"Failed items: {assign_result['failed_items']}")
    else:
        print(f"Folders API failed: {folders_data.get('error')}")

if __name__ == "__main__":
    test_path_fix()
