#!/usr/bin/env python3
"""
TEST ARCHIVES ASSIGNMENT CONSOLE
Test the actual Archives Assignment Console workflow to verify Google Sheets columns
"""

import requests
import json
from datetime import datetime

def test_archives_console():
    print("🎬 TESTING ARCHIVES ASSIGNMENT CONSOLE")
    print("="*60)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Login as assigner
    print("🔐 Logging in as assigner...")
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    print("✅ Login successful")
    
    # Get folders (same as Archives Console does)
    print("\n📁 Getting folders...")
    folders_response = session.get(f"{base_url}/api/get-folders")
    
    if folders_response.status_code != 200:
        print(f"❌ Folders API failed: {folders_response.status_code}")
        return
    
    folders_data = folders_response.json()
    if not folders_data.get('success') or not folders_data.get('folders'):
        print("❌ No folders available")
        return
    
    # Use first folder for testing
    test_folder = folders_data['folders'][0]
    folder_path = test_folder['path']
    folder_name = test_folder['name']
    
    print(f"✅ Using test folder: {folder_name}")
    
    # Step 1: Add to queue (Archives Console workflow)
    timestamp = datetime.now().strftime("%H%M%S")
    print(f"\n📋 STEP 1: Adding to queue (Archives Console workflow)...")
    
    queue_data = {
        'folder_paths': [folder_path],
        'category': 'Internal video without stems',
        'assign_to': 'Executor Public',
        'video_ids': f'ARCHIVES_TEST_{timestamp}',
        'url': f'https://archives-test-{timestamp}.com',
        'remarks': f'ARCHIVES CONSOLE TEST {timestamp} - SHOULD BE COLUMNS A-F'
    }
    
    print(f"📝 Queue data:")
    print(f"   📁 Folder: {folder_name}")
    print(f"   📂 Category: {queue_data['category']}")
    print(f"   👤 Assign to: {queue_data['assign_to']}")
    print(f"   🆔 Video IDs: {queue_data['video_ids']}")
    print(f"   🔗 URL: {queue_data['url']}")
    print(f"   📝 Remarks: {queue_data['remarks']}")
    
    # Add to queue (same API call as Archives Console)
    add_response = session.post(f"{base_url}/api/add-to-queue", json=queue_data)
    
    if add_response.status_code == 200:
        add_result = add_response.json()
        print(f"✅ Added to queue: {add_result.get('success', False)}")
        print(f"✅ Added {add_result.get('added', 0)} items")
    else:
        print(f"❌ Add to queue failed: {add_response.status_code}")
        return
    
    # Step 2: Process queue (this should write to Google Sheets columns A-F)
    print(f"\n🚀 STEP 2: Processing queue (should write to columns A-F)...")
    
    process_response = session.post(f"{base_url}/api/process-queue")
    
    if process_response.status_code == 200:
        process_result = process_response.json()
        print(f"✅ Queue processed: {process_result.get('success', False)}")
        print(f"✅ Processed {process_result.get('processed', 0)} items")
        
        if process_result.get('processed_items'):
            for item in process_result['processed_items']:
                print(f"   📁 Processed: {item.get('folder_name')}")
                print(f"   📂 Category: {item.get('category')}")
    else:
        print(f"❌ Process queue failed: {process_response.status_code}")
        print(f"❌ Response: {process_response.text}")
        return
    
    print(f"\n🎯 EXPECTED RESULT:")
    print(f"✅ Data should appear in Google Sheets columns A-F")
    print(f"   Column A: {folder_name}")
    print(f"   Column B: Date processed")
    print(f"   Column C: {queue_data['category']}")
    print(f"   Column D: {queue_data['url']}")
    print(f"   Column E: {queue_data['assign_to']}")
    print(f"   Column F: Video IDs: {queue_data['video_ids']}. {queue_data['remarks']}")
    
    print(f"\n🔍 VERIFICATION:")
    print(f"1. Open Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print(f"2. Go to 'Records' sheet")
    print(f"3. Look for folder name '{folder_name}' in Column A")
    print(f"4. Look for 'ARCHIVES_TEST_{timestamp}' in Column F")
    print(f"5. Verify all data is in columns A-F, NOT G-M")
    
    print(f"\n✅ ARCHIVES ASSIGNMENT CONSOLE TEST COMPLETE!")
    print("="*60)

if __name__ == "__main__":
    test_archives_console()
