#!/usr/bin/env python3
"""
Simple test for VLC integration
"""

import requests
import json

def test_vlc_endpoints():
    print("🎬 Testing VLC Integration")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Login
    print("📋 Step 1: Login")
    login_data = {'username': 'admin', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=True)
    
    if 'professional-assigner' in login_response.url or 'dashboard' in login_response.url:
        print("✅ Login successful")
    else:
        print("❌ Login failed")
        return
    
    # Test VLC info
    print("\n📋 Step 2: Test VLC Info")
    try:
        response = session.get(f"{base_url}/api/vlc/info")
        data = response.json()
        
        if data.get('success'):
            info = data.get('info', {})
            print(f"✅ VLC Available: {info.get('vlc_available')}")
            print(f"✅ VLC Version: {info.get('version')}")
            print(f"✅ VLC Path: {info.get('vlc_path')}")
            print(f"✅ Active Sessions: {info.get('active_sessions')}")
        else:
            print(f"❌ VLC Info failed: {data.get('error')}")
    except Exception as e:
        print(f"❌ VLC Info error: {e}")
    
    # Test embedded VLC page
    print("\n📋 Step 3: Test Embedded VLC Page")
    try:
        response = session.get(f"{base_url}/embedded-vlc-player")
        if response.status_code == 200:
            content = response.text
            checks = [
                ('VLC Player Container', 'vlc-player-container' in content),
                ('Video Display', 'videoDisplay' in content),
                ('Control Panel', 'controls-panel' in content),
                ('File Browser', 'fileBrowser' in content),
                ('JavaScript Functions', 'initializeVLCPlayer' in content)
            ]
            
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"{status} {check_name}")
        else:
            print(f"❌ Page access failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Page access error: {e}")
    
    # Test VLC session creation
    print("\n📋 Step 4: Test VLC Session Creation")
    try:
        response = session.post(f"{base_url}/api/vlc/create-session",
                               headers={'Content-Type': 'application/json'})
        data = response.json()
        
        if data.get('success'):
            session_id = data.get('session_id')
            print(f"✅ VLC Session Created: {session_id[:8]}...")
            
            # Test session status
            print("\n📋 Step 5: Test VLC Status")
            status_response = session.get(f"{base_url}/api/vlc/status",
                                        params={'session_id': session_id})
            status_data = status_response.json()
            
            if status_data.get('success'):
                status = status_data.get('status', {})
                print(f"✅ Player State: {status.get('state')}")
                print(f"✅ Volume: {status.get('volume')}%")
            else:
                print(f"❌ Status failed: {status_data.get('error')}")
            
            # Cleanup session
            print("\n📋 Step 6: Cleanup Session")
            cleanup_response = session.post(f"{base_url}/api/vlc/cleanup",
                                          headers={'Content-Type': 'application/json'},
                                          json={'session_id': session_id})
            cleanup_data = cleanup_response.json()
            
            if cleanup_data.get('success'):
                print("✅ Session cleaned up successfully")
            else:
                print(f"❌ Cleanup failed: {cleanup_data.get('error')}")
                
        else:
            print(f"❌ Session creation failed: {data.get('error')}")
    except Exception as e:
        print(f"❌ Session creation error: {e}")
    
    print("\n🎉 VLC Integration Test Complete!")

if __name__ == "__main__":
    test_vlc_endpoints()
