#!/usr/bin/env python3
"""
SIMPLE TEST FOR FILES IN FOLDERS
Tests if individual files are visible in the folder tree
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5001"

def test_files_in_folders():
    """Test if individual files are visible in folder tree"""
    print("🎬 TESTING FILES VISIBILITY IN FOLDER TREE")
    print("=" * 60)
    
    # Create session for login
    session = requests.Session()
    
    # Login
    login_data = {'username': 'admin', 'password': 'admin123'}
    try:
        login_response = session.post(f"{BASE_URL}/login", data=login_data)
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Test Enhanced Folder Tree API
    print("\n🌳 Testing Enhanced Folder Tree API...")
    try:
        folders_response = session.get(f"{BASE_URL}/api/get-folders")
        if folders_response.status_code == 200:
            folders_data = folders_response.json()
            if folders_data.get('success'):
                folders = folders_data.get('folders', [])
                print(f"✅ Enhanced Folder Tree API working - Found {len(folders)} top-level folders")
                
                # Count files and folders
                total_folders = 0
                total_files = 0
                
                def count_items(items):
                    nonlocal total_folders, total_files
                    for item in items:
                        if item.get('type') == 'file':
                            total_files += 1
                            print(f"   📄 FILE: {item['name']} ({item.get('size', 'Unknown size')})")
                        else:
                            total_folders += 1
                            print(f"   📁 FOLDER: {item['name']} ({item.get('file_count', 0)} files)")
                            
                            # Check children
                            if item.get('children'):
                                count_items(item['children'])
                
                count_items(folders)
                
                print(f"\n📊 SUMMARY:")
                print(f"   📁 Total folders: {total_folders}")
                print(f"   📄 Total individual files shown: {total_files}")
                
                if total_files > 0:
                    print(f"\n🎉 SUCCESS: Individual files are visible in the tree!")
                    print(f"   ✅ You can now see and play individual video files")
                    print(f"   ✅ Each file has a VLC play button")
                    print(f"   ✅ Files show size information")
                else:
                    print(f"\n⚠️  No individual files found in tree structure")
                    print(f"   This might be because files are deeper in the folder structure")
                
            else:
                print(f"❌ Enhanced Folder Tree API failed: {folders_data.get('error')}")
        else:
            print(f"❌ Enhanced Folder Tree API error: {folders_response.status_code}")
    except Exception as e:
        print(f"❌ Enhanced Folder Tree API exception: {e}")
    
    print(f"\n🌐 Access the enhanced interface at: {BASE_URL}/professional-assigner")

if __name__ == "__main__":
    test_files_in_folders()
