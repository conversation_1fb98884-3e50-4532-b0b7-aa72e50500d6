#!/usr/bin/env python3
"""
Setup script for embedded VLC media player
Downloads and configures a portable VLC installation for the Archives Management System
"""

import os
import sys
import urllib.request
import zipfile
import shutil
import platform
import subprocess
from pathlib import Path

class EmbeddedVLCSetup:
    def __init__(self):
        self.system = platform.system()
        self.architecture = platform.machine()
        self.app_dir = Path(__file__).parent
        self.vlc_dir = self.app_dir / "embedded_vlc"
        
        # VLC download URLs for different platforms
        self.vlc_urls = {
            'Windows': {
                'x86_64': 'https://get.videolan.org/vlc/3.0.18/win64/vlc-3.0.18-win64.zip',
                'x86': 'https://get.videolan.org/vlc/3.0.18/win32/vlc-3.0.18-win32.zip'
            },
            'Darwin': {
                'x86_64': 'https://get.videolan.org/vlc/3.0.18/macosx/vlc-3.0.18-intel64.dmg',
                'arm64': 'https://get.videolan.org/vlc/3.0.18/macosx/vlc-3.0.18-arm64.dmg'
            },
            'Linux': {
                'x86_64': 'https://get.videolan.org/vlc/3.0.18/vlc-3.0.18.tar.xz'
            }
        }
    
    def print_status(self, message, status="INFO"):
        """Print status message with formatting"""
        symbols = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "ERROR": "❌",
            "WARNING": "⚠️",
            "DOWNLOAD": "📥"
        }
        print(f"{symbols.get(status, 'ℹ️')} {message}")
    
    def check_existing_vlc(self):
        """Check if VLC is already installed on the system"""
        vlc_paths = {
            'Windows': [
                r'C:\Program Files\VideoLAN\VLC\vlc.exe',
                r'C:\Program Files (x86)\VideoLAN\VLC\vlc.exe'
            ],
            'Darwin': ['/Applications/VLC.app/Contents/MacOS/VLC'],
            'Linux': ['/usr/bin/vlc', '/snap/bin/vlc']
        }
        
        system_paths = vlc_paths.get(self.system, [])
        for path in system_paths:
            if os.path.exists(path):
                self.print_status(f"Found existing VLC installation: {path}", "SUCCESS")
                return path
        
        # Check PATH
        try:
            vlc_path = shutil.which("vlc")
            if vlc_path:
                self.print_status(f"Found VLC in PATH: {vlc_path}", "SUCCESS")
                return vlc_path
        except:
            pass
        
        self.print_status("No existing VLC installation found", "WARNING")
        return None
    
    def download_vlc(self):
        """Download VLC for the current platform"""
        if self.system not in self.vlc_urls:
            self.print_status(f"Unsupported platform: {self.system}", "ERROR")
            return False
        
        arch_key = 'x86_64' if self.architecture in ['x86_64', 'AMD64'] else self.architecture
        if arch_key not in self.vlc_urls[self.system]:
            arch_key = 'x86_64'  # Fallback to x86_64
        
        url = self.vlc_urls[self.system][arch_key]
        filename = url.split('/')[-1]
        download_path = self.app_dir / filename
        
        self.print_status(f"Downloading VLC from: {url}", "DOWNLOAD")
        
        try:
            # Download with progress
            def progress_hook(block_num, block_size, total_size):
                downloaded = block_num * block_size
                if total_size > 0:
                    percent = min(100, (downloaded * 100) // total_size)
                    print(f"\rDownload progress: {percent}%", end='', flush=True)
            
            urllib.request.urlretrieve(url, download_path, progress_hook)
            print()  # New line after progress
            
            self.print_status(f"Downloaded: {filename}", "SUCCESS")
            return download_path
            
        except Exception as e:
            self.print_status(f"Download failed: {e}", "ERROR")
            return None
    
    def extract_vlc_windows(self, zip_path):
        """Extract VLC on Windows"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Extract to temporary directory first
                temp_dir = self.app_dir / "temp_vlc"
                zip_ref.extractall(temp_dir)
                
                # Find the VLC directory (usually vlc-x.x.x)
                vlc_folders = [f for f in temp_dir.iterdir() if f.is_dir() and f.name.startswith('vlc-')]
                if vlc_folders:
                    vlc_source = vlc_folders[0]
                    
                    # Move to final location
                    if self.vlc_dir.exists():
                        shutil.rmtree(self.vlc_dir)
                    shutil.move(str(vlc_source), str(self.vlc_dir))
                    
                    # Clean up
                    shutil.rmtree(temp_dir)
                    
                    self.print_status("VLC extracted successfully", "SUCCESS")
                    return True
                else:
                    self.print_status("Could not find VLC directory in archive", "ERROR")
                    return False
                    
        except Exception as e:
            self.print_status(f"Extraction failed: {e}", "ERROR")
            return False
    
    def setup_vlc_config(self):
        """Setup VLC configuration for embedded use"""
        config_content = """
# VLC Configuration for Archives Management System
# Optimized for embedded use

[main]
intf=dummy
no-video-title-show
no-stats
quiet
no-osd
extraintf=http
http-password=vlcpassword
http-port=8080

[http]
http-host=127.0.0.1
http-port=8080
http-password=vlcpassword

[video]
no-video-title-show
no-osd

[audio]
volume=50
"""
        
        try:
            config_dir = self.vlc_dir / "config"
            config_dir.mkdir(exist_ok=True)
            
            config_file = config_dir / "vlcrc"
            with open(config_file, 'w') as f:
                f.write(config_content)
            
            self.print_status("VLC configuration created", "SUCCESS")
            return True
            
        except Exception as e:
            self.print_status(f"Config setup failed: {e}", "ERROR")
            return False
    
    def install_python_vlc(self):
        """Install python-vlc bindings"""
        try:
            self.print_status("Installing python-vlc bindings...", "INFO")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'python-vlc'])
            self.print_status("python-vlc installed successfully", "SUCCESS")
            return True
        except Exception as e:
            self.print_status(f"Failed to install python-vlc: {e}", "ERROR")
            return False
    
    def create_launcher_script(self):
        """Create launcher script for the embedded VLC"""
        if self.system == 'Windows':
            script_content = f"""@echo off
set VLC_PLUGIN_PATH={self.vlc_dir}\\plugins
set VLC_DATA_PATH={self.vlc_dir}
"{self.vlc_dir}\\vlc.exe" %*
"""
            script_path = self.vlc_dir / "vlc_launcher.bat"
        else:
            script_content = f"""#!/bin/bash
export VLC_PLUGIN_PATH="{self.vlc_dir}/plugins"
export VLC_DATA_PATH="{self.vlc_dir}"
"{self.vlc_dir}/vlc" "$@"
"""
            script_path = self.vlc_dir / "vlc_launcher.sh"
        
        try:
            with open(script_path, 'w') as f:
                f.write(script_content)
            
            if self.system != 'Windows':
                os.chmod(script_path, 0o755)
            
            self.print_status("Launcher script created", "SUCCESS")
            return script_path
            
        except Exception as e:
            self.print_status(f"Launcher creation failed: {e}", "ERROR")
            return None
    
    def test_vlc_installation(self):
        """Test the VLC installation"""
        try:
            if self.system == 'Windows':
                vlc_exe = self.vlc_dir / "vlc.exe"
            else:
                vlc_exe = self.vlc_dir / "vlc"
            
            if vlc_exe.exists():
                # Test VLC version
                result = subprocess.run([str(vlc_exe), '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version_line = result.stdout.split('\n')[0]
                    self.print_status(f"VLC test successful: {version_line}", "SUCCESS")
                    return True
                else:
                    self.print_status("VLC test failed", "ERROR")
                    return False
            else:
                self.print_status("VLC executable not found", "ERROR")
                return False
                
        except Exception as e:
            self.print_status(f"VLC test failed: {e}", "ERROR")
            return False
    
    def setup(self):
        """Main setup function"""
        self.print_status("Starting Embedded VLC Setup", "INFO")
        self.print_status(f"Platform: {self.system} {self.architecture}", "INFO")
        
        # Check if we already have embedded VLC
        if self.vlc_dir.exists():
            self.print_status("Embedded VLC directory already exists", "WARNING")
            if input("Remove existing installation? (y/N): ").lower() == 'y':
                shutil.rmtree(self.vlc_dir)
            else:
                self.print_status("Setup cancelled", "INFO")
                return False
        
        # Check for existing system VLC
        existing_vlc = self.check_existing_vlc()
        if existing_vlc:
            use_existing = input("Use existing VLC installation? (Y/n): ").lower()
            if use_existing != 'n':
                self.print_status("Using existing VLC installation", "SUCCESS")
                return True
        
        # Install python-vlc bindings
        if not self.install_python_vlc():
            return False
        
        # Download VLC
        if self.system == 'Windows':
            download_path = self.download_vlc()
            if not download_path:
                return False
            
            # Extract VLC
            if not self.extract_vlc_windows(download_path):
                return False
            
            # Clean up download
            download_path.unlink()
            
        else:
            self.print_status(f"Manual VLC installation required for {self.system}", "WARNING")
            self.print_status("Please install VLC using your system package manager", "INFO")
            return False
        
        # Setup configuration
        if not self.setup_vlc_config():
            return False
        
        # Create launcher script
        launcher = self.create_launcher_script()
        if not launcher:
            return False
        
        # Test installation
        if not self.test_vlc_installation():
            return False
        
        self.print_status("Embedded VLC setup completed successfully!", "SUCCESS")
        self.print_status(f"VLC installed to: {self.vlc_dir}", "INFO")
        
        return True

def main():
    """Main function"""
    print("🎬 Archives Management System - Embedded VLC Setup")
    print("=" * 60)
    
    setup = EmbeddedVLCSetup()
    
    if setup.setup():
        print("\n✅ Setup completed successfully!")
        print("You can now use the embedded VLC media player in the Archives Management System.")
    else:
        print("\n❌ Setup failed!")
        print("Please check the error messages above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
