{"summary": {"total_tests": 22, "passed": 20, "failed": 2, "success_rate": 90.9090909090909}, "detailed_results": [{"test_name": "Database tables", "status": "FAIL", "details": "Missing tables: ['users', 'operations_log', 'metadata_entries']", "timestamp": "2025-06-11T12:29:29.703635"}, {"test_name": "Database functionality", "status": "FAIL", "details": "no such table: users", "timestamp": "2025-06-11T12:29:29.703893"}, {"test_name": "Directory access: source", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\restored files", "timestamp": "2025-06-11T12:29:29.707337"}, {"test_name": "Directory access: cross_check", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\Folder to be cross checked", "timestamp": "2025-06-11T12:29:29.710009"}, {"test_name": "Directory access: audio_tr", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\Audio Sent to TR", "timestamp": "2025-06-11T12:29:29.712200"}, {"test_name": "Directory access: audio_repo", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\Audios Repository", "timestamp": "2025-06-11T12:29:29.714156"}, {"test_name": "Directory access: to_reingest", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\To Reingest", "timestamp": "2025-06-11T12:29:29.716066"}, {"test_name": "Directory access: to_delete", "status": "PASS", "details": "T:\\To_Process\\Rough folder\\To Be Deleted", "timestamp": "2025-06-11T12:29:29.718832"}, {"test_name": "File creation", "status": "PASS", "details": "Created test files in T:\\To_Process\\Rough folder\\restored files\\TEST_MANUAL_20250611_122929", "timestamp": "2025-06-11T12:29:29.745637"}, {"test_name": "File movement", "status": "PASS", "details": "Successfully moved folder to T:\\To_Process\\Rough folder\\Folder to be cross checked\\MOVED_TEST_122929", "timestamp": "2025-06-11T12:29:29.755646"}, {"test_name": "Template: login.html", "status": "PASS", "details": "Size: 8439 bytes", "timestamp": "2025-06-11T12:29:29.759965"}, {"test_name": "Template: dashboard.html", "status": "PASS", "details": "Size: 9272 bytes", "timestamp": "2025-06-11T12:29:29.762480"}, {"test_name": "Template: assigner.html", "status": "PASS", "details": "Size: 21181 bytes", "timestamp": "2025-06-11T12:29:29.763309"}, {"test_name": "Template: executor_public.html", "status": "PASS", "details": "Size: 122078 bytes", "timestamp": "2025-06-11T12:29:29.764412"}, {"test_name": "Template: executor_private.html", "status": "PASS", "details": "Size: 84144 bytes", "timestamp": "2025-06-11T12:29:29.765280"}, {"test_name": "Template: cross_checker.html", "status": "PASS", "details": "Size: 62477 bytes", "timestamp": "2025-06-11T12:29:29.765779"}, {"test_name": "Template: admin.html", "status": "PASS", "details": "Size: 66377 bytes", "timestamp": "2025-06-11T12:29:29.766237"}, {"test_name": "Static files", "status": "PASS", "details": "Found 2 static files", "timestamp": "2025-06-11T12:29:29.767018"}, {"test_name": "App import", "status": "PASS", "details": "Application imported successfully", "timestamp": "2025-06-11T12:29:34.985447"}, {"test_name": "Flask app object", "status": "PASS", "details": "App name: app", "timestamp": "2025-06-11T12:29:34.985678"}, {"test_name": "Route registration", "status": "PASS", "details": "Found 85 routes", "timestamp": "2025-06-11T12:29:34.985966"}, {"test_name": "Google Sheets credentials", "status": "PASS", "details": "Credentials file valid", "timestamp": "2025-06-11T12:29:34.987534"}]}