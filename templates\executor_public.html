{% extends "base.html" %}

{% block title %}Executor Public - File Management System{% endblock %}

{% block head %}
<style>
/* Folder Tree Styles (matching Archives Assignment Console) */
.tree-node {
    margin: 2px 0;
    user-select: none;
}

.file-node {
    background: rgba(0,123,255,0.05);
    border-left: 3px solid #007bff;
    border-radius: 6px;
    margin: 1px 0;
    transition: all 0.2s ease;
}

.file-node:hover {
    background: rgba(0,123,255,0.15);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.folder-node:hover .node-content {
    background: rgba(255,193,7,0.1);
}

.node-content {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.node-content:hover {
    background: rgba(52, 152, 219, 0.1);
}

.folder-node .node-content {
    font-weight: 500;
}

.folder-node.selected .node-content {
    background: linear-gradient(135deg, #3498db, #1976d2);
    color: white;
}

.expand-icon {
    cursor: pointer;
    margin-right: 8px;
    transition: transform 0.2s ease;
    width: 16px;
    text-align: center;
}

.folder-children {
    border-left: 2px solid rgba(52, 152, 219, 0.2);
    margin-left: 15px;
    padding-left: 5px;
}

.tree-node .btn {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.tree-node:hover .btn {
    opacity: 1;
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 mb-0">
            <i class="fas fa-cog me-2"></i>Execute Public Tasks
        </h1>
        <p class="text-muted">Process cross-checked files (excluding private files)</p>
    </div>
</div>

<!-- Notification Area -->
<div id="notificationArea" class="row mb-3" style="display: none;">
    <div class="col-12">
        <div id="notificationAlert" class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <span id="notificationMessage">Loading...</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
</div>

<!-- Dashboard Tabs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="dashboardTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="queue-tab" data-bs-toggle="tab" data-bs-target="#queue-pane" type="button" role="tab">
                            <i class="fas fa-list me-2"></i>Files in Queue to be Processed
                            <span class="badge bg-primary ms-2" id="queueCount">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="processing-tab" data-bs-toggle="tab" data-bs-target="#processing-pane" type="button" role="tab">
                            <i class="fas fa-cogs me-2"></i>Processing
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="batch-audio-tab" data-bs-toggle="tab" data-bs-target="#batch-audio-pane" type="button" role="tab">
                            <i class="fas fa-music me-2"></i>Batch Audio Extraction
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="dashboardTabContent">
                    <!-- Queue Tab -->
                    <div class="tab-pane fade show active" id="queue-pane" role="tabpanel">
                        <!-- Navigation Tabs for Queue Views -->
                        <ul class="nav nav-pills mb-3" id="queueViewTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="table-view-tab" data-bs-toggle="tab" data-bs-target="#table-view-pane" type="button" role="tab">
                                    <i class="fas fa-table me-2"></i>Table View
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="tree-view-tab" data-bs-toggle="tab" data-bs-target="#tree-view-pane" type="button" role="tab">
                                    <i class="fas fa-folder-tree me-2"></i>Folder Tree View
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="queueViewTabContent">
                            <!-- Table View -->
                            <div class="tab-pane fade show active" id="table-view-pane" role="tabpanel">
                        <!-- Search and Filter Controls -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchFiles" placeholder="Search files...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="filterCategory">
                                    <option value="">All Categories</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="filterSize">
                                    <option value="">All Sizes</option>
                                    <option value="<1GB">&lt; 1GB</option>
                                    <option value="1-5GB">1-5GB</option>
                                    <option value="5-10GB">5-10GB</option>
                                    <option value=">10GB">&gt; 10GB</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control" id="filterDate" placeholder="Filter by date">
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>Clear
                                </button>
                            </div>
                        </div>

                        <!-- Files Table -->
                        <div id="filesTableContainer">
                            <div class="table-responsive">
                                <table class="table table-hover" id="filesTable">
                                    <thead>
                                        <tr>
                                            <th>
                                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                            </th>
                                            <th>File Name</th>
                                            <th>File Size</th>
                                            <th>Number of Files</th>
                                            <th>Assigned Date</th>
                                            <th>Category</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="filesTableBody">
                                        <!-- Files will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Batch Processing Controls -->
                        <div class="row mt-3" id="batchControls" style="display: none;">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6><i class="fas fa-layer-group me-2"></i>Batch Processing</h6>
                                        <div class="row">
                                            <div class="col-md-8">
                                                <span id="selectedCount">0</span> files selected
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <button class="btn btn-primary btn-sm" onclick="processBatch()">
                                                    <i class="fas fa-play me-1"></i>Process Selected
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                                    <i class="fas fa-times me-1"></i>Clear Selection
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div id="emptyState" class="text-center py-5" style="display: none;">
                            <i class="fas fa-tasks fa-4x text-primary mb-3"></i>
                            <h4>No Files to Process</h4>
                            <p class="text-muted">All available files have been processed or there are no files ready for processing.</p>
                        </div>
                            </div>

                            <!-- Tree View (EXACT MATCH - Archives Assignment Console) -->
                            <div class="tab-pane fade" id="tree-view-pane" role="tabpanel">
                                <!-- Folder Browser Section (EXACT COPY from Archives Console) -->
                                <div class="folder-browser">
                                    <h3 class="mb-4">
                                        <i class="fas fa-folder-tree me-2"></i>Executor Folder Browser
                                        <button class="btn btn-outline-primary btn-sm float-end" onclick="refreshFolders()">
                                            <i class="fas fa-refresh me-1"></i>Refresh
                                        </button>
                                    </h3>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="folder-tree" id="folderTree">
                                                <div class="text-center py-4">
                                                    <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                                                    <p class="mt-2">Loading folders...</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="selected-folders-panel">
                                                <h5><i class="fas fa-check-square me-2"></i>Selected Folders</h5>
                                                <div id="selectedFoldersList" class="selected-folders-list">
                                                    <p class="text-muted">No folders selected</p>
                                                </div>
                                                <div class="mt-3">
                                                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="selectAllFolders()">
                                                        <i class="fas fa-check-double me-1"></i>Select All
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                                        <i class="fas fa-times me-1"></i>Clear All
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Processing Tab -->
                    <div class="tab-pane fade" id="processing-pane" role="tabpanel">
                        <div id="processingContent">
                            <div class="text-center py-5">
                                <i class="fas fa-cogs fa-4x text-muted mb-3"></i>
                                <h4>No Active Processing</h4>
                                <p class="text-muted">Select files from the queue to start processing.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Batch Audio Extraction Tab -->
                    <div class="tab-pane fade" id="batch-audio-pane" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-music me-2"></i>Batch Audio Extraction
                                </h5>
                                <p class="text-muted mb-0">Extract audio from multiple video files simultaneously</p>
                            </div>
                            <div class="card-body">
                                <!-- Video File Selection -->
                                <div class="row mb-4">
                                    <div class="col-md-8">
                                        <label class="form-label">Select Multiple Video Files</label>
                                        <div class="input-group">
                                            <input type="file" class="form-control" id="batchVideoFileInput"
                                                   accept=".mp4,.avi,.mov,.mkv,.wmv,.flv,.webm" multiple>
                                            <button type="button" class="btn btn-primary" onclick="addBatchVideoFiles()">
                                                <i class="fas fa-plus me-1"></i>Add Videos
                                            </button>
                                        </div>
                                        <small class="text-muted">Select multiple video files from any location for batch audio extraction</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Actions</label>
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-success" onclick="startBatchExtraction()" disabled id="startBatchBtn">
                                                <i class="fas fa-play me-1"></i>Start Batch Extraction
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" onclick="clearBatchList()">
                                                <i class="fas fa-trash me-1"></i>Clear All
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Selected Videos List -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card border-info">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-list me-2"></i>Selected Videos for Batch Extraction
                                                    <span class="badge bg-light text-dark ms-2" id="batchVideoCount">0</span>
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="batchVideosList" class="row">
                                                    <div class="col-12 text-center text-muted py-4">
                                                        <i class="fas fa-video fa-3x mb-3"></i>
                                                        <p>No videos selected for batch extraction</p>
                                                        <small>Use the file input above to select multiple video files</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Batch Progress -->
                                <div id="batchExtractionProgress" class="mb-4" style="display: none;">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">
                                                <i class="fas fa-cogs me-2"></i>Batch Extraction Progress
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <!-- Overall Progress -->
                                            <div class="mb-3">
                                                <label class="form-label">Overall Progress</label>
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                         role="progressbar" id="batchOverallProgress" style="width: 0%">0%</div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-md-6">
                                                        <small class="text-muted" id="batchProgressText">Ready to start...</small>
                                                    </div>
                                                    <div class="col-md-6 text-end">
                                                        <small class="text-info" id="batchTimeRemaining">Estimated time: Calculating...</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Current File Progress -->
                                            <div class="mb-3">
                                                <label class="form-label">Current File: <span id="currentFileName">--</span></label>
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar" id="currentFileProgress" style="width: 0%">0%</div>
                                                </div>
                                            </div>

                                            <!-- Status Log -->
                                            <div class="mb-3">
                                                <label class="form-label">Status Log</label>
                                                <div id="batchStatusLog" class="border rounded p-2 bg-light" style="height: 150px; overflow-y: auto;">
                                                    <!-- Status messages will appear here -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Results Summary -->
                                <div id="batchResults" class="mb-4" style="display: none;">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-check-circle me-2"></i>Batch Extraction Results
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="text-center">
                                                        <h4 class="text-success" id="successCount">0</h4>
                                                        <small class="text-muted">Successful Extractions</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="text-center">
                                                        <h4 class="text-danger" id="failureCount">0</h4>
                                                        <small class="text-muted">Failed Extractions</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="text-center">
                                                        <h4 class="text-info" id="totalProcessed">0</h4>
                                                        <small class="text-muted">Total Processed</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <button type="button" class="btn btn-primary" onclick="openCrossCheckFolder()">
                                                    <i class="fas fa-folder-open me-1"></i>Open Cross Check Folder
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" onclick="downloadBatchReport()">
                                                    <i class="fas fa-download me-1"></i>Download Report
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Comprehensive Metadata Processing Modal -->
<div class="modal fade" id="metadataModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Process File - Complete Metadata Entry
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- File Information Panel -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-folder me-2"></i>File Information</h6>
                                        <p><strong>Folder:</strong> <span id="currentFolderName">--</span></p>
                                        <p><strong>Category:</strong> <span id="currentCategory">--</span></p>
                                        <p><strong>Path:</strong> <small id="currentFolderPath">--</small></p>
                                        <p><strong>Video IDs:</strong> <span id="currentVideoIds">--</span></p>
                                        <p><strong>Assigner Remarks:</strong> <span id="currentAssignerRemarks">--</span></p>
                                    </div>
                                    <div class="col-md-3">
                                        <h6><i class="fas fa-chart-bar me-2"></i>Auto-Detected</h6>
                                        <p><strong>Files:</strong> <span id="fileCount">--</span></p>
                                        <p><strong>Size:</strong> <span id="totalSize">--</span></p>
                                        <p><strong>Duration:</strong> <span id="detectedDuration">--</span></p>
                                    </div>
                                    <div class="col-md-3">
                                        <h6><i class="fas fa-video me-2"></i>Video Info</h6>
                                        <p><strong>Resolution:</strong> <span id="detectedResolution">--</span></p>
                                        <p><strong>Format:</strong> <span id="detectedFormat">--</span></p>
                                        <button class="btn btn-sm btn-outline-primary" onclick="previewFile()">
                                            <i class="fas fa-play me-1"></i>Preview
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Metadata Entry Form -->
                <form id="metadataForm">
                    <input type="hidden" id="currentQueueItemId">

                    <!-- Navigation Tabs for Form Sections -->
                    <ul class="nav nav-tabs mb-3" id="metadataFormTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general-metadata" type="button" role="tab">
                                <i class="fas fa-info-circle me-1"></i>General
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="audio-tab" data-bs-toggle="tab" data-bs-target="#audio-metadata" type="button" role="tab">
                                <i class="fas fa-microphone me-1"></i>Audio/Transcript
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social-metadata" type="button" role="tab">
                                <i class="fas fa-share-alt me-1"></i>Social Media
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="metadataFormTabContent">
                        <!-- General Metadata Tab -->
                        <div class="tab-pane fade show active" id="general-metadata" role="tabpanel">
                            <div class="row">
                                <!-- Basic Information -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3"><i class="fas fa-file-alt me-2"></i>Basic Information</h6>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            OCD/VP Number <span class="text-danger">*</span>
                                            <i class="fas fa-info-circle text-muted" title="Unique identifier for internal tracking" data-bs-toggle="tooltip"></i>
                                        </label>
                                        <input type="text" class="form-control" id="ocdVpNumber" required>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            Edited File Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="editedFileName" required>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    Language <span class="text-danger">*</span>
                                                </label>
                                                <select class="form-select" id="language" required>
                                                    <option value="">Select Language</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    Edited Year <span class="text-danger">*</span>
                                                </label>
                                                <input type="number" class="form-control" id="editedYear" min="2000" max="2030" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Trim Closed Date</label>
                                                <input type="date" class="form-control" id="trimClosedDate">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Total Duration</label>
                                                <input type="text" class="form-control" id="totalDuration" placeholder="e.g., 5:30">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Content Classification -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3"><i class="fas fa-tags me-2"></i>Content Classification</h6>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            Type of Video <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="videoType" required>
                                            <option value="">Select Video Type</option>
                                        </select>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    Edited in <span class="text-danger">*</span>
                                                </label>
                                                <select class="form-select" id="editedLocation" required>
                                                    <option value="">Select Location</option>
                                                    <option value="Ashram">Ashram</option>
                                                    <option value="US">US</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    Component <span class="text-danger">*</span>
                                                </label>
                                                <select class="form-select" id="component" required>
                                                    <option value="">Select Component</option>
                                                    <option value="Sadhguru">Sadhguru</option>
                                                    <option value="Non-Sadhguru">Non-Sadhguru</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            Published Platforms <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="publishedPlatforms" multiple required>
                                        </select>
                                        <div class="form-text">Hold Ctrl/Cmd to select multiple platforms</div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    Department <span class="text-danger">*</span>
                                                </label>
                                                <input type="text" class="form-control" id="departmentName" required placeholder="Enter department name">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    Access Level <span class="text-danger">*</span>
                                                </label>
                                                <select class="form-select" id="accessLevel" required>
                                                    <option value="">Select Access</option>
                                                    <option value="Public">Public</option>
                                                    <option value="Private">Private</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <!-- Content Tags -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            Content Tags <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="contentTags" multiple required>
                                        </select>
                                        <div class="form-text">Select one or more content tags</div>
                                    </div>
                                </div>

                                <!-- Technical Information -->
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    Backup Type <span class="text-danger">*</span>
                                                </label>
                                                <select class="form-select" id="backupType" required>
                                                    <option value="">Select Backup Type</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Software Show/Renamed Folder</label>
                                                <input type="text" class="form-control" id="softwareShowName" placeholder="Video, Audio, Transcript">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Audio/Transcript Metadata Tab -->
                        <div class="tab-pane fade" id="audio-metadata" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3"><i class="fas fa-microphone me-2"></i>Audio Information</h6>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            Audio Code <span class="text-danger">*</span>
                                            <i class="fas fa-info-circle text-muted" title="Format: AUD-YYYY-NNN" data-bs-toggle="tooltip"></i>
                                        </label>
                                        <input type="text" class="form-control" id="audioCode" required>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Audio File Name</label>
                                        <input type="text" class="form-control" id="audioFileName" placeholder="Auto-generated from folder name">
                                    </div>

                                    <!-- Enhanced Audio File Selection & Extraction -->
                                    <div class="mb-3">
                                        <div class="card border-warning">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0"><i class="fas fa-music me-2"></i>Audio File Selection & Extraction</h6>
                                            </div>
                                            <div class="card-body">
                                                <!-- Current Folder Audio Selection -->
                                                <div class="mb-3">
                                                    <label for="selectedAudioFile" class="form-label">Select Audio File from Current Folder</label>
                                                    <div class="input-group">
                                                        <select class="form-select" id="selectedAudioFile" name="selected_audio_file">
                                                            <option value="">Select an audio file...</option>
                                                        </select>
                                                        <button type="button" class="btn btn-outline-primary" onclick="browseAudioFiles()" id="browseAudioBtn">
                                                            <i class="fas fa-folder-open me-1"></i>Browse Current
                                                        </button>
                                                    </div>
                                                    <small class="text-muted">Browse and select audio files from the current folder</small>
                                                </div>

                                                <!-- Enhanced Video File Selection -->
                                                <div class="mb-3">
                                                    <label class="form-label">Select Video Files from Any Location</label>
                                                    <div class="input-group">
                                                        <input type="file" class="form-control" id="videoFileInput"
                                                               accept=".mp4,.avi,.mov,.mkv,.wmv,.flv,.webm" multiple>
                                                        <button type="button" class="btn btn-outline-info" onclick="selectVideoFiles()">
                                                            <i class="fas fa-video me-1"></i>Select Videos
                                                        </button>
                                                    </div>
                                                    <small class="text-muted">Select one or multiple video files from any location for audio extraction</small>
                                                </div>

                                                <!-- Selected Videos Display -->
                                                <div id="selectedVideosDisplay" class="mb-3" style="display: none;">
                                                    <label class="form-label">Selected Videos:</label>
                                                    <div id="selectedVideosList" class="border rounded p-2 bg-light">
                                                        <!-- Selected videos will be displayed here -->
                                                    </div>
                                                </div>

                                                <!-- Audio File Actions -->
                                                <div class="row mb-3">
                                                    <div class="col-md-3">
                                                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="loadAudioFiles()">
                                                            <i class="fas fa-refresh me-1"></i>Refresh
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="previewSelectedAudio()" disabled id="previewAudioBtn">
                                                            <i class="fas fa-play me-1"></i>Preview
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button type="button" class="btn btn-warning btn-sm w-100" onclick="extractAudioFileWithProgress()" disabled id="extractAudioBtn">
                                                            <i class="fas fa-file-export me-1"></i>Extract Audio
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button type="button" class="btn btn-success btn-sm w-100" onclick="extractFromSelectedVideos()" disabled id="extractVideosBtn">
                                                            <i class="fas fa-film me-1"></i>Extract from Videos
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Enhanced Progress Bar -->
                                                <div id="audioExtractionProgress" class="mb-3" style="display: none;">
                                                    <label class="form-label">Extraction Progress</label>
                                                    <div class="progress">
                                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                             role="progressbar"
                                                             id="audioProgressBar"
                                                             style="width: 0%">0%</div>
                                                    </div>
                                                    <div class="row mt-2">
                                                        <div class="col-md-6">
                                                            <small class="text-muted" id="audioProgressText">Ready to extract...</small>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <small class="text-info" id="timeRemaining">Estimated time: Calculating...</small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Status Display -->
                                                <div id="audioExtractionStatus" class="mt-2"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3"><i class="fas fa-file-alt me-2"></i>Transcript Information</h6>

                                    <div class="mb-3">
                                        <label class="form-label">Transcription File Name</label>
                                        <input type="text" class="form-control" id="transcriptionFileName" placeholder="Auto-generated or uploaded">
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Transcription Status</label>
                                                <select class="form-select" id="transcriptionStatus">
                                                    <option value="Pending">Pending</option>
                                                    <option value="In Progress">In Progress</option>
                                                    <option value="Completed">Completed</option>
                                                    <option value="Not Required">Not Required</option>
                                                </select>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Social Media Metadata Tab -->
                        <div class="tab-pane fade" id="social-metadata" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3"><i class="fas fa-calendar me-2"></i>Publishing Information</h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Published Date</label>
                                                <input type="date" class="form-control" id="publishedDate">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    Video ID <span class="text-danger">*</span>
                                                    <i class="fas fa-info-circle text-muted" title="Format: VID-YYYY-NNN" data-bs-toggle="tooltip"></i>
                                                </label>
                                                <input type="text" class="form-control" id="videoId" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            Social Media Title <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="socialMediaTitle" required>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Duration</label>
                                        <input type="text" class="form-control" id="durationCategory" placeholder="Enter duration (e.g., 5:30, 2 minutes)">
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3"><i class="fas fa-share-alt me-2"></i>Content Details</h6>

                                    <div class="mb-3">
                                        <label class="form-label">Description</label>
                                        <textarea class="form-control" id="description" rows="4" placeholder="Enter description or select from predefined snippets"></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Social Media URL</label>
                                        <input type="url" class="form-control" id="socialMediaUrl" placeholder="https://...">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Modal Footer with Action Buttons -->
            <div class="modal-footer">
                <div class="row w-100">
                    <div class="col-md-6">
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                                <i class="fas fa-save me-1"></i>Save Draft
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="copyMetadata()">
                                <i class="fas fa-copy me-1"></i>Copy to All
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-success" onclick="processFileWithMetadata()">
                                <i class="fas fa-check me-1"></i>Process & Move to Cross-Check
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Details Modal -->
<div class="modal fade" id="fileDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>File Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="fileDetailsContent">
                <!-- File details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentFileId = null;
let metadataOptions = {};
let selectedFiles = [];

$(document).ready(function() {
    // Initialize the dashboard
    loadFilesQueue();
    loadMetadataOptions();

    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Set current year as default
    $('#editedYear').val(new Date().getFullYear());

    // Auto-update file name based on folder name
    $('#currentFolderName').on('DOMSubtreeModified', function() {
        if (!$('#editedFileName').val()) {
            $('#editedFileName').val($(this).text());
        }
    });
});

function loadFilesQueue() {
    console.log('Loading files queue...');

    $('#filesTableBody').html(`
        <tr>
            <td colspan="7" class="text-center py-4">
                <div class="loading"></div>
                <p>Loading files...</p>
            </td>
        </tr>
    `);

    $.ajax({
        url: '/api/executive-public/queue',
        method: 'GET',
        timeout: 10000,
        success: function(response) {
            console.log('Queue API response:', response);

            if (response && response.success) {
                console.log('Files loaded:', response.files.length);
                displayFiles(response.files);
                updateQueueCount(response.files.length);
            } else {
                console.error('Queue API error:', response);
                showAlert(`Error loading files: ${response ? response.error : 'Unknown error'}`, 'danger');
                showEmptyState();
            }
        },
        error: function(xhr, status, error) {
            console.error('Queue API AJAX error:', {xhr, status, error});

            let errorMessage = 'Failed to load files. ';
            if (status === 'timeout') {
                errorMessage += 'Request timed out.';
            } else if (xhr.status === 403) {
                errorMessage += 'Access denied.';
            } else if (xhr.status === 500) {
                errorMessage += 'Server error.';
            } else {
                errorMessage += `Error: ${error}`;
            }

            showAlert(errorMessage + ' Please refresh the page.', 'danger');
            showEmptyState();
        }
    });
}

function displayFiles(files) {
    if (files.length === 0) {
        showEmptyState();
        return;
    }

    $('#emptyState').hide();
    $('#filesTableContainer').show();

    let html = '';
    files.forEach(file => {
        html += `
            <tr id="file-row-${file.queue_item_id}">
                <td>
                    <input type="checkbox" class="file-checkbox" value="${file.queue_item_id}" onchange="updateSelection()">
                </td>
                <td>
                    <i class="fas fa-folder me-2 text-primary"></i>
                    <strong>${file.folder_name}</strong>
                    <br>
                    <small class="text-muted">${file.folder_path}</small>
                </td>
                <td>
                    <span class="badge bg-info">${file.total_size_formatted || 'Calculating...'}</span>
                </td>
                <td>
                    <span class="badge bg-secondary">${file.file_count || 0} files</span>
                </td>
                <td class="format-date">${file.assigned_date}</td>
                <td>
                    <span class="badge bg-success">${file.category}</span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-primary btn-sm" onclick="processFileEnhanced(${file.queue_item_id})">
                            <i class="fas fa-edit me-1"></i>Process
                        </button>
                        <button class="btn btn-outline-primary btn-sm preview-btn"
                                data-folder-path="${file.folder_path}"
                                data-queue-id="${file.queue_item_id}">
                            <i class="fas fa-play me-1"></i>Preview
                        </button>
                        <button class="btn btn-info btn-sm details-btn"
                                data-queue-id="${file.queue_item_id}"
                                data-folder-name="${file.folder_name}"
                                data-folder-path="${file.folder_path}">
                            <i class="fas fa-info me-1"></i>Details
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    $('#filesTableBody').html(html);

    // Format dates
    $('.format-date').each(function() {
        const text = $(this).text();
        if (text && text !== 'None') {
            $(this).text(formatDate(text));
        }
    });

    // Add event listeners for Preview and Details buttons (safer than onclick with complex paths)
    $('.preview-btn').off('click').on('click', function() {
        const folderPath = $(this).data('folder-path');
        const queueId = $(this).data('queue-id');
        console.log('Preview button clicked:', {folderPath, queueId});
        previewFileInModal(folderPath);
    });

    $('.details-btn').off('click').on('click', function() {
        const queueId = $(this).data('queue-id');
        const folderName = $(this).data('folder-name');
        const folderPath = $(this).data('folder-path');
        console.log('Details button clicked:', {queueId, folderName, folderPath});
        showFileDetails(queueId, folderName, folderPath);
    });
}

// Enhanced process function that handles errors properly
function processFileEnhanced(queueItemId) {
    try {
        console.log('Processing file with queue item ID:', queueItemId);
        currentFileId = queueItemId;

        // Show loading state
        showAlert('Loading file details...', 'info');

        // Load file details and show metadata modal
        loadFileDetails(queueItemId);
    } catch (error) {
        console.error('Error in processFileEnhanced:', error);
        showAlert('Error loading file details. Please try again.', 'danger');
    }
}

function loadMetadataOptions() {
    $.ajax({
        url: '/api/executive-public/metadata-options',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                metadataOptions = response.options;
                populateDropdowns();
            }
        },
        error: function() {
            console.error('Failed to load metadata options');
        }
    });
}

function populateDropdowns() {
    // Populate all dropdown fields
    populateSelect('#language', metadataOptions.languages);
    populateSelect('#videoType', metadataOptions.video_types);
    populateSelect('#publishedPlatforms', metadataOptions.published_platforms);
    // Note: departmentName is now a text input, not a dropdown
    populateSelect('#contentTags', metadataOptions.content_tags);
    populateSelect('#backupType', metadataOptions.backup_types);
}

function populateSelect(selector, options) {
    const $select = $(selector);
    const isMultiple = $select.prop('multiple');

    if (!isMultiple) {
        // Keep the first option (usually "Select...")
        $select.find('option:not(:first)').remove();
    } else {
        $select.empty();
    }

    options.forEach(option => {
        $select.append(`<option value="${option}">${option}</option>`);
    });
}

function processFile(queueItemId) {
    currentFileId = queueItemId;

    // Load file details and show metadata modal
    loadFileDetails(queueItemId);
}

function loadFileDetails(queueItemId) {
    try {
        console.log('Loading file details for queue item:', queueItemId);

        // Show loading in modal
        $('#currentFolderName').text('Loading...');
        $('#currentCategory').text('Loading...');
        $('#currentFolderPath').text('Loading...');
        $('#fileCount').text('--');
        $('#totalSize').text('--');
        $('#detectedDuration').text('--');
        $('#detectedResolution').text('--');
        $('#detectedFormat').text('--');

        // Clear form
        if ($('#metadataForm')[0]) {
            $('#metadataForm')[0].reset();
        }
        $('#currentQueueItemId').val(queueItemId);

        $.ajax({
            url: `/api/file-details/${queueItemId}`,
            method: 'GET',
            timeout: 10000, // 10 second timeout
            success: function(response) {
                try {
                    console.log('File details response:', response);

                    if (response && response.success) {
                        const details = response.file_details;

                        // Store current file details for audio functions
                        currentFile = {
                            folder_path: details.folder_path,
                            folder_name: details.folder_name,
                            queue_item_id: queueItemId
                        };

                        // Update file information display
                        $('#currentFolderName').text(details.folder_name || 'Unknown');
                        $('#currentCategory').text(details.category || 'Unknown');
                        $('#currentFolderPath').text(details.folder_path || 'Unknown');
                        $('#currentVideoIds').text(details.video_ids || 'Not specified');
                        $('#currentAssignerRemarks').text(details.remarks || 'No remarks');
                        $('#fileCount').text(details.file_count || 0);
                        $('#totalSize').text(details.total_size_formatted || 'Unknown');
                        $('#detectedDuration').text(details.detected_duration || 'Unknown');
                        $('#detectedResolution').text(details.detected_resolution || 'Unknown');
                        $('#detectedFormat').text(details.detected_format || 'Unknown');

                        // Pre-fill suggested codes
                        if (details.suggested_codes) {
                            $('#ocdVpNumber').val(details.suggested_codes.ocd_vp_number || '');
                            $('#audioCode').val(details.suggested_codes.audio_code || '');
                            $('#videoId').val(details.suggested_codes.video_id || '');
                        }

                        // Pre-fill file name
                        $('#editedFileName').val(details.folder_name || '');
                        $('#audioFileName').val((details.folder_name || '') + '_Audio');
                        $('#transcriptionFileName').val((details.folder_name || '') + '_Transcript');
                        $('#socialMediaTitle').val(details.folder_name || '');

                        // Load draft if exists
                        if (details.has_draft && details.draft_metadata) {
                            loadDraftMetadata(details.draft_metadata);
                        }

                        // Show the modal
                        $('#metadataModal').modal('show');
                        showAlert('File details loaded successfully', 'success');

                        // Auto-load audio files
                        setTimeout(() => {
                            loadAudioFiles();
                        }, 1000);
                    } else {
                        console.error('API returned error:', response);
                        showAlert(`Error loading file details: ${response ? response.error : 'Unknown error'}`, 'danger');
                    }
                } catch (error) {
                    console.error('Error processing response:', error);
                    showAlert('Error processing file details response', 'danger');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', {xhr, status, error});
                let errorMessage = 'Failed to load file details. ';

                if (status === 'timeout') {
                    errorMessage += 'Request timed out.';
                } else if (xhr.status === 404) {
                    errorMessage += 'File not found.';
                } else if (xhr.status === 403) {
                    errorMessage += 'Access denied.';
                } else if (xhr.status === 500) {
                    errorMessage += 'Server error.';
                } else {
                    errorMessage += `Error: ${error}`;
                }

                showAlert(errorMessage + ' Please try again.', 'danger');
            }
        });
    } catch (error) {
        console.error('Error in loadFileDetails:', error);
        showAlert('Error initializing file details loading', 'danger');
    }
}

function loadDraftMetadata(draft) {
    // Load all draft values into form
    Object.keys(draft).forEach(key => {
        const element = $(`#${camelCase(key)}`);
        if (element.length && draft[key]) {
            if (element.prop('multiple')) {
                // Handle multi-select
                const values = draft[key].split(',').map(v => v.trim());
                element.val(values);
            } else {
                element.val(draft[key]);
            }
        }
    });

    showAlert('Draft metadata loaded successfully', 'info');
}

function camelCase(str) {
    return str.replace(/_([a-z])/g, function(g) { return g[1].toUpperCase(); });
}

function saveDraft() {
    const metadata = collectMetadata();

    $.ajax({
        url: '/api/save-metadata-draft',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            queue_item_id: $('#currentQueueItemId').val(),
            metadata: metadata
        }),
        success: function(response) {
            if (response.success) {
                showAlert('Draft saved successfully', 'success');
            } else {
                showAlert(`Error saving draft: ${response.error}`, 'danger');
            }
        },
        error: function() {
            showAlert('Failed to save draft. Please try again.', 'danger');
        }
    });
}

function collectMetadata() {
    const metadata = {
        folder_path: $('#currentFolderPath').text(),
        ocd_vp_number: $('#ocdVpNumber').val(),
        edited_file_name: $('#editedFileName').val(),
        language: $('#language').val(),
        edited_year: $('#editedYear').val(),
        trim_closed_date: $('#trimClosedDate').val(),
        total_duration: $('#totalDuration').val(),
        video_type: $('#videoType').val(),
        edited_location: $('#editedLocation').val(),
        published_platforms: $('#publishedPlatforms').val() ? $('#publishedPlatforms').val().join(', ') : '',
        department_name: $('#departmentName').val(),
        component: $('#component').val(),
        content_tags: $('#contentTags').val() ? $('#contentTags').val().join(', ') : '',
        backup_type: $('#backupType').val(),
        access_level: $('#accessLevel').val(),
        software_show_name: $('#softwareShowName').val(),
        audio_code: $('#audioCode').val(),
        audio_file_name: $('#audioFileName').val(),
        transcription_file_name: $('#transcriptionFileName').val(),
        transcription_status: $('#transcriptionStatus').val(),
        published_date: $('#publishedDate').val(),
        video_id: $('#videoId').val(),
        social_media_title: $('#socialMediaTitle').val(),
        description: $('#description').val(),
        social_media_url: $('#socialMediaUrl').val(),
        duration_category: $('#durationCategory').val(),
        processing_notes: $('#processingNotes').val()
    };

    return metadata;
}

function processFileWithMetadata() {
    // Validate required fields
    if (!validateForm()) {
        return;
    }

    const metadata = collectMetadata();

    // Show processing state
    const $btn = $('button[onclick="processFileWithMetadata()"]');
    $btn.prop('disabled', true).html(`
        <div class="loading me-1"></div>Processing...
    `);

    $.ajax({
        url: '/api/executive-public/process-metadata',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            queue_item_id: $('#currentQueueItemId').val(),
            metadata: metadata
        }),
        success: function(response) {
            if (response.success) {
                showAlert('File processed successfully and moved to cross-checker queue!', 'success');

                // Remove from table and close modal
                $(`#file-row-${currentFileId}`).fadeOut(500, function() {
                    $(this).remove();
                    if ($('#filesTableBody tr').length === 0) {
                        showEmptyState();
                    }
                });

                $('#metadataModal').modal('hide');
                updateQueueCount($('#filesTableBody tr').length - 1);
            } else {
                showAlert(`Error processing file: ${response.error}`, 'danger');
            }
        },
        error: function() {
            showAlert('Failed to process file. Please try again.', 'danger');
        },
        complete: function() {
            $btn.prop('disabled', false).html(`
                <i class="fas fa-check me-1"></i>Process & Move to Cross-Check
            `);
        }
    });
}

function validateForm() {
    const requiredFields = [
        '#ocdVpNumber', '#editedFileName', '#language', '#editedYear',
        '#videoType', '#editedLocation', '#component', '#publishedPlatforms',
        '#departmentName', '#accessLevel', '#contentTags', '#backupType',
        '#audioCode', '#videoId', '#socialMediaTitle'
    ];

    let isValid = true;

    requiredFields.forEach(field => {
        const $field = $(field);
        if (!$field.val() || ($field.prop('multiple') && $field.val().length === 0)) {
            $field.addClass('is-invalid');
            isValid = false;
        } else {
            $field.removeClass('is-invalid');
        }
    });

    if (!isValid) {
        showAlert('Please fill in all required fields (marked with *)', 'warning');
    }

    return isValid;
}

function showFileDetails(fileId, filename, filepath) {
    $('#fileDetailsContent').html(`
        <div class="text-center py-4">
            <div class="loading"></div>
            <p>Loading file details...</p>
        </div>
    `);
    
    $('#fileDetailsModal').modal('show');
    
    // Simulate loading file details
    setTimeout(() => {
        $('#fileDetailsContent').html(`
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-file me-2"></i>File Information</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>${filename}</td>
                        </tr>
                        <tr>
                            <td><strong>Path:</strong></td>
                            <td><small>${filepath}</small></td>
                        </tr>
                        <tr>
                            <td><strong>File ID:</strong></td>
                            <td>${fileId}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-eye me-2"></i>Preview</h6>
                    <div class="text-center p-4 bg-light rounded">
                        <i class="fas fa-file-video fa-3x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Video preview not available</p>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6><i class="fas fa-history me-2"></i>Processing History</h6>
                    <div class="timeline">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            File has been cross-checked and is ready for processing.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6><i class="fas fa-tools me-2"></i>Processing Guidelines</h6>
                    <div class="alert alert-info">
                        <ul class="mb-0">
                            <li>Ensure video quality meets standards</li>
                            <li>Check audio synchronization</li>
                            <li>Verify output format requirements</li>
                            <li>Add processing notes for future reference</li>
                        </ul>
                    </div>
                </div>
            </div>
        `);
    }, 1000);
}

// Utility Functions
function updateSelection() {
    selectedFiles = $('.file-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    $('#selectedCount').text(selectedFiles.length);

    if (selectedFiles.length > 0) {
        $('#batchControls').show();
    } else {
        $('#batchControls').hide();
    }
}

function toggleSelectAll() {
    const isChecked = $('#selectAll').prop('checked');
    $('.file-checkbox').prop('checked', isChecked);
    updateSelection();
}

function clearSelection() {
    $('.file-checkbox').prop('checked', false);
    $('#selectAll').prop('checked', false);
    updateSelection();
}

function processBatch() {
    if (selectedFiles.length === 0) {
        showAlert('Please select files to process', 'warning');
        return;
    }

    showAlert(`Processing ${selectedFiles.length} files...`, 'info');

    // Process each file individually
    selectedFiles.forEach((queueItemId, index) => {
        setTimeout(() => {
            processFile(queueItemId);
        }, index * 1000); // Stagger the processing
    });
}

function clearFilters() {
    $('#searchFiles').val('');
    $('#filterCategory').val('');
    $('#filterSize').val('');
    $('#filterDate').val('');
    loadFilesQueue(); // Reload without filters
}

function updateQueueCount(count) {
    $('#queueCount').text(count);
}

function showEmptyState() {
    $('#filesTableContainer').hide();
    $('#emptyState').show();
    updateQueueCount(0);
}

function previewFile() {
    const folderPath = $('#currentFolderPath').text();
    previewFileInModal(folderPath);
}

function previewFileInModal(folderPath) {
    console.log('Previewing files in folder:', folderPath);

    if (!folderPath || folderPath === '--' || folderPath === 'Unknown') {
        showAlert('No folder path available for preview', 'warning');
        return;
    }

    // Show loading message
    showAlert('Opening video files in VLC Media Player...', 'info');

    $.ajax({
        url: '/api/preview-video',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            folder_path: folderPath
        }),
        success: function(response) {
            console.log('Preview response:', response);

            if (response.success) {
                const fileCount = response.video_files ? response.video_files.length : 0;
                const fileList = response.video_files ? response.video_files.join(', ') : '';

                showAlert(
                    `✅ Opened ${fileCount} video file(s) in VLC: ${fileList}`,
                    'success'
                );
            } else {
                console.error('Preview error:', response.error);

                if (response.error.includes('VLC Media Player not found')) {
                    showAlert(
                        '❌ VLC Media Player not found. Please install VLC Media Player to preview videos. ' +
                        'Download from: https://www.videolan.org/vlc/',
                        'danger'
                    );
                } else if (response.error.includes('No video files found')) {
                    const message = response.video_files && response.video_files.length > 0
                        ? `No video files found in folder. Available files: ${response.video_files.join(', ')}`
                        : 'No video files found in the selected folder.';
                    showAlert(`⚠️ ${message}`, 'warning');
                } else {
                    showAlert(`❌ Preview failed: ${response.error}`, 'danger');
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Preview AJAX error:', {xhr, status, error});

            let errorMessage = 'Failed to preview video files. ';
            if (status === 'timeout') {
                errorMessage += 'Request timed out.';
            } else if (xhr.status === 403) {
                errorMessage += 'Access denied.';
            } else if (xhr.status === 500) {
                errorMessage += 'Server error.';
            } else {
                errorMessage += `Error: ${error}`;
            }

            showAlert(errorMessage, 'danger');
        }
    });
}

function copyMetadata() {
    // Copy current metadata to clipboard or to other files
    const metadata = collectMetadata();

    // Store in localStorage for copying to other files
    localStorage.setItem('copiedMetadata', JSON.stringify(metadata));
    showAlert('Metadata copied! You can paste it to other files.', 'success');
}

function pasteMetadata() {
    const copiedMetadata = localStorage.getItem('copiedMetadata');
    if (copiedMetadata) {
        const metadata = JSON.parse(copiedMetadata);
        loadDraftMetadata(metadata);
        showAlert('Metadata pasted successfully!', 'success');
    } else {
        showAlert('No metadata to paste. Copy metadata from another file first.', 'warning');
    }
}

function showAlert(message, type = 'info') {
    const alertClass = `alert-${type}`;
    const iconClass = type === 'success' ? 'fa-check-circle' :
                     type === 'danger' ? 'fa-exclamation-triangle' :
                     type === 'warning' ? 'fa-exclamation-circle' : 'fa-info-circle';

    $('#notificationMessage').text(message);
    $('#notificationAlert').removeClass('alert-info alert-success alert-danger alert-warning')
                           .addClass(alertClass);
    $('#notificationAlert i').removeClass().addClass(`fas ${iconClass} me-2`);
    $('#notificationArea').show();

    // Auto-hide after 5 seconds for success/info messages
    if (type === 'success' || type === 'info') {
        setTimeout(() => {
            $('#notificationArea').fadeOut();
        }, 5000);
    }
}

function hideAlert() {
    $('#notificationArea').fadeOut();
}

function formatDate(dateString) {
    if (!dateString || dateString === 'None') return 'N/A';
    try {
        return new Date(dateString).toLocaleString();
    } catch (e) {
        return dateString;
    }
}

// Search and Filter Functions
$('#searchFiles').on('input', function() {
    const searchTerm = $(this).val().toLowerCase();
    filterTable();
});

$('#filterCategory, #filterSize, #filterDate').on('change', function() {
    filterTable();
});

function filterTable() {
    const searchTerm = $('#searchFiles').val().toLowerCase();
    const categoryFilter = $('#filterCategory').val();
    const sizeFilter = $('#filterSize').val();
    const dateFilter = $('#filterDate').val();

    $('#filesTableBody tr').each(function() {
        const $row = $(this);
        const fileName = $row.find('td:nth-child(2)').text().toLowerCase();
        const category = $row.find('td:nth-child(6)').text();
        const size = $row.find('td:nth-child(3)').text();
        const date = $row.find('td:nth-child(5)').text();

        let show = true;

        // Search filter
        if (searchTerm && !fileName.includes(searchTerm)) {
            show = false;
        }

        // Category filter
        if (categoryFilter && !category.includes(categoryFilter)) {
            show = false;
        }

        // Size filter (simplified)
        if (sizeFilter) {
            // This would need more sophisticated size comparison
            if (!size.includes(sizeFilter.replace(/[<>]/g, ''))) {
                show = false;
            }
        }

        // Date filter
        if (dateFilter) {
            // This would need date comparison logic
        }

        $row.toggle(show);
    });
}

// EXACT COPY - Folder Tree Functionality from Archives Assignment Console
let selectedFolders = new Set();
let folderData = [];

// Load executor folder tree when tree view tab is clicked
$('#tree-view-tab').on('click', function() {
    loadFolders();
});

// Load folder structure (EXACT COPY from Archives Console)
async function loadFolders() {
    try {
        showAlert('Loading folders...', 'info');
        const response = await fetch('/api/executive-public/folder-tree');
        const data = await response.json();

        if (data.success) {
            folderData = data.folders;
            renderFolderTree(data.folders);
            updateStats();
            hideAlert();
        } else {
            showAlert('Error loading folders: ' + data.error, 'danger');
        }
    } catch (error) {
        showAlert('Error loading folders: ' + error.message, 'danger');
    }
}

// Refresh folders (EXACT COPY from Archives Console)
function refreshFolders() {
    loadFolders();
}

// Render folder tree (EXACT COPY from Archives Assignment Console)
function renderFolderTree(folders) {
    const treeContainer = document.getElementById('folderTree');
    treeContainer.innerHTML = '';

    if (!folders || folders.length === 0) {
        treeContainer.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-folder-open fa-2x mb-2"></i><p>No folders found</p></div>';
        return;
    }

    folders.forEach(folder => {
        const folderElement = createFolderElement(folder, 0);
        treeContainer.appendChild(folderElement);
    });
}

// Create folder or file element with enhanced display (EXACT COPY from Archives Assignment Console)
function createFolderElement(item, level) {
    const div = document.createElement('div');
    const isFile = item.type === 'file';
    div.className = `tree-node ${isFile ? 'file-node' : 'folder-node'}`;
    div.style.marginLeft = (level * 20) + 'px';

    const hasChildren = item.children && item.children.length > 0;

    if (isFile) {
        // File element with enhanced media detection
        const isMedia = item.is_media || false;
        const fileIcon = getFileIcon(item.extension, isMedia);
        const fileColor = isMedia ? 'text-success' : 'text-info';

        div.innerHTML = `
            <div class="node-content" data-path="${item.path}" data-type="file">
                <span class="expand-icon" style="visibility: hidden; width: 20px;"></span>
                <i class="${fileIcon} me-2 ${fileColor}"></i>
                <span class="file-name" title="${item.name}">${truncateFileName(item.name, 40)}</span>
                <small class="text-muted ms-2">(${item.size || 'Unknown'})</small>
                ${isMedia ? `
                    <button class="btn btn-sm btn-success ms-2" onclick="openFileInVLC('${escapeHtml(item.path)}')" title="Open in VLC">
                        <i class="fas fa-play"></i>
                    </button>
                ` : ''}
                <button class="btn btn-sm btn-outline-secondary ms-1" onclick="showFileInfo('${escapeHtml(item.path)}')" title="File Info">
                    <i class="fas fa-info"></i>
                </button>
            </div>
        `;
    } else {
        // Folder element with enhanced display
        const folderIcon = hasChildren ? 'fas fa-folder' : 'fas fa-folder-open';
        const mediaCount = item.media_count || item.file_count || 0;

        div.innerHTML = `
            <div class="node-content" data-path="${item.path}" data-type="folder">
                <span class="expand-icon" onclick="toggleFolder(this)" style="visibility: ${hasChildren ? 'visible' : 'hidden'}">
                    <i class="fas fa-chevron-right"></i>
                </span>
                <input type="checkbox" class="folder-checkbox" onchange="toggleFolderSelection('${escapeHtml(item.path)}', this.checked)">
                <i class="${folderIcon} me-2 text-warning"></i>
                <span class="folder-name" title="${item.name}">${truncateFileName(item.name, 35)}</span>
                <small class="text-muted ms-2">(${mediaCount} media files)</small>
                ${mediaCount > 0 ? `
                    <button class="btn btn-sm btn-warning ms-2" onclick="openFolderInVLC('${escapeHtml(item.path)}')" title="Open first media file in VLC">
                        <i class="fas fa-play"></i>
                    </button>
                ` : ''}
                <button class="btn btn-sm btn-outline-info ms-1" onclick="showFolderInfo('${escapeHtml(item.path)}')" title="Folder Info">
                    <i class="fas fa-info"></i>
                </button>
            </div>
        `;
    }

    if (hasChildren) {
        const childrenDiv = document.createElement('div');
        childrenDiv.className = 'folder-children';
        childrenDiv.style.display = 'none';

        item.children.forEach(child => {
            const childElement = createFolderElement(child, level + 1);
            childrenDiv.appendChild(childElement);
        });

        div.appendChild(childrenDiv);
    }

    return div;
}

// Helper functions for enhanced display (EXACT COPY from Archives Assignment Console)
function getFileIcon(extension, isMedia) {
    if (isMedia) {
        if (['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'].includes(extension)) {
            return 'fas fa-file-video';
        } else if (['.mp3', '.wav', '.m4a', '.aac', '.ogg'].includes(extension)) {
            return 'fas fa-file-audio';
        }
    }
    return 'fas fa-file';
}

function truncateFileName(name, maxLength) {
    if (name.length <= maxLength) return name;
    return name.substring(0, maxLength - 3) + '...';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Toggle folder expansion (EXACT COPY from Archives Assignment Console)
function toggleFolder(element) {
    const icon = element.querySelector('i');
    const nodeContent = element.parentElement;
    const treeNode = nodeContent.parentElement;
    const childrenDiv = treeNode.querySelector('.folder-children');

    if (childrenDiv) {
        const isExpanded = childrenDiv.style.display !== 'none';
        childrenDiv.style.display = isExpanded ? 'none' : 'block';
        icon.className = isExpanded ? 'fas fa-chevron-right' : 'fas fa-chevron-down';
    }
}

// Toggle folder selection (EXACT COPY from Archives Assignment Console)
function toggleFolderSelection(folderPath, isSelected) {
    if (isSelected) {
        selectedFolders.add(folderPath);
    } else {
        selectedFolders.delete(folderPath);
    }

    updateSelectedFoldersList();
    updateStats();

    // Update visual selection
    const nodeContent = document.querySelector(`[data-path="${folderPath}"]`);
    if (nodeContent) {
        const folderNode = nodeContent.parentElement;
        if (isSelected) {
            folderNode.classList.add('selected');
        } else {
            folderNode.classList.remove('selected');
        }
    }
}

// Update selected folders list (EXACT COPY from Archives Assignment Console)
function updateSelectedFoldersList() {
    const listContainer = document.getElementById('selectedFoldersList');

    if (selectedFolders.size === 0) {
        listContainer.innerHTML = '<p class="text-muted">No folders selected</p>';
        return;
    }

    let html = '';
    selectedFolders.forEach(folderPath => {
        const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();
        html += `
            <div class="selected-folder-item d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                <span class="folder-name">${folderName}</span>
                <button class="btn btn-sm btn-outline-danger" onclick="removeFolderSelection('${folderPath}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    });

    listContainer.innerHTML = html;
}

// Remove folder from selection (EXACT COPY from Archives Assignment Console)
function removeFolderSelection(folderPath) {
    selectedFolders.delete(folderPath);

    // Uncheck checkbox
    const checkbox = document.querySelector(`[data-path="${folderPath}"]`).parentElement.querySelector('.folder-checkbox');
    if (checkbox) {
        checkbox.checked = false;
    }

    // Remove visual selection
    const nodeContent = document.querySelector(`[data-path="${folderPath}"]`);
    if (nodeContent) {
        nodeContent.parentElement.classList.remove('selected');
    }

    updateSelectedFoldersList();
    updateStats();
}

// Select all folders (EXACT COPY from Archives Assignment Console)
function selectAllFolders() {
    const checkboxes = document.querySelectorAll('.folder-checkbox');
    checkboxes.forEach(checkbox => {
        if (!checkbox.checked) {
            checkbox.checked = true;
            const folderPath = checkbox.closest('.node-content').dataset.path;
            toggleFolderSelection(folderPath, true);
        }
    });
}

// Clear all selections (EXACT COPY from Archives Assignment Console)
function clearSelection() {
    const checkboxes = document.querySelectorAll('.folder-checkbox');
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            checkbox.checked = false;
            const folderPath = checkbox.closest('.node-content').dataset.path;
            toggleFolderSelection(folderPath, false);
        }
    });
}

// Update statistics (EXACT COPY from Archives Assignment Console)
function updateStats() {
    // Count only top-level folders (those with marginLeft: 0px)
    const allFolderCheckboxes = document.querySelectorAll('.folder-checkbox');
    let topLevelFolderCount = 0;

    allFolderCheckboxes.forEach(checkbox => {
        const treeNode = checkbox.closest('.tree-node');
        if (treeNode && treeNode.style.marginLeft === '0px') {
            topLevelFolderCount++;
        }
    });
}

// VLC integration functions (EXACT COPY from Archives Assignment Console)
function openFileInVLC(filePath) {
    showAlert('Opening file in VLC: ' + filePath, 'info');
    // Implement VLC integration
}

function openFolderInVLC(folderPath) {
    showAlert('Opening folder in VLC: ' + folderPath, 'info');
    // Implement VLC integration for first media file in folder
}

// File/folder info functions (EXACT COPY from Archives Assignment Console)
function showFileInfo(filePath) {
    showAlert('Showing file info: ' + filePath, 'info');
    // Implement file info modal
}

function showFolderInfo(folderPath) {
    showAlert('Showing folder info: ' + folderPath, 'info');
    // Implement folder info modal
}

// Enhanced Audio File Selection & Extraction Functions
let currentFolderPath = null;
let availableAudioFiles = [];
let currentFile = null; // Store current file details

// Browse audio files from current folder
async function browseAudioFiles() {
    // Get the actual folder path from the current file details
    const folderPath = getCurrentFolderPath();

    if (!folderPath) {
        showAlert('No folder selected. Please process a folder first.', 'warning');
        return;
    }

    try {
        showAlert('Browsing audio files...', 'info');
        console.log('Browsing audio files in:', folderPath);
        await loadAudioFiles();
    } catch (error) {
        console.error('Error browsing audio files:', error);
        showAlert('Failed to browse audio files', 'danger');
    }
}

// Get current folder path from various sources
function getCurrentFolderPath() {
    // Priority 1: From currentFile object
    if (currentFile && currentFile.folder_path) {
        console.log('Using currentFile.folder_path:', currentFile.folder_path);
        return currentFile.folder_path;
    }

    // Priority 2: From the displayed folder path in the modal
    const displayedPath = document.getElementById('currentFolderPath');
    if (displayedPath && displayedPath.textContent && displayedPath.textContent !== '--') {
        console.log('Using displayed folder path:', displayedPath.textContent);
        return displayedPath.textContent;
    }

    // Priority 3: From the folder name (construct path)
    const folderName = document.getElementById('currentFolderName');
    if (folderName && folderName.textContent && folderName.textContent !== '--') {
        const constructedPath = `T:\\To_Process\\Rough folder\\restored files\\${folderName.textContent}`;
        console.log('Using constructed path:', constructedPath);
        return constructedPath;
    }

    console.log('No valid folder path found');
    return null;
}

// Load audio files from current folder
async function loadAudioFiles() {
    const folderPath = getCurrentFolderPath();

    if (!folderPath) {
        showAlert('No folder selected. Please select a folder first.', 'warning');
        return;
    }

    try {
        showAlert('Loading audio files...', 'info');
        console.log('Loading audio files from:', folderPath);

        const response = await fetch('/api/get-audio-files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ folder_path: folderPath })
        });

        const data = await response.json();
        if (data.success) {
            availableAudioFiles = data.audio_files;
            populateAudioFileSelect(data.audio_files);
            showAlert(`Found ${data.audio_files.length} audio files in ${folderPath}`, 'success');
        } else {
            showAlert('Error loading audio files: ' + data.error, 'danger');
            console.error('Audio files API error:', data.error);
        }
    } catch (error) {
        console.error('Error loading audio files:', error);
        showAlert('Failed to load audio files', 'danger');
    }
}

// Populate audio file select dropdown
function populateAudioFileSelect(audioFiles) {
    const select = document.getElementById('selectedAudioFile');
    select.innerHTML = '<option value="">Select an audio file...</option>';

    audioFiles.forEach(file => {
        const option = document.createElement('option');
        option.value = file.path;
        option.textContent = `${file.name} (${file.size})`;
        select.appendChild(option);
    });

    // Enable/disable buttons based on availability
    const hasAudioFiles = audioFiles.length > 0;
    document.getElementById('previewAudioBtn').disabled = !hasAudioFiles;
    document.getElementById('extractAudioBtn').disabled = !hasAudioFiles;

    // Add change event listener
    select.addEventListener('change', function() {
        const isSelected = this.value !== '';
        document.getElementById('previewAudioBtn').disabled = !isSelected;
        document.getElementById('extractAudioBtn').disabled = !isSelected;
    });
}

// Preview selected audio file
function previewSelectedAudio() {
    const selectedPath = document.getElementById('selectedAudioFile').value;
    if (!selectedPath) {
        showAlert('Please select an audio file first', 'warning');
        return;
    }

    // Open audio file in VLC
    fetch('/api/open-vlc', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `file_path=${encodeURIComponent(selectedPath)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Audio file opened in VLC for preview', 'success');
        } else {
            showAlert('Error opening audio file: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error opening audio file:', error);
        showAlert('Failed to open audio file', 'danger');
    });
}

// Enhanced extract function with progress tracking
async function extractAudioFileWithProgress() {
    const selectedPath = document.getElementById('selectedAudioFile').value;
    const audioFileName = document.getElementById('audioFileName').value;

    if (!selectedPath) {
        showAlert('Please select an audio file first', 'warning');
        return;
    }

    if (!audioFileName) {
        showAlert('Please enter an audio file name', 'warning');
        return;
    }

    try {
        // Show progress bar
        document.getElementById('audioExtractionProgress').style.display = 'block';
        updateProgress(0, 'Initializing extraction...');

        showAlert('Starting audio extraction...', 'info');

        // Simulate progress steps
        updateProgress(20, 'Validating audio file...');
        await sleep(500);

        updateProgress(40, 'Preparing destination folder...');
        await sleep(500);

        updateProgress(60, 'Copying audio file...');

        const response = await fetch('/api/extract-audio', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                source_path: selectedPath,
                audio_file_name: audioFileName,
                folder_name: currentFile ? currentFile.folder_name : document.getElementById('currentFolderName').textContent
            })
        });

        updateProgress(80, 'Processing response...');
        await sleep(300);

        const data = await response.json();
        if (data.success) {
            updateProgress(100, 'Extraction completed successfully!');

            document.getElementById('audioExtractionStatus').innerHTML =
                '<div class="alert alert-success mb-0"><i class="fas fa-check me-2"></i>Audio extracted successfully!</div>';
            showAlert(`Audio file extracted to: ${data.destination_path}`, 'success');

            // Disable extract button to prevent duplicate extraction
            document.getElementById('extractAudioBtn').disabled = true;
            document.getElementById('extractAudioBtn').innerHTML =
                '<i class="fas fa-check me-1"></i>Extracted';

            // Hide progress bar after 3 seconds
            setTimeout(() => {
                document.getElementById('audioExtractionProgress').style.display = 'none';
            }, 3000);
        } else {
            updateProgress(0, 'Extraction failed!');
            document.getElementById('audioExtractionStatus').innerHTML =
                '<div class="alert alert-danger mb-0"><i class="fas fa-times me-2"></i>Extraction failed!</div>';
            showAlert('Error extracting audio: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error extracting audio:', error);
        updateProgress(0, 'Extraction failed!');
        document.getElementById('audioExtractionStatus').innerHTML =
            '<div class="alert alert-danger mb-0"><i class="fas fa-times me-2"></i>Extraction failed!</div>';
        showAlert('Failed to extract audio file', 'danger');
    }
}

// Helper function to update progress bar
function updateProgress(percentage, message) {
    const progressBar = document.getElementById('audioProgressBar');
    const progressText = document.getElementById('audioProgressText');

    progressBar.style.width = percentage + '%';
    progressBar.textContent = percentage + '%';
    progressText.textContent = message;

    // Update progress bar color based on status
    progressBar.className = 'progress-bar progress-bar-striped';
    if (percentage === 100) {
        progressBar.classList.add('bg-success');
    } else if (percentage === 0 && message.includes('failed')) {
        progressBar.classList.add('bg-danger');
    } else {
        progressBar.classList.add('progress-bar-animated');
    }
}

// Helper function for delays
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Legacy function for backward compatibility
async function extractAudioFile() {
    await extractAudioFileWithProgress();
}

// Auto-load audio files when a file is processed
function autoLoadAudioFiles() {
    if (currentFile && currentFile.folder_path) {
        loadAudioFiles();
    }
}

// Enhanced Video File Selection & Extraction Functions
let selectedVideoFiles = [];
let batchVideoFiles = [];

// Select video files for audio extraction
function selectVideoFiles() {
    const fileInput = document.getElementById('videoFileInput');
    const files = Array.from(fileInput.files);

    if (files.length === 0) {
        showAlert('Please select video files first', 'warning');
        return;
    }

    selectedVideoFiles = files;
    displaySelectedVideos();

    // Enable extract button
    document.getElementById('extractVideosBtn').disabled = false;

    showAlert(`Selected ${files.length} video files for audio extraction`, 'success');
}

// Display selected videos
function displaySelectedVideos() {
    const container = document.getElementById('selectedVideosList');

    if (selectedVideoFiles.length === 0) {
        container.innerHTML = '<small class="text-muted">No videos selected</small>';
        return;
    }

    let html = '';
    selectedVideoFiles.forEach((file, index) => {
        const sizeStr = formatFileSize(file.size);
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                <div>
                    <strong>${file.name}</strong>
                    <br><small class="text-muted">${sizeStr}</small>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="removeSelectedVideo(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    });

    container.innerHTML = html;
}

// Remove selected video
function removeSelectedVideo(index) {
    selectedVideoFiles.splice(index, 1);
    displaySelectedVideos();

    if (selectedVideoFiles.length === 0) {
        document.getElementById('extractVideosBtn').disabled = true;
    }
}

// Extract audio from selected videos
async function extractFromSelectedVideos() {
    if (selectedVideoFiles.length === 0) {
        showAlert('Please select video files first', 'warning');
        return;
    }

    const audioFileName = document.getElementById('audioFileName').value;
    if (!audioFileName) {
        showAlert('Please enter an audio file name', 'warning');
        return;
    }

    try {
        // Show progress
        document.getElementById('audioExtractionProgress').style.display = 'block';
        updateProgressWithTime(0, 'Starting video audio extraction...', selectedVideoFiles.length * 30);

        let successCount = 0;
        let failureCount = 0;

        for (let i = 0; i < selectedVideoFiles.length; i++) {
            const file = selectedVideoFiles[i];
            const currentProgress = Math.round((i / selectedVideoFiles.length) * 100);
            const remainingTime = (selectedVideoFiles.length - i) * 30;

            updateProgressWithTime(currentProgress, `Processing ${file.name}...`, remainingTime);

            try {
                // Create FormData for file upload
                const formData = new FormData();
                formData.append('video_file', file);
                formData.append('audio_file_name', `${audioFileName}_${i + 1}`);
                formData.append('folder_name', currentFile ? currentFile.folder_name : 'Unknown');

                const response = await fetch('/api/extract-audio-from-video', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                if (data.success) {
                    successCount++;
                } else {
                    failureCount++;
                    console.error(`Failed to extract audio from ${file.name}:`, data.error);
                }
            } catch (error) {
                failureCount++;
                console.error(`Error processing ${file.name}:`, error);
            }
        }

        // Complete
        updateProgressWithTime(100, 'Extraction completed!', 0);

        const message = `Extraction completed! Success: ${successCount}, Failed: ${failureCount}`;
        showAlert(message, successCount > 0 ? 'success' : 'warning');

        // Reset
        selectedVideoFiles = [];
        displaySelectedVideos();
        document.getElementById('extractVideosBtn').disabled = true;
        document.getElementById('videoFileInput').value = '';

        // Hide progress after 3 seconds
        setTimeout(() => {
            document.getElementById('audioExtractionProgress').style.display = 'none';
        }, 3000);

    } catch (error) {
        console.error('Error in batch extraction:', error);
        showAlert('Failed to extract audio from videos', 'danger');
    }
}

// Enhanced progress update with time estimation
function updateProgressWithTime(percentage, message, remainingSeconds = 0) {
    updateProgress(percentage, message);

    const timeElement = document.getElementById('timeRemaining');
    if (timeElement) {
        if (remainingSeconds > 0) {
            const minutes = Math.floor(remainingSeconds / 60);
            const seconds = remainingSeconds % 60;
            timeElement.textContent = `Estimated time: ${minutes}m ${seconds}s`;
        } else {
            timeElement.textContent = 'Estimated time: Complete';
        }
    }
}

// Batch Audio Extraction Functions
function addBatchVideoFiles() {
    const fileInput = document.getElementById('batchVideoFileInput');
    const files = Array.from(fileInput.files);

    if (files.length === 0) {
        showAlert('Please select video files first', 'warning');
        return;
    }

    // Add to batch list
    batchVideoFiles = [...batchVideoFiles, ...files];
    displayBatchVideosList();

    // Enable start button
    document.getElementById('startBatchBtn').disabled = false;

    showAlert(`Added ${files.length} video files to batch queue`, 'success');

    // Clear input
    fileInput.value = '';
}

// Display batch videos list
function displayBatchVideosList() {
    const container = document.getElementById('batchVideosList');
    const countElement = document.getElementById('batchVideoCount');

    countElement.textContent = batchVideoFiles.length;

    if (batchVideoFiles.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center text-muted py-4">
                <i class="fas fa-video fa-3x mb-3"></i>
                <p>No videos selected for batch extraction</p>
                <small>Use the file input above to select multiple video files</small>
            </div>
        `;
        return;
    }

    let html = '';
    batchVideoFiles.forEach((file, index) => {
        const sizeStr = formatFileSize(file.size);
        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-secondary">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1">${file.name}</h6>
                                <small class="text-muted">${sizeStr}</small>
                            </div>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeBatchVideo(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-secondary" id="batchStatus_${index}">Pending</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// Remove video from batch list
function removeBatchVideo(index) {
    batchVideoFiles.splice(index, 1);
    displayBatchVideosList();

    if (batchVideoFiles.length === 0) {
        document.getElementById('startBatchBtn').disabled = true;
    }
}

// Clear batch list
function clearBatchList() {
    batchVideoFiles = [];
    displayBatchVideosList();
    document.getElementById('startBatchBtn').disabled = true;

    // Hide progress and results
    document.getElementById('batchExtractionProgress').style.display = 'none';
    document.getElementById('batchResults').style.display = 'none';
}

// Start batch extraction
async function startBatchExtraction() {
    if (batchVideoFiles.length === 0) {
        showAlert('Please add video files to the batch queue first', 'warning');
        return;
    }

    try {
        // Show progress
        document.getElementById('batchExtractionProgress').style.display = 'block';
        document.getElementById('batchResults').style.display = 'none';

        // Disable start button
        document.getElementById('startBatchBtn').disabled = true;

        let successCount = 0;
        let failureCount = 0;
        const totalFiles = batchVideoFiles.length;
        const startTime = Date.now();

        // Clear status log
        const statusLog = document.getElementById('batchStatusLog');
        statusLog.innerHTML = '';

        addStatusLog('Starting batch audio extraction...', 'info');

        for (let i = 0; i < batchVideoFiles.length; i++) {
            const file = batchVideoFiles[i];
            const currentProgress = Math.round((i / totalFiles) * 100);
            const elapsedTime = (Date.now() - startTime) / 1000;
            const avgTimePerFile = elapsedTime / (i + 1);
            const remainingFiles = totalFiles - i - 1;
            const estimatedRemaining = Math.round(avgTimePerFile * remainingFiles);

            // Update overall progress
            updateBatchProgress(currentProgress, `Processing ${i + 1} of ${totalFiles}`, estimatedRemaining);

            // Update current file
            document.getElementById('currentFileName').textContent = file.name;
            updateCurrentFileProgress(0);

            // Update status
            const statusElement = document.getElementById(`batchStatus_${i}`);
            if (statusElement) {
                statusElement.className = 'badge bg-warning';
                statusElement.textContent = 'Processing...';
            }

            addStatusLog(`Processing: ${file.name}`, 'info');

            try {
                // Simulate file processing steps
                updateCurrentFileProgress(25);
                await sleep(200);

                updateCurrentFileProgress(50);
                await sleep(300);

                // Create FormData for file upload
                const formData = new FormData();
                formData.append('video_file', file);
                formData.append('audio_file_name', `BatchAudio_${Date.now()}_${i + 1}`);
                formData.append('folder_name', 'BatchExtraction');

                updateCurrentFileProgress(75);

                const response = await fetch('/api/extract-audio-from-video', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                updateCurrentFileProgress(100);

                if (data.success) {
                    successCount++;
                    addStatusLog(`✓ Successfully extracted audio from: ${file.name}`, 'success');

                    if (statusElement) {
                        statusElement.className = 'badge bg-success';
                        statusElement.textContent = 'Success';
                    }
                } else {
                    failureCount++;
                    addStatusLog(`✗ Failed to extract audio from: ${file.name} - ${data.error}`, 'error');

                    if (statusElement) {
                        statusElement.className = 'badge bg-danger';
                        statusElement.textContent = 'Failed';
                    }
                }
            } catch (error) {
                failureCount++;
                addStatusLog(`✗ Error processing: ${file.name} - ${error.message}`, 'error');

                if (statusElement) {
                    statusElement.className = 'badge bg-danger';
                    statusElement.textContent = 'Error';
                }
            }

            await sleep(100); // Small delay between files
        }

        // Complete
        updateBatchProgress(100, 'Batch extraction completed!', 0);
        document.getElementById('currentFileName').textContent = 'All files processed';
        updateCurrentFileProgress(100);

        addStatusLog(`Batch extraction completed! Success: ${successCount}, Failed: ${failureCount}`, 'info');

        // Show results
        showBatchResults(successCount, failureCount, totalFiles);

        // Re-enable start button
        document.getElementById('startBatchBtn').disabled = false;

        const message = `Batch extraction completed! Success: ${successCount}, Failed: ${failureCount}`;
        showAlert(message, successCount > 0 ? 'success' : 'warning');

    } catch (error) {
        console.error('Error in batch extraction:', error);
        showAlert('Failed to complete batch extraction', 'danger');
        document.getElementById('startBatchBtn').disabled = false;
    }
}

// Update batch progress
function updateBatchProgress(percentage, message, remainingSeconds = 0) {
    const progressBar = document.getElementById('batchOverallProgress');
    const progressText = document.getElementById('batchProgressText');
    const timeElement = document.getElementById('batchTimeRemaining');

    progressBar.style.width = percentage + '%';
    progressBar.textContent = percentage + '%';
    progressText.textContent = message;

    if (remainingSeconds > 0) {
        const minutes = Math.floor(remainingSeconds / 60);
        const seconds = remainingSeconds % 60;
        timeElement.textContent = `Estimated time: ${minutes}m ${seconds}s`;
    } else {
        timeElement.textContent = 'Estimated time: Complete';
    }
}

// Update current file progress
function updateCurrentFileProgress(percentage) {
    const progressBar = document.getElementById('currentFileProgress');
    progressBar.style.width = percentage + '%';
    progressBar.textContent = percentage + '%';
}

// Add status log entry
function addStatusLog(message, type = 'info') {
    const statusLog = document.getElementById('batchStatusLog');
    const timestamp = new Date().toLocaleTimeString();

    let iconClass = 'fas fa-info-circle';
    let textClass = 'text-info';

    switch (type) {
        case 'success':
            iconClass = 'fas fa-check-circle';
            textClass = 'text-success';
            break;
        case 'error':
            iconClass = 'fas fa-exclamation-circle';
            textClass = 'text-danger';
            break;
        case 'warning':
            iconClass = 'fas fa-exclamation-triangle';
            textClass = 'text-warning';
            break;
    }

    const logEntry = document.createElement('div');
    logEntry.className = `mb-1 ${textClass}`;
    logEntry.innerHTML = `
        <small>
            <i class="${iconClass} me-1"></i>
            [${timestamp}] ${message}
        </small>
    `;

    statusLog.appendChild(logEntry);
    statusLog.scrollTop = statusLog.scrollHeight;
}

// Show batch results
function showBatchResults(successCount, failureCount, totalProcessed) {
    document.getElementById('successCount').textContent = successCount;
    document.getElementById('failureCount').textContent = failureCount;
    document.getElementById('totalProcessed').textContent = totalProcessed;

    document.getElementById('batchResults').style.display = 'block';
}

// Open cross check folder
function openCrossCheckFolder() {
    fetch('/api/open-folder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            folder_path: 'T:\\To_Process\\Rough folder\\Folder to be cross checked'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Cross check folder opened', 'success');
        } else {
            showAlert('Error opening folder: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error opening folder:', error);
        showAlert('Failed to open cross check folder', 'danger');
    });
}

// Download batch report
function downloadBatchReport() {
    const statusLog = document.getElementById('batchStatusLog');
    const logContent = statusLog.textContent;

    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `batch_extraction_report_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
    showAlert('Batch report downloaded', 'success');
}

// Helper function to format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>
{% endblock %}
