#!/usr/bin/env python3
"""
Test script to verify access to the specific folder with large .mov files
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:5001"
TEST_USERNAME = "assigner"
TEST_PASSWORD = "Shiva@123"
SPECIFIC_FOLDER = "Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019/Output"

def test_login():
    """Test login functionality"""
    print("🔐 Testing login...")
    
    session = requests.Session()
    
    # Login
    login_data = {
        'username': TEST_USERNAME,
        'password': TEST_PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        print("✅ Login successful")
        return session
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def test_specific_folder_access(session):
    """Test access to the specific folder with .mov files"""
    print(f"📁 Testing access to specific folder: {SPECIFIC_FOLDER}")
    
    test_data = {'folder_path': SPECIFIC_FOLDER}
    response = session.post(
        f"{BASE_URL}/api/folder-contents/",
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Response data: {data}")
        
        if data.get('success'):
            files = data.get('files', [])
            print(f"✅ Successfully accessed folder")
            print(f"   Found {len(files)} files")
            
            # Check for .mov files specifically
            mov_files = [f for f in files if f['name'].lower().endswith('.mov')]
            print(f"   Found {len(mov_files)} .mov files")
            
            for file in mov_files:
                size_mb = file['size'] / (1024 * 1024)
                print(f"     • {file['name']} ({size_mb:.1f} MB)")
            
            return files
        else:
            print(f"❌ Folder access failed: {data.get('error')}")
            return []
    else:
        print(f"❌ Folder access API failed: {response.status_code}")
        try:
            error_data = response.json()
            print(f"Error details: {error_data}")
        except:
            print(f"Response text: {response.text}")
        return []

def test_large_file_detection(session):
    """Test large file detection API"""
    print("🔍 Testing large file detection...")
    
    response = session.get(f"{BASE_URL}/api/check-large-files")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            large_files = data.get('large_files', [])
            threshold = data.get('threshold_mb', 100)
            total = data.get('total_found', 0)
            
            print(f"✅ Large file detection working")
            print(f"   Threshold: {threshold}MB")
            print(f"   Total large files found: {total}")
            
            if large_files:
                print(f"   Top 10 largest files:")
                for file in large_files[:10]:
                    print(f"     • {file['name']} ({file['size_mb']} MB) - {file['relative_path']}")
            
            return True
        else:
            print(f"❌ Large file detection failed: {data.get('error')}")
            return False
    else:
        print(f"❌ Large file detection API failed: {response.status_code}")
        return False

def main():
    """Run the test"""
    print("🚀 Testing Specific Folder Access for Large .mov Files\n")
    
    # Test login
    session = test_login()
    if not session:
        print("\n❌ Cannot proceed without login")
        return
    
    print()
    
    # Test specific folder access
    files = test_specific_folder_access(session)
    print()
    
    # Test large file detection
    test_large_file_detection(session)
    print()
    
    if files:
        print("🎉 SUCCESS! The system can access the specific folder and detect large .mov files!")
        print(f"Found {len(files)} files in the target folder.")
    else:
        print("⚠️ The system could not access the specific folder or find files.")
        print("This might be due to path encoding or permission issues.")

if __name__ == "__main__":
    main()
