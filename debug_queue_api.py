#!/usr/bin/env python3
"""
DEBUG QUEUE API ISSUE
Debug why the queue API is returning 0 files even after adding test data
"""

import sqlite3
import requests
import json
import os

def check_database_directly():
    """Check the database directly to see what's in there"""
    print("🔍 CHECKING DATABASE DIRECTLY")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('archives_management.db')
        cursor = conn.cursor()
        
        # Check total queue items
        cursor.execute('SELECT COUNT(*) FROM queue_items')
        total = cursor.fetchone()[0]
        print(f"📊 Total queue items: {total}")
        
        # Check by assign_to
        cursor.execute('SELECT assign_to, COUNT(*) FROM queue_items GROUP BY assign_to')
        assign_counts = cursor.fetchall()
        print("📋 Assignment breakdown:")
        for assign_to, count in assign_counts:
            print(f"   {assign_to}: {count}")
        
        # Check by status
        cursor.execute('SELECT status, COUNT(*) FROM queue_items GROUP BY status')
        status_counts = cursor.fetchall()
        print("📊 Status breakdown:")
        for status, count in status_counts:
            print(f"   {status}: {count}")
        
        # Check specific query used by API
        print("\n🔍 TESTING EXACT API QUERY:")
        cursor.execute('''
            SELECT id, folder_name, folder_path, category, assign_to, created_at, processed_at,
                   video_ids, url, remarks
            FROM queue_items
            WHERE assign_to = 'Executor Public'
            AND status = 'completed'
            AND category != 'Private one video'
            ORDER BY processed_at DESC
        ''')
        
        api_results = cursor.fetchall()
        print(f"📥 API query results: {len(api_results)} items")
        
        for row in api_results:
            queue_id, folder_name, folder_path, category, assign_to, created_at, processed_at, video_ids, url, remarks = row
            print(f"   📂 {folder_name} -> {category} (Status: completed)")
            print(f"      Path: {folder_path}")
            print(f"      Exists: {os.path.exists(folder_path) if folder_path else False}")
        
        # Check all queue items regardless of filters
        print("\n🔍 ALL QUEUE ITEMS:")
        cursor.execute('SELECT * FROM queue_items ORDER BY created_at DESC LIMIT 10')
        all_items = cursor.fetchall()
        
        for item in all_items:
            print(f"   ID: {item[0]}, Name: {item[1]}, Assign: {item[5]}, Status: {item[10]}, Category: {item[4]}")
        
        conn.close()
        return api_results
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return []

def test_queue_api_directly():
    """Test the queue API directly"""
    print("\n🌐 TESTING QUEUE API DIRECTLY")
    print("=" * 50)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5001"
    
    try:
        # Login
        print("1. 🔐 Login...")
        login_data = {
            'username': 'executor_public',
            'password': 'Shiva@123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code not in [200, 302]:
            print("   ❌ Login failed!")
            return False
        
        print("   ✅ Login successful!")
        
        # Test queue API
        print("\n2. 📋 Test queue API...")
        queue_response = session.get(f"{base_url}/api/executive-public/queue")
        print(f"   Status: {queue_response.status_code}")
        
        if queue_response.status_code == 200:
            try:
                queue_data = queue_response.json()
                print(f"   Success: {queue_data.get('success')}")
                
                if queue_data.get('success'):
                    files = queue_data.get('files', [])
                    print(f"   Files: {len(files)}")
                    
                    for file in files:
                        print(f"      📂 {file.get('folder_name')} (ID: {file.get('queue_item_id')})")
                        print(f"         Path: {file.get('folder_path')}")
                        print(f"         Category: {file.get('category')}")
                else:
                    print(f"   Error: {queue_data.get('error')}")
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON: {queue_response.text[:200]}...")
        else:
            print(f"   ❌ HTTP Error: {queue_response.status_code}")
            print(f"      Response: {queue_response.text[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ API test error: {e}")
        return False

def create_test_folders():
    """Create test folders in the destination to match the database entries"""
    print("\n📁 CREATING TEST FOLDERS")
    print("=" * 50)
    
    dest_base = r"T:\To_Process\Rough folder"
    
    test_folders = [
        {
            'category': 'Internal video without stems',
            'folder_name': 'DB_Lock_Test_Folder_1'
        },
        {
            'category': 'Miscellaneous',
            'folder_name': 'DB_Lock_Test_Folder_2'
        },
        {
            'category': 'For Editing',
            'folder_name': 'DB_Lock_Test_Folder_3'
        }
    ]
    
    created_folders = []
    
    for folder_info in test_folders:
        try:
            folder_path = os.path.join(dest_base, folder_info['category'], folder_info['folder_name'])
            os.makedirs(folder_path, exist_ok=True)
            
            # Create a test video file
            test_video_path = os.path.join(folder_path, "test_video.mp4")
            with open(test_video_path, 'w') as f:
                f.write("# Test video file for database lock testing")
            
            print(f"   ✅ Created: {folder_path}")
            created_folders.append(folder_path)
            
        except Exception as e:
            print(f"   ❌ Failed to create {folder_info['folder_name']}: {e}")
    
    return created_folders

def main():
    print("🚨 DEBUG QUEUE API ISSUE")
    print("=" * 80)
    
    # Step 1: Check database directly
    db_results = check_database_directly()
    
    # Step 2: Create test folders if needed
    if db_results:
        print("\n📁 Database has items, creating matching folders...")
        created_folders = create_test_folders()
        print(f"   ✅ Created {len(created_folders)} test folders")
    
    # Step 3: Test queue API
    test_queue_api_directly()
    
    print("\n" + "=" * 80)
    print("🎯 QUEUE API DEBUG RESULTS")
    print("=" * 80)
    
    if db_results:
        print("✅ DATABASE HAS QUEUE ITEMS")
        print(f"   📊 Found {len(db_results)} items matching API query")
        print("   🔍 Check if folders exist in destination paths")
        print("   🔍 Queue API should return these items")
    else:
        print("❌ DATABASE HAS NO MATCHING ITEMS")
        print("   🔧 Need to add test data with correct:")
        print("      - assign_to = 'Executor Public'")
        print("      - status = 'completed'")
        print("      - category != 'Private one video'")
    
    print("\n🎯 NEXT STEPS:")
    print("   1. Verify folders exist in destination paths")
    print("   2. Check queue API response")
    print("   3. Test database lock error reproduction")

if __name__ == "__main__":
    main()
