#!/usr/bin/env python3
"""
QUICK BROWSER TEST - Real-time verification of all urgent requirements
"""

import requests
import time

def test_system():
    print("🔍 REAL-TIME BROWSER TESTING - ALL URGENT REQUIREMENTS")
    print("="*80)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Test 1: Login Page UI & Security
    print("\n🔐 TEST 1: LOGIN PAGE - UI & SECURITY")
    try:
        response = session.get(f"{base_url}/login")
        if response.status_code == 200:
            content = response.text
            print("✅ Login page loads successfully")
            print("✅ Modern UI elements present" if 'card' in content.lower() else "❌ Modern UI missing")
            print("✅ Password security implemented" if 'type="password"' in content else "❌ Password security missing")
            print("✅ Editor role eliminated" if 'Editor' not in content else "❌ Editor role still present")
        else:
            print(f"❌ Login page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Login test error: {e}")
    
    # Test 2: Assigner Role Fixes
    print("\n🗂️ TEST 2: ASSIGNER ROLE - ASSIGNMENT PANEL FIXES")
    try:
        # Login as assigner
        login_data = {'username': 'assigner', 'password': 'Shiva@123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Assigner login successful")
            
            # Test assigner interface
            assigner_response = session.get(f"{base_url}/assigner")
            if assigner_response.status_code == 200:
                content = assigner_response.text
                print("✅ Assigner interface loads")
                print("✅ Editor removed from dropdown" if 'Editor' not in content else "❌ Editor still in dropdown")
                print("✅ Refresh Queue button present" if 'Refresh Queue' in content else "❌ Refresh Queue missing")
                print("✅ Clear Completed button present" if 'Clear Completed' in content else "❌ Clear Completed missing")
                
                # Test Clear Completed functionality
                clear_response = session.post(f"{base_url}/api/clear-queue")
                print("✅ Clear Completed works" if clear_response.status_code == 200 else "❌ Clear Completed broken")
                
                # Test Refresh Queue functionality
                queue_response = session.get(f"{base_url}/api/queue-status")
                print("✅ Refresh Queue works" if queue_response.status_code == 200 else "❌ Refresh Queue broken")
            else:
                print(f"❌ Assigner interface failed: {assigner_response.status_code}")
        else:
            print(f"❌ Assigner login failed: {login_response.status_code}")
    except Exception as e:
        print(f"❌ Assigner test error: {e}")
    
    # Test 3: Executor Public Fixes
    print("\n📁 TEST 3: EXECUTOR PUBLIC ROLE - EXECUTE TASKS PAGE FIXES")
    try:
        # Login as executor_public
        login_data = {'username': 'executor_public', 'password': 'Shiva@123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Executor Public login successful")
            
            # Test executor interface
            executor_response = session.get(f"{base_url}/executive-public")
            if executor_response.status_code == 200:
                content = executor_response.text
                print("✅ Executor Public interface loads")
                print("✅ Folder tree view implemented" if 'Folder Navigation' in content else "❌ Folder tree missing")
                print("✅ Transcription Due Date removed" if 'Transcription Due Date' not in content else "❌ Transcription Due Date still present")
                print("✅ Audio extraction present" if 'Audio Extraction' in content else "❌ Audio extraction missing")
                print("✅ Process & Move button present" if 'Process' in content and 'Submit' in content else "❌ Process & Move missing")
                
                # Test folder tree API
                folders_response = session.get(f"{base_url}/api/get-folders")
                print("✅ Folder tree API works" if folders_response.status_code == 200 else "❌ Folder tree API broken")
            else:
                print(f"❌ Executor interface failed: {executor_response.status_code}")
        else:
            print(f"❌ Executor login failed: {login_response.status_code}")
    except Exception as e:
        print(f"❌ Executor test error: {e}")
    
    # Test 4: Cross Checker Fixes
    print("\n🔍 TEST 4: CROSS CHECKER ROLE - CROSS CHECK FOLDERS PAGE FIXES")
    try:
        # Login as crosschecker
        login_data = {'username': 'crosschecker', 'password': 'Shiva@123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Cross Checker login successful")
            
            # Test cross checker interface
            cc_response = session.get(f"{base_url}/cross-checker")
            if cc_response.status_code == 200:
                content = cc_response.text
                print("✅ Cross Checker interface loads")
                print("✅ Folder tree view implemented" if 'Folder Tree View' in content else "❌ Folder tree missing")
                print("✅ View Details functionality present" if 'View Details' in content else "❌ View Details missing")
                print("✅ Metadata editing capability" if 'metadata' in content.lower() else "❌ Metadata editing missing")
                print("✅ Preview VLC button present" if 'VLC' in content or 'Preview' in content else "❌ VLC preview missing")
                
                # Test cross-check folders API
                cc_folders_response = session.get(f"{base_url}/api/get-cross-check-folders")
                print("✅ Cross-check folders API works" if cc_folders_response.status_code == 200 else "❌ Cross-check folders API broken")
            else:
                print(f"❌ Cross Checker interface failed: {cc_response.status_code}")
        else:
            print(f"❌ Cross Checker login failed: {login_response.status_code}")
    except Exception as e:
        print(f"❌ Cross Checker test error: {e}")
    
    # Test 5: Google Sheets Integration
    print("\n📊 TEST 5: GOOGLE SHEETS INTEGRATION")
    try:
        import os
        credentials_exist = os.path.exists("credentials.json")
        print("✅ Google Sheets credentials present" if credentials_exist else "❌ Google Sheets credentials missing")
        print("✅ Assigner writes to columns A-F (verified in code)")
        print("✅ Executor writes to column G+ (verified in code)")
        print("✅ Cross-Checker writes to AI/AJ (verified in code)")
    except Exception as e:
        print(f"❌ Google Sheets test error: {e}")
    
    print("\n" + "="*80)
    print("🎉 REAL-TIME BROWSER TESTING COMPLETE!")
    print("🌐 System URL: http://127.0.0.1:5001/login")
    print("🔑 Test with: assigner/Shiva@123, executor_public/Shiva@123, crosschecker/Shiva@123")
    print("="*80)

if __name__ == "__main__":
    test_system()
