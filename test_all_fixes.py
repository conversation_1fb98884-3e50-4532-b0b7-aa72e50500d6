#!/usr/bin/env python3
"""
Comprehensive test script to verify all fixes:
1. Video playback
2. Info button functionality
3. Folder selection
4. Queue system
5. Progress tracking
6. Google Sheets logging
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:5001"
TEST_USERNAME = "assigner"
TEST_PASSWORD = "Shiva@123"
SPECIFIC_FOLDER = "Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019/Output"

def test_login():
    """Test login functionality"""
    print("🔐 Testing login...")
    
    session = requests.Session()
    
    # Login
    login_data = {
        'username': TEST_USERNAME,
        'password': TEST_PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        print("✅ Login successful")
        return session
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def test_video_info_api(session):
    """Test video info API (i button functionality)"""
    print("ℹ️ Testing video info API (i button)...")
    
    # Get files from specific folder first
    test_data = {'folder_path': SPECIFIC_FOLDER}
    response = session.post(
        f"{BASE_URL}/api/folder-contents/",
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success') and data.get('files'):
            # Test video info for first file
            test_file = data['files'][0]
            file_path = test_file['path']
            
            info_response = session.get(f"{BASE_URL}/api/video-info", params={'path': file_path})
            
            if info_response.status_code == 200:
                info_data = info_response.json()
                if info_data.get('success'):
                    print("✅ Video info API working")
                    print(f"   File: {info_data['info']['name']}")
                    print(f"   Size: {info_data['info']['size_formatted']}")
                    return True
                else:
                    print(f"❌ Video info failed: {info_data.get('error')}")
                    return False
            else:
                print(f"❌ Video info API failed: {info_response.status_code}")
                return False
        else:
            print("❌ No files found to test video info")
            return False
    else:
        print("❌ Could not get folder contents for video info test")
        return False

def test_video_serving(session):
    """Test video serving API"""
    print("🎥 Testing video serving...")
    
    # Get files from specific folder first
    test_data = {'folder_path': SPECIFIC_FOLDER}
    response = session.post(
        f"{BASE_URL}/api/folder-contents/",
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success') and data.get('files'):
            # Test video serving for first file
            test_file = data['files'][0]
            file_path = test_file['path']
            
            # Test HEAD request (what the frontend does)
            serve_response = session.head(f"{BASE_URL}/api/serve-video", params={'path': file_path})
            
            if serve_response.status_code in [200, 206]:  # 206 for partial content
                print("✅ Video serving API working")
                print(f"   Status: {serve_response.status_code}")
                print(f"   Content-Type: {serve_response.headers.get('Content-Type', 'Unknown')}")
                return True
            else:
                print(f"❌ Video serving failed: {serve_response.status_code}")
                return False
        else:
            print("❌ No files found to test video serving")
            return False
    else:
        print("❌ Could not get folder contents for video serving test")
        return False

def test_google_sheets_connectivity(session):
    """Test Google Sheets connectivity"""
    print("📊 Testing Google Sheets connectivity...")
    
    response = session.post(f"{BASE_URL}/api/test-google-sheets")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Google Sheets test completed")
        print(f"   Success: {data.get('success')}")
        print(f"   gspread available: {data.get('gspread_available')}")
        print(f"   Credentials exist: {data.get('credentials_exist')}")
        
        if data.get('success'):
            print(f"   Message: {data.get('message')}")
        else:
            print(f"   Error: {data.get('error')}")
        
        return data.get('gspread_available', False)
    else:
        print(f"❌ Google Sheets test failed: {response.status_code}")
        return False

def test_enhanced_assignment_api(session):
    """Test enhanced assignment API"""
    print("🔄 Testing enhanced assignment API...")
    
    # Test with empty file list (should fail gracefully)
    test_data = {
        'file_paths': [],
        'category': 'Internal video with stems',
        'assign_to': 'Executor Public',
        'video_ids': 'TEST001',
        'url': '',
        'remarks': 'Test assignment'
    }
    
    response = session.post(
        f"{BASE_URL}/api/enhanced-batch-assign",
        json=test_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        data = response.json()
        if not data.get('success') and "Missing required data" in data.get('error', ''):
            print("✅ Enhanced assignment API validation working")
            return True
        else:
            print(f"❌ Unexpected response: {data}")
            return False
    else:
        print(f"❌ Enhanced assignment API failed: {response.status_code}")
        return False

def test_tree_assigner_interface(session):
    """Test tree assigner interface loads with all enhancements"""
    print("🌳 Testing tree assigner interface...")
    
    response = session.get(f"{BASE_URL}/tree-assigner")
    
    if response.status_code == 200:
        content = response.text
        
        # Check for key enhancements
        checks = [
            ("Processing Queue", "Processing Queue" in content),
            ("Select Whole Main Folder", "Select Whole Main Folder" in content),
            ("Bottom Progress Bar", "bottomProgressBar" in content),
            ("Enhanced Video Player", "videoLoadingIndicator" in content),
            ("VLC Button", "openInVLC" in content),
            ("File Details Modal", "fileDetailsModal" in content)
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            if check_result:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
                all_passed = False
        
        if all_passed:
            print("✅ Tree assigner interface has all enhancements")
            return True
        else:
            print("❌ Some enhancements missing from interface")
            return False
    else:
        print(f"❌ Tree assigner interface failed: {response.status_code}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Comprehensive Fix Verification Tests\n")
    
    # Test login
    session = test_login()
    if not session:
        print("\n❌ Cannot proceed without login")
        return
    
    print()
    
    # Run all tests
    tests = [
        ("Tree Assigner Interface", test_tree_assigner_interface),
        ("Video Info API (i button)", test_video_info_api),
        ("Video Serving", test_video_serving),
        ("Enhanced Assignment API", test_enhanced_assignment_api),
        ("Google Sheets Connectivity", test_google_sheets_connectivity)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"Testing {test_name}...")
            if test_func(session):
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            print()
    
    # Summary
    print("=" * 70)
    print(f"COMPREHENSIVE TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 70)
    
    if passed == total:
        print("🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("\n✅ Working Features:")
        print("   • Video files can be played (with VLC fallback)")
        print("   • Info button (i) shows detailed file information")
        print("   • Folder selection and queue system implemented")
        print("   • Progress tracking with bottom progress bar")
        print("   • Enhanced assignment API with error handling")
        print("   • Google Sheets logging connectivity tested")
        print("   • All UI enhancements present")
        
        print("\n🚀 SYSTEM READY FOR PRODUCTION USE!")
    else:
        print(f"⚠️  {total - passed} issues found. Please check the details above.")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
