#!/usr/bin/env python3
"""
Test batch assignment functionality
"""

import requests
import json

def test_batch_assignment():
    """Test batch assignment with correct users"""
    print("🔄 Testing Batch Assignment")
    
    # Login
    session = requests.Session()
    login_data = {'username': 'assigner', 'password': '<PERSON>@123'}
    response = session.post("http://127.0.0.1:5001/login", data=login_data)
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    print("✅ Login successful")
    
    # Get files
    response = session.get("http://127.0.0.1:5001/api/simple-files/Z6371_Promo_Cauvery-Calling_Core-Video-Short-Version_English_02Mins-AND-04Mins_Stems_21-Sep-2019")
    if response.status_code != 200:
        print("❌ Failed to get files")
        return
    
    data = response.json()
    if not data.get('success') or not data.get('files'):
        print("❌ No files found")
        return
    
    files = data['files']
    print(f"✅ Found {len(files)} files")
    
    # Test batch assignment with correct user
    test_files = [f['path'] for f in files[:2]]  # Test with first 2 files
    batch_data = {
        'files': test_files,
        'category': 'Miscellaneous',
        'user': 'Executor Public',  # Use correct user
        'move_files': False  # Copy instead of move for testing
    }
    
    print(f"🔄 Testing batch assignment with {len(test_files)} files...")
    print(f"Category: {batch_data['category']}")
    print(f"User: {batch_data['user']}")
    print(f"Move files: {batch_data['move_files']}")
    
    response = session.post("http://127.0.0.1:5001/api/batch-assign",
                          headers={'Content-Type': 'application/json'},
                          data=json.dumps(batch_data))
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"✅ Batch assignment successful!")
            print(f"   Processed: {result.get('processed', 0)} files")
            print(f"   Failed: {result.get('failed', 0)} files")
            if result.get('processed_files'):
                print(f"   Files: {', '.join(result['processed_files'])}")
        else:
            print(f"❌ Batch assignment failed: {result.get('error')}")
    else:
        print(f"❌ Batch assignment API failed: {response.status_code}")
        print(f"Response: {response.text}")

if __name__ == "__main__":
    test_batch_assignment()
