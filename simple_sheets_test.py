#!/usr/bin/env python3
"""
SIMPLE GOOGLE SHEETS TEST
Direct test of Google Sheets column mapping
"""

import requests
import json
from datetime import datetime

def simple_sheets_test():
    print("🚨 SIMPLE GOOGLE SHEETS COLUMN TEST")
    print("="*60)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Login as assigner
    print("🔐 Logging in as assigner...")
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    print("✅ Login successful")
    
    # Test Google Sheets API
    print("\n📊 Testing Google Sheets API...")
    
    test_data = {
        'folder_name': f'REAL_TEST_{datetime.now().strftime("%H%M%S")}',
        'date_processed': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'moved_to_folder': 'Internal video without stems',
        'social_media_url': 'https://real-test.com',
        'assigned_to': 'Executor Public',
        'remarks': 'REAL TEST - Should be in columns A-F'
    }
    
    print(f"📝 Test data:")
    print(f"   Column A: {test_data['folder_name']}")
    print(f"   Column B: {test_data['date_processed']}")
    print(f"   Column C: {test_data['moved_to_folder']}")
    print(f"   Column D: {test_data['social_media_url']}")
    print(f"   Column E: {test_data['assigned_to']}")
    print(f"   Column F: {test_data['remarks']}")
    
    # Call the test API
    response = session.post(f"{base_url}/api/test-google-sheets", json=test_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"\n✅ API Response: {result.get('success', False)}")
        print(f"✅ Message: {result.get('message', 'No message')}")
        
        if result.get('success'):
            print(f"✅ Credentials exist: {result.get('credentials_exist', False)}")
            print(f"✅ Sheet ID: {result.get('sheet_id', 'Not found')}")
            print(f"✅ Sheet Name: {result.get('sheet_name', 'Not found')}")
            
            if 'row_data' in result:
                row_data = result['row_data']
                print(f"\n📊 ACTUAL DATA WRITTEN TO GOOGLE SHEETS:")
                print(f"   Position 1 (Column A): {row_data[0] if len(row_data) > 0 else 'Empty'}")
                print(f"   Position 2 (Column B): {row_data[1] if len(row_data) > 1 else 'Empty'}")
                print(f"   Position 3 (Column C): {row_data[2] if len(row_data) > 2 else 'Empty'}")
                print(f"   Position 4 (Column D): {row_data[3] if len(row_data) > 3 else 'Empty'}")
                print(f"   Position 5 (Column E): {row_data[4] if len(row_data) > 4 else 'Empty'}")
                print(f"   Position 6 (Column F): {row_data[5] if len(row_data) > 5 else 'Empty'}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
    else:
        print(f"❌ API call failed: {response.status_code}")
        print(f"❌ Response: {response.text}")
    
    print("\n" + "="*60)
    print("🎯 VERIFICATION REQUIRED:")
    print("1. Open: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print("2. Go to 'Records' sheet")
    print("3. Check if the test data appears in columns A-F")
    print("4. If data appears in columns G-M, the bug still exists")
    print("="*60)

if __name__ == "__main__":
    simple_sheets_test()
