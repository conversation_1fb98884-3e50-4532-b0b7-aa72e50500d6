{% extends "base.html" %}

{% block title %}Admin Panel - Archives Management System{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
<link href="/static/style.css" rel="stylesheet">
<style>
    .nav-pills .nav-link.active { background-color: #667eea; }
    .chart-container { position: relative; height: 300px; }
    .config-section { border-left: 4px solid #667eea; padding-left: 1rem; margin-bottom: 2rem; }
    .path-item { background-color: #f8f9fa; border-radius: 0.375rem; padding: 0.75rem; margin-bottom: 0.5rem; }
    .audit-log { font-family: 'Courier New', monospace; font-size: 0.875rem; max-height: 400px; overflow-y: auto; }
    .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 0.5rem; padding: 1.5rem; text-align: center; }
    .stat-number { font-size: 2rem; font-weight: bold; margin-bottom: 0.5rem; }
    .metadata-field { border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem; margin-bottom: 1rem; }
    .system-health { padding: 1rem; border-radius: 0.375rem; margin-bottom: 1rem; }
    .health-good { background-color: #d4edda; border-left: 4px solid #28a745; }
    .health-warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
    .health-danger { background-color: #f8d7da; border-left: 4px solid #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 mb-0">
            <i class="fas fa-shield-alt me-2 text-gradient"></i>Admin Control Center
        </h1>
        <p class="text-muted">Comprehensive system administration, reporting, and configuration management</p>
    </div>
</div>

<!-- Navigation Tabs -->
<div class="row mb-4">
    <div class="col-12">
        <ul class="nav nav-pills nav-fill" id="adminTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="dashboard-tab" data-bs-toggle="pill" data-bs-target="#dashboard-pane" type="button" role="tab">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="reports-tab" data-bs-toggle="pill" data-bs-target="#reports-pane" type="button" role="tab">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="paths-tab" data-bs-toggle="pill" data-bs-target="#paths-pane" type="button" role="tab">
                    <i class="fas fa-folder-tree me-2"></i>Path Config
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="metadata-tab" data-bs-toggle="pill" data-bs-target="#metadata-pane" type="button" role="tab">
                    <i class="fas fa-tags me-2"></i>Metadata
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="users-tab" data-bs-toggle="pill" data-bs-target="#users-pane" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>Users
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-tab" data-bs-toggle="pill" data-bs-target="#system-pane" type="button" role="tab">
                    <i class="fas fa-cogs me-2"></i>System
                </button>
            </li>
        </ul>
    </div>
</div>

<!-- Tab Content -->
<div class="tab-content" id="adminTabContent">
    <!-- Dashboard Tab -->
    <div class="tab-pane fade show active" id="dashboard-pane" role="tabpanel">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">{{ users|length if users else 0 }}</div>
                    <div>Total Users</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                    <div class="stat-number" id="completedFiles">{{ file_stats.get('Completed', 0) if file_stats else 0 }}</div>
                    <div>Completed Files</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                    <div class="stat-number" id="pendingFiles">{{ (file_stats.get('Assigned', 0) + file_stats.get('Pending', 0)) if file_stats else 0 }}</div>
                    <div>Pending Files</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                    <div class="stat-number" id="totalFiles">{{ file_stats.values()|sum if file_stats else 0 }}</div>
                    <div>Total Files</div>
                </div>
            </div>
        </div>

        <!-- System Health Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>System Health Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="systemHealthContainer">
                            <div class="text-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-muted">Loading system health...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-primary w-100" onclick="generateSystemReport()">
                                    <i class="fas fa-file-alt me-2"></i>Generate Report
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-success w-100" onclick="backupSystem()">
                                    <i class="fas fa-download me-2"></i>Backup System
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="dropdown">
                                    <button class="btn btn-warning w-100 dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-broom me-2"></i>Clear Cache
                                    </button>
                                    <ul class="dropdown-menu w-100">
                                        <li><a class="dropdown-item" href="#" onclick="clearCache('all')">
                                            <i class="fas fa-trash-alt me-2"></i>Clear All Cache
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="clearCache('sessions')">
                                            <i class="fas fa-users me-2"></i>Clear User Sessions
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="clearCache('filesystem')">
                                            <i class="fas fa-folder me-2"></i>Clear File Cache
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="clearCache('database')">
                                            <i class="fas fa-database me-2"></i>Clear DB Cache
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="resetAllUsers()">
                                            <i class="fas fa-exclamation-triangle me-2"></i>Reset All Users
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-info w-100" onclick="refreshStats()">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh Stats
                                </button>
                            </div>
                        </div>

                        <!-- Master Reset Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Master System Reset
                                    </h6>
                                    <p class="mb-3">Use these controls to reset the system to a clean state. All user data and cache will be cleared.</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-danger" onclick="masterCacheReset()">
                                            <i class="fas fa-nuclear me-2"></i>Master Cache Reset
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="resetAllUserSessions()">
                                            <i class="fas fa-users-slash me-2"></i>Reset All Sessions
                                        </button>
                                        <button class="btn btn-outline-info" onclick="cleanupOldData()">
                                            <i class="fas fa-broom me-2"></i>Cleanup Old Data
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Tab -->
    <div class="tab-pane fade" id="reports-pane" role="tabpanel">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>Role-Based Reports & Analytics
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Report Filters -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="reportDateFrom" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="reportDateFrom">
                            </div>
                            <div class="col-md-3">
                                <label for="reportDateTo" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="reportDateTo">
                            </div>
                            <div class="col-md-3">
                                <label for="reportUser" class="form-label">User</label>
                                <select class="form-select" id="reportUser">
                                    <option value="">All Users</option>
                                    {% if users %}
                                        {% for user in users %}
                                        <option value="{{ user.username }}">{{ user.username }} ({{ user.role }})</option>
                                        {% endfor %}
                                    {% endif %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="reportRole" class="form-label">Role</label>
                                <select class="form-select" id="reportRole">
                                    <option value="">All Roles</option>
                                    <option value="Assigner">Assigner</option>
                                    <option value="Executor Public">Executor Public</option>
                                    <option value="Executor Private">Executor Private</option>
                                    <option value="Cross Checker">Cross Checker</option>
                                    <option value="Main Admin">Main Admin</option>
                                </select>
                            </div>
                        </div>

                        <!-- Report Actions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="btn-group me-2" role="group">
                                    <button class="btn btn-primary" onclick="generateReport('activity')">
                                        <i class="fas fa-chart-bar me-1"></i>Activity Report
                                    </button>
                                    <button class="btn btn-success" onclick="generateReport('performance')">
                                        <i class="fas fa-tachometer-alt me-1"></i>Performance Report
                                    </button>
                                    <button class="btn btn-info" onclick="generateReport('system')">
                                        <i class="fas fa-server me-1"></i>System Report
                                    </button>
                                </div>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-download me-1"></i>Export
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                                            <i class="fas fa-file-csv me-2"></i>CSV
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                            <i class="fas fa-file-pdf me-2"></i>PDF
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                            <i class="fas fa-file-excel me-2"></i>Excel
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Report Content -->
                        <div id="reportContent">
                            <div class="row">
                                <!-- Charts -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">User Activity Distribution</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="chart-container">
                                                <canvas id="userActivityChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Processing Time Trends</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="chart-container">
                                                <canvas id="processingTimeChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Category Distribution</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="chart-container">
                                                <canvas id="categoryChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Storage Usage</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="chart-container">
                                                <canvas id="storageChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Detailed Statistics -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Detailed Statistics</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-hover" id="reportTable">
                                                    <thead>
                                                        <tr>
                                                            <th>User</th>
                                                            <th>Role</th>
                                                            <th>Files Processed</th>
                                                            <th>Folders Validated</th>
                                                            <th>Audio Extracted</th>
                                                            <th>Avg Processing Time</th>
                                                            <th>Error Rate</th>
                                                            <th>Last Activity</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="reportTableBody">
                                                        <tr>
                                                            <td colspan="8" class="text-center text-muted py-4">
                                                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                                                <br>Generate a report to view detailed statistics
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Path Configuration Tab -->
    <div class="tab-pane fade" id="paths-pane" role="tabpanel">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-folder-tree me-2"></i>System Path Configuration
                            </h5>
                            <div>
                                <button class="btn btn-success me-2" onclick="addNewPath()">
                                    <i class="fas fa-plus me-1"></i>Add Path
                                </button>
                                <button class="btn btn-primary" onclick="validateAllPaths()">
                                    <i class="fas fa-check-circle me-1"></i>Validate All
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="config-section">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-cog me-2"></i>Core System Paths
                            </h6>
                            <div id="systemPathsContainer">
                                <!-- System paths will be loaded here -->
                            </div>
                        </div>

                        <div class="config-section">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-folder me-2"></i>Processing Destination Paths
                            </h6>
                            <div id="destinationPathsContainer">
                                <!-- Destination paths will be loaded here -->
                            </div>
                        </div>

                        <div class="config-section">
                            <h6 class="text-info mb-3">
                                <i class="fas fa-music me-2"></i>Audio Processing Paths
                            </h6>
                            <div id="audioPathsContainer">
                                <!-- Audio paths will be loaded here -->
                            </div>
                        </div>

                        <div class="config-section">
                            <h6 class="text-warning mb-3">
                                <i class="fas fa-plus-circle me-2"></i>Custom Paths
                            </h6>
                            <div id="customPathsContainer">
                                <!-- Custom paths will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Path Validation Results -->
        <div class="row mb-4" id="pathValidationResults" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>Path Validation Results
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="validationResultsContent">
                            <!-- Validation results will appear here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Metadata Management Tab -->
    <div class="tab-pane fade" id="metadata-pane" role="tabpanel">
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-tags me-2"></i>Category Management
                            </h6>
                            <button class="btn btn-success btn-sm" onclick="addNewCategory()">
                                <i class="fas fa-plus me-1"></i>Add Category
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="categoriesContainer">
                            <!-- Categories will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-2"></i>Metadata Fields
                            </h6>
                            <button class="btn btn-success btn-sm" onclick="addNewMetadataField()">
                                <i class="fas fa-plus me-1"></i>Add Field
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="metadataFieldsContainer">
                            <!-- Metadata fields will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Metadata Template Editor -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Metadata Template Editor
                            </h6>
                            <div>
                                <button class="btn btn-primary btn-sm me-2" onclick="previewTemplate()">
                                    <i class="fas fa-eye me-1"></i>Preview
                                </button>
                                <button class="btn btn-success btn-sm" onclick="saveTemplate()">
                                    <i class="fas fa-save me-1"></i>Save Template
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Available Fields</h6>
                                <div id="availableFields" class="border rounded p-3" style="min-height: 300px;">
                                    <!-- Available fields will be loaded here -->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Template Layout</h6>
                                <div id="templateLayout" class="border rounded p-3" style="min-height: 300px;" ondrop="dropField(event)" ondragover="allowDrop(event)">
                                    <div class="text-center text-muted py-5">
                                        <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                                        <p>Drag fields here to build your template</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Management Tab -->
    <div class="tab-pane fade" id="users-pane" role="tabpanel">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Enhanced User Management</h5>
                        <div>
                            <button class="btn btn-info me-2" onclick="exportUsers()">
                                <i class="fas fa-download me-1"></i>Export Users
                            </button>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createUserModal">
                                <i class="fas fa-plus me-1"></i>Create User
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- User Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="userSearch" placeholder="Search users...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="roleFilter">
                                    <option value="">All Roles</option>
                                    <option value="Main Admin">Main Admin</option>
                                    <option value="Assigner">Assigner</option>
                                    <option value="Executor Public">Executor Public</option>
                                    <option value="Executor Private">Executor Private</option>
                                    <option value="Cross Checker">Cross Checker</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary w-100" onclick="filterUsers()">
                                    <i class="fas fa-filter me-1"></i>Filter
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Role</th>
                                        <th>Created</th>
                                        <th>Last Login</th>
                                        <th>Activity Score</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    {% if users %}
                                        {% for user in users %}
                                        <tr id="user-row-{{ user.id }}">
                                            <td>
                                                <i class="fas fa-user me-2"></i>
                                                <strong>{{ user.username }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ user.role }}</span>
                                            </td>
                                            <td class="format-date">{{ user.created_at }}</td>
                                            <td class="format-date">{{ user.last_login or 'Never' }}</td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" style="width: 75%">75%</div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                                                    {{ 'Active' if user.is_active else 'Inactive' }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-info btn-sm" onclick="viewUserDetails({{ user.id }})">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-warning btn-sm" onclick="editUser({{ user.id }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-secondary btn-sm" onclick="resetUserPassword({{ user.id }})">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    {% if user.id != session.user_id %}
                                                    <button class="btn btn-danger btn-sm" onclick="deleteUser({{ user.id }}, '{{ user.username }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center text-muted py-4">
                                                <i class="fas fa-users fa-2x mb-2"></i>
                                                <br>No users found
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Management Tab -->
    <div class="tab-pane fade" id="system-pane" role="tabpanel">
        <div class="row mb-4">
            <!-- System Health Monitoring -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>System Health Monitoring
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="systemHealthDetails">
                            <!-- System health details will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup & Restore -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-database me-2"></i>Backup & Restore
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="createBackup()">
                                <i class="fas fa-download me-2"></i>Create System Backup
                            </button>
                            <button class="btn btn-warning" onclick="showRestoreModal()">
                                <i class="fas fa-upload me-2"></i>Restore from Backup
                            </button>
                            <button class="btn btn-info" onclick="scheduleBackup()">
                                <i class="fas fa-clock me-2"></i>Schedule Automatic Backup
                            </button>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">Last backup: <span id="lastBackupTime">Never</span></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Audit Log -->
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>Admin Audit Log
                            </h6>
                            <div>
                                <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshAuditLog()">
                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="exportAuditLog()">
                                    <i class="fas fa-download me-1"></i>Export
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="audit-log" id="auditLogContainer">
                            <!-- Audit log entries will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Modals -->

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Create New User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <div class="mb-3">
                        <label for="newUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="newUsername" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">Password</label>
                        <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newRole" class="form-label">Role</label>
                        <select class="form-select" id="newRole" required>
                            <option value="">Select a role...</option>
                            <option value="Main Admin">Main Admin</option>
                            <option value="Assigner">Assigner</option>
                            <option value="Executor Public">Executor Public</option>
                            <option value="Executor Private">Executor Private</option>
                            <option value="Cross Checker">Cross Checker</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="newEmail" class="form-label">Email (Optional)</label>
                        <input type="email" class="form-control" id="newEmail">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmCreateUser">
                    <i class="fas fa-plus me-1"></i>Create User
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Path Modal -->
<div class="modal fade" id="editPathModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit Path Configuration
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editPathForm">
                    <div class="mb-3">
                        <label for="pathName" class="form-label">Path Name</label>
                        <input type="text" class="form-control" id="pathName" required>
                    </div>
                    <div class="mb-3">
                        <label for="pathValue" class="form-label">Path Value</label>
                        <input type="text" class="form-control" id="pathValue" required>
                    </div>
                    <div class="mb-3">
                        <label for="pathDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="pathDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="pathRequired">
                            <label class="form-check-label" for="pathRequired">
                                Required Path (System Critical)
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmEditPath">
                    <i class="fas fa-save me-1"></i>Save Changes
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Metadata Field Modal -->
<div class="modal fade" id="addMetadataFieldModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Add Metadata Field
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMetadataFieldForm">
                    <div class="mb-3">
                        <label for="fieldName" class="form-label">Field Name</label>
                        <input type="text" class="form-control" id="fieldName" required>
                    </div>
                    <div class="mb-3">
                        <label for="fieldType" class="form-label">Field Type</label>
                        <select class="form-select" id="fieldType" required>
                            <option value="">Select field type...</option>
                            <option value="text">Text Input</option>
                            <option value="textarea">Text Area</option>
                            <option value="select">Dropdown</option>
                            <option value="checkbox">Checkbox</option>
                            <option value="date">Date</option>
                            <option value="number">Number</option>
                            <option value="email">Email</option>
                            <option value="url">URL</option>
                        </select>
                    </div>
                    <div class="mb-3" id="optionsContainer" style="display: none;">
                        <label for="fieldOptions" class="form-label">Options (one per line)</label>
                        <textarea class="form-control" id="fieldOptions" rows="4" placeholder="Option 1&#10;Option 2&#10;Option 3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="fieldValidation" class="form-label">Validation Rules</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="fieldRequired">
                                    <label class="form-check-label" for="fieldRequired">Required</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <input type="number" class="form-control" id="fieldMaxLength" placeholder="Max Length">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="fieldCategory" class="form-label">Category</label>
                        <select class="form-select" id="fieldCategory">
                            <option value="">Select category...</option>
                            <option value="general">General</option>
                            <option value="video">Video</option>
                            <option value="audio">Audio</option>
                            <option value="processing">Processing</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmAddMetadataField">
                    <i class="fas fa-plus me-1"></i>Add Field
                </button>
            </div>
        </div>
    </div>
</div>

<!-- System Backup Modal -->
<div class="modal fade" id="backupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-database me-2"></i>System Backup
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="backupName" class="form-label">Backup Name</label>
                    <input type="text" class="form-control" id="backupName" value="">
                </div>
                <div class="mb-3">
                    <label class="form-label">Backup Components</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="backupDatabase" checked>
                        <label class="form-check-label" for="backupDatabase">Database</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="backupConfig" checked>
                        <label class="form-check-label" for="backupConfig">Configuration Files</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="backupLogs">
                        <label class="form-check-label" for="backupLogs">System Logs</label>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="backupDescription" class="form-label">Description (Optional)</label>
                    <textarea class="form-control" id="backupDescription" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmBackup">
                    <i class="fas fa-download me-1"></i>Create Backup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Delete Modal -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUsername"></strong>?</p>
                <div class="alert alert-danger">
                    <i class="fas fa-warning me-2"></i>
                    This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-1"></i>Delete User
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>Edit User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="editUsername" required>
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">Role</label>
                        <select class="form-select" id="editRole" required>
                            <option value="Main Admin">Main Admin</option>
                            <option value="Assigner">Assigner</option>
                            <option value="Executor Public">Executor Public</option>
                            <option value="Executor Private">Executor Private</option>
                            <option value="Cross Checker">Cross Checker</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmEditUser">
                    <i class="fas fa-save me-1"></i>Save Changes
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>Reset Password
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="resetPasswordForm">
                    <input type="hidden" id="resetUserId">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Resetting password for user: <strong id="resetUserDisplay"></strong>
                    </div>
                    <div class="mb-3">
                        <label for="newPasswordInput" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="newPasswordInput" required minlength="6">
                        <div class="form-text">Password must be at least 6 characters long</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPasswordInput" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirmPasswordInput" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmResetPassword">
                    <i class="fas fa-key me-1"></i>Reset Password
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Master Cache Reset Confirmation Modal -->
<div class="modal fade" id="masterResetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Master System Reset
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">⚠️ WARNING: DESTRUCTIVE ACTION</h6>
                    <p>This will completely reset the system and clear ALL user data:</p>
                    <ul>
                        <li>All user sessions will be terminated</li>
                        <li>All cached data will be cleared</li>
                        <li>All folder views will be reset</li>
                        <li>All user preferences will be lost</li>
                        <li>All operation logs will be cleared</li>
                    </ul>
                    <p class="mb-0"><strong>This action cannot be undone!</strong></p>
                </div>
                <div class="mb-3">
                    <label for="confirmResetText" class="form-label">
                        Type <code>RESET SYSTEM</code> to confirm:
                    </label>
                    <input type="text" class="form-control" id="confirmResetText" placeholder="RESET SYSTEM">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmMasterReset" disabled>
                    <i class="fas fa-nuclear me-1"></i>RESET SYSTEM
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk User Creation Modal -->
<div class="modal fade" id="bulkCreateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-users-cog me-2"></i>Bulk Create Users
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="bulkUserData" class="form-label">User Data (CSV Format)</label>
                    <textarea class="form-control" id="bulkUserData" rows="10" placeholder="username,role,email&#10;user1,Executor Public,<EMAIL>&#10;user2,Cross Checker,<EMAIL>"></textarea>
                    <div class="form-text">Format: username,role,email (one user per line)</div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Default password will be set to <code>Shiva@123</code> for all users
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmBulkCreate">
                    <i class="fas fa-users me-1"></i>Create Users
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="/static/app.js"></script>
<script>
// Global variables
let userToDelete = null;
let currentEditPath = null;
let charts = {};
let systemPaths = {};

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPanel();
});

function initializeAdminPanel() {
    // Format dates
    document.querySelectorAll('.format-date').forEach(element => {
        const text = element.textContent;
        if (text && text !== 'None' && text !== 'Never') {
            element.textContent = formatDate(text);
        }
    });

    // Load initial data
    loadSystemHealth();
    loadSystemPaths();
    loadMetadataFields();
    loadAuditLog();

    // Initialize charts
    initializeCharts();

    // Set up event listeners
    setupEventListeners();
}

function setupEventListeners() {
    // Tab change events
    document.querySelectorAll('#adminTabs button').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const targetId = event.target.getAttribute('data-bs-target');
            handleTabChange(targetId);
        });
    });

    // Field type change for metadata
    const fieldTypeSelect = document.getElementById('fieldType');
    if (fieldTypeSelect) {
        fieldTypeSelect.addEventListener('change', function() {
            const optionsContainer = document.getElementById('optionsContainer');
            if (this.value === 'select') {
                optionsContainer.style.display = 'block';
            } else {
                optionsContainer.style.display = 'none';
            }
        });
    }
}

function handleTabChange(targetId) {
    switch(targetId) {
        case '#reports-pane':
            loadReportData();
            break;
        case '#paths-pane':
            loadSystemPaths();
            break;
        case '#metadata-pane':
            loadMetadataFields();
            break;
        case '#system-pane':
            loadSystemHealth();
            loadAuditLog();
            break;
    }
}

// System Health Functions
async function loadSystemHealth() {
    try {
        const response = await fetch('/api/admin/system-health');
        const data = await response.json();

        if (data.success) {
            displaySystemHealth(data.health);
        } else {
            showAlert('Failed to load system health: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error loading system health:', error);
        showAlert('Failed to load system health', 'danger');
    }
}

function displaySystemHealth(health) {
    const container = document.getElementById('systemHealthContainer');
    if (!container) return;

    let html = '';
    health.forEach(item => {
        const statusClass = item.status === 'good' ? 'health-good' :
                           item.status === 'warning' ? 'health-warning' : 'health-danger';
        const iconClass = item.status === 'good' ? 'fa-check-circle text-success' :
                         item.status === 'warning' ? 'fa-exclamation-triangle text-warning' : 'fa-times-circle text-danger';

        html += `
            <div class="system-health ${statusClass}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas ${iconClass} me-2"></i>
                        <strong>${item.component}</strong>
                    </div>
                    <span class="badge bg-${item.status === 'good' ? 'success' : item.status === 'warning' ? 'warning' : 'danger'}">
                        ${item.status.toUpperCase()}
                    </span>
                </div>
                <p class="mb-0 mt-1">${item.message}</p>
                ${item.details ? `<small class="text-muted">${item.details}</small>` : ''}
            </div>
        `;
    });

    container.innerHTML = html;
}

// Path Configuration Functions
async function loadSystemPaths() {
    try {
        const response = await fetch('/api/admin/system-paths');
        const data = await response.json();

        if (data.success) {
            systemPaths = data.paths;
            displaySystemPaths(data.paths);
        } else {
            showAlert('Failed to load system paths: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error loading system paths:', error);
        showAlert('Failed to load system paths', 'danger');
    }
}

function displaySystemPaths(paths) {
    const containers = {
        'system': document.getElementById('systemPathsContainer'),
        'destination': document.getElementById('destinationPathsContainer'),
        'audio': document.getElementById('audioPathsContainer'),
        'custom': document.getElementById('customPathsContainer')
    };

    Object.keys(containers).forEach(category => {
        const container = containers[category];
        if (!container) return;

        const categoryPaths = paths.filter(p => p.category === category);
        let html = '';

        categoryPaths.forEach(path => {
            const statusIcon = path.exists ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
            html += `
                <div class="path-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <i class="fas ${statusIcon} me-2"></i>
                                <strong>${path.name}</strong>
                                ${path.required ? '<span class="badge bg-warning ms-2">Required</span>' : ''}
                            </div>
                            <code class="text-muted">${path.value}</code>
                            ${path.description ? `<p class="mb-0 mt-1 text-muted small">${path.description}</p>` : ''}
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-primary btn-sm" onclick="editPath('${path.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="validatePath('${path.id}')">
                                <i class="fas fa-check"></i>
                            </button>
                            ${!path.required ? `<button class="btn btn-outline-danger btn-sm" onclick="deletePath('${path.id}')"><i class="fas fa-trash"></i></button>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        if (html === '') {
            html = '<p class="text-muted">No paths configured for this category</p>';
        }

        container.innerHTML = html;
    });
}

// Report Functions
async function generateReport(type) {
    const filters = {
        dateFrom: document.getElementById('reportDateFrom').value,
        dateTo: document.getElementById('reportDateTo').value,
        user: document.getElementById('reportUser').value,
        role: document.getElementById('reportRole').value
    };

    try {
        const response = await fetch('/api/admin/generate-report', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ type, filters })
        });

        const data = await response.json();
        if (data.success) {
            displayReportData(data.report);
            updateCharts(data.charts);
        } else {
            showAlert('Failed to generate report: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error generating report:', error);
        showAlert('Failed to generate report', 'danger');
    }
}

function displayReportData(report) {
    const tbody = document.getElementById('reportTableBody');
    if (!tbody || !report.data) return;

    let html = '';
    report.data.forEach(row => {
        html += `
            <tr>
                <td>${row.user}</td>
                <td><span class="badge bg-primary">${row.role}</span></td>
                <td>${row.files_processed}</td>
                <td>${row.folders_validated}</td>
                <td>${row.audio_extracted}</td>
                <td>${row.avg_processing_time}</td>
                <td>${row.error_rate}%</td>
                <td>${formatDate(row.last_activity)}</td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

async function exportReport(format) {
    const filters = {
        dateFrom: document.getElementById('reportDateFrom').value,
        dateTo: document.getElementById('reportDateTo').value,
        user: document.getElementById('reportUser').value,
        role: document.getElementById('reportRole').value
    };

    try {
        const response = await fetch('/api/admin/export-report', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ format, filters })
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `archives_report_${new Date().toISOString().slice(0, 10)}.${format}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showAlert(`Report exported as ${format.toUpperCase()}`, 'success');
        } else {
            showAlert('Failed to export report', 'danger');
        }
    } catch (error) {
        console.error('Error exporting report:', error);
        showAlert('Failed to export report', 'danger');
    }
}

// Chart Functions
function initializeCharts() {
    const chartConfigs = [
        { id: 'userActivityChart', type: 'doughnut' },
        { id: 'processingTimeChart', type: 'line' },
        { id: 'categoryChart', type: 'bar' },
        { id: 'storageChart', type: 'pie' }
    ];

    chartConfigs.forEach(config => {
        const canvas = document.getElementById(config.id);
        if (canvas) {
            const ctx = canvas.getContext('2d');
            charts[config.id] = new Chart(ctx, {
                type: config.type,
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }
    });
}

function updateCharts(chartData) {
    Object.keys(chartData).forEach(chartId => {
        if (charts[chartId]) {
            charts[chartId].data = chartData[chartId];
            charts[chartId].update();
        }
    });
}

// User Management Functions
async function createUser() {
    const username = document.getElementById('newUsername').value;
    const password = document.getElementById('newPassword').value;
    const role = document.getElementById('newRole').value;
    const email = document.getElementById('newEmail').value;

    if (!username || !password || !role) {
        showAlert('Please fill in all required fields.', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/admin/users', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'create',
                username, password, role, email
            })
        });

        const data = await response.json();
        if (data.success) {
            showAlert('User created successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('createUserModal')).hide();
            location.reload();
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error creating user:', error);
        showAlert('Failed to create user', 'danger');
    }
}

function deleteUser(userId, username) {
    userToDelete = userId;
    document.getElementById('deleteUsername').textContent = username;
    new bootstrap.Modal(document.getElementById('confirmDeleteModal')).show();
}

async function confirmDeleteUser() {
    if (!userToDelete) return;

    try {
        const response = await fetch('/api/admin/users', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'delete',
                user_id: userToDelete
            })
        });

        const data = await response.json();
        if (data.success) {
            showAlert('User deleted successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal')).hide();
            document.getElementById(`user-row-${userToDelete}`).remove();
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error deleting user:', error);
        showAlert('Failed to delete user', 'danger');
    }
}

// Quick Action Functions
async function generateSystemReport() {
    showAlert('Generating comprehensive system report...', 'info');

    try {
        const response = await fetch('/api/admin/system-report', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            // Download the report
            const blob = new Blob([JSON.stringify(data.report, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system_report_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showAlert('System report generated and downloaded!', 'success');
        } else {
            showAlert('Failed to generate system report: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error generating system report:', error);
        showAlert('Failed to generate system report', 'danger');
    }
}

async function backupSystem() {
    // Set default backup name with current timestamp
    const now = new Date();
    const timestamp = now.getFullYear() +
                     (now.getMonth() + 1).toString().padStart(2, '0') +
                     now.getDate().toString().padStart(2, '0') + '_' +
                     now.getHours().toString().padStart(2, '0') +
                     now.getMinutes().toString().padStart(2, '0') +
                     now.getSeconds().toString().padStart(2, '0');

    document.getElementById('backupName').value = `backup_${timestamp}`;
    new bootstrap.Modal(document.getElementById('backupModal')).show();
}

async function clearCache(type = 'all') {
    const typeNames = {
        'all': 'all system cache',
        'sessions': 'user sessions',
        'filesystem': 'file system cache',
        'database': 'database cache',
        'preferences': 'user preferences'
    };

    if (!confirm(`Are you sure you want to clear ${typeNames[type]}?`)) return;

    try {
        const response = await fetch('/api/admin/clear-cache', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ type: type })
        });

        const data = await response.json();

        if (data.success) {
            showAlert(`${typeNames[type]} cleared successfully!`, 'success');
            if (data.cleared_items) {
                console.log('Cleared items:', data.cleared_items);
            }
        } else {
            showAlert('Failed to clear cache: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error clearing cache:', error);
        showAlert('Failed to clear cache', 'danger');
    }
}

async function masterCacheReset() {
    new bootstrap.Modal(document.getElementById('masterResetModal')).show();
}

async function resetAllUsers() {
    if (!confirm('Are you sure you want to reset ALL user data? This will clear all user sessions, preferences, and assignments.')) return;

    try {
        const response = await fetch('/api/admin/reset-all-users', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            showAlert('All users reset successfully! System is now in clean state.', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('Failed to reset users: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error resetting users:', error);
        showAlert('Failed to reset users', 'danger');
    }
}

async function resetAllUserSessions() {
    if (!confirm('Are you sure you want to reset all user sessions? All users will be logged out.')) return;

    try {
        const response = await fetch('/api/admin/clear-cache', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ type: 'sessions' })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('All user sessions reset successfully!', 'success');
        } else {
            showAlert('Failed to reset sessions: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error resetting sessions:', error);
        showAlert('Failed to reset sessions', 'danger');
    }
}

async function cleanupOldData() {
    if (!confirm('Are you sure you want to cleanup old data? This will remove old logs and temporary files.')) return;

    try {
        const response = await fetch('/api/admin/clear-cache', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ type: 'database' })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Old data cleaned up successfully!', 'success');
        } else {
            showAlert('Failed to cleanup data: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error cleaning up data:', error);
        showAlert('Failed to cleanup data', 'danger');
    }
}

async function refreshStats() {
    showAlert('Refreshing system statistics...', 'info');

    try {
        const response = await fetch('/api/admin/refresh-stats', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            // Update dashboard stats
            document.getElementById('totalUsers').textContent = data.stats.total_users;
            document.getElementById('completedFiles').textContent = data.stats.completed_files;
            document.getElementById('pendingFiles').textContent = data.stats.pending_files;
            document.getElementById('totalFiles').textContent = data.stats.total_files;

            showAlert('Statistics refreshed successfully!', 'success');
        } else {
            showAlert('Failed to refresh statistics: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error refreshing statistics:', error);
        showAlert('Failed to refresh statistics', 'danger');
    }
}

// Utility Functions
function formatDate(dateString) {
    if (!dateString || dateString === 'None' || dateString === 'Never') return dateString;
    try {
        return new Date(dateString).toLocaleString();
    } catch (e) {
        return dateString;
    }
}

function showAlert(message, type) {
    if (window.ArchivesSystem && window.ArchivesSystem.showAlert) {
        window.ArchivesSystem.showAlert(message, type);
    } else {
        alert(message);
    }
}

// Enhanced User Management Functions
function editUser(userId, username, role, email) {
    document.getElementById('editUserId').value = userId;
    document.getElementById('editUsername').value = username;
    document.getElementById('editRole').value = role;
    document.getElementById('editEmail').value = email || '';

    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

function resetUserPassword(userId, username) {
    document.getElementById('resetUserId').value = userId;
    document.getElementById('resetUserDisplay').textContent = username;
    document.getElementById('newPasswordInput').value = '';
    document.getElementById('confirmPasswordInput').value = '';

    new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
}

function viewUserDetails(userId) {
    // Implementation for viewing user details
    showAlert('User details view - Feature coming soon!', 'info');
}

async function confirmEditUser() {
    const userId = document.getElementById('editUserId').value;
    const username = document.getElementById('editUsername').value;
    const role = document.getElementById('editRole').value;
    const email = document.getElementById('editEmail').value;

    if (!username || !role) {
        showAlert('Username and role are required', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/admin/users', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'edit',
                user_id: userId,
                username: username,
                role: role,
                email: email
            })
        });

        const data = await response.json();
        if (data.success) {
            showAlert('User updated successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            location.reload();
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error updating user:', error);
        showAlert('Failed to update user', 'danger');
    }
}

async function confirmResetPassword() {
    const userId = document.getElementById('resetUserId').value;
    const newPassword = document.getElementById('newPasswordInput').value;
    const confirmPassword = document.getElementById('confirmPasswordInput').value;

    if (!newPassword || newPassword.length < 6) {
        showAlert('Password must be at least 6 characters long', 'warning');
        return;
    }

    if (newPassword !== confirmPassword) {
        showAlert('Passwords do not match', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/admin/users', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'reset_password',
                user_id: userId,
                new_password: newPassword
            })
        });

        const data = await response.json();
        if (data.success) {
            showAlert('Password reset successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error resetting password:', error);
        showAlert('Failed to reset password', 'danger');
    }
}

function showBulkCreateModal() {
    new bootstrap.Modal(document.getElementById('bulkCreateModal')).show();
}

async function confirmBulkCreate() {
    const bulkData = document.getElementById('bulkUserData').value.trim();
    if (!bulkData) {
        showAlert('Please enter user data', 'warning');
        return;
    }

    const lines = bulkData.split('\n').filter(line => line.trim());
    const users = [];

    for (const line of lines) {
        const parts = line.split(',').map(part => part.trim());
        if (parts.length >= 2) {
            users.push({
                username: parts[0],
                role: parts[1],
                email: parts[2] || ''
            });
        }
    }

    if (users.length === 0) {
        showAlert('No valid user data found', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/admin/users', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'bulk_create',
                users: users
            })
        });

        const data = await response.json();
        if (data.success) {
            showAlert(`Bulk user creation completed!`, 'success');
            bootstrap.Modal.getInstance(document.getElementById('bulkCreateModal')).hide();
            location.reload();
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error creating users:', error);
        showAlert('Failed to create users', 'danger');
    }
}

// Master Reset Confirmation
document.getElementById('confirmResetText').addEventListener('input', function() {
    const confirmBtn = document.getElementById('confirmMasterReset');
    if (this.value === 'RESET SYSTEM') {
        confirmBtn.disabled = false;
        confirmBtn.classList.remove('btn-secondary');
        confirmBtn.classList.add('btn-danger');
    } else {
        confirmBtn.disabled = true;
        confirmBtn.classList.remove('btn-danger');
        confirmBtn.classList.add('btn-secondary');
    }
});

async function confirmMasterReset() {
    try {
        const response = await fetch('/api/admin/reset-all-users', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            showAlert('MASTER SYSTEM RESET COMPLETED! System is now in clean state.', 'success');
            bootstrap.Modal.getInstance(document.getElementById('masterResetModal')).hide();
            setTimeout(() => {
                window.location.href = '/login';
            }, 3000);
        } else {
            showAlert('Failed to reset system: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error resetting system:', error);
        showAlert('Failed to reset system', 'danger');
    }
}

// Event Listeners
document.getElementById('confirmCreateUser').addEventListener('click', createUser);
document.getElementById('confirmDelete').addEventListener('click', confirmDeleteUser);
document.getElementById('confirmEditUser').addEventListener('click', confirmEditUser);
document.getElementById('confirmResetPassword').addEventListener('click', confirmResetPassword);
document.getElementById('confirmBulkCreate').addEventListener('click', confirmBulkCreate);
document.getElementById('confirmMasterReset').addEventListener('click', confirmMasterReset);
</script>
{% endblock %}
