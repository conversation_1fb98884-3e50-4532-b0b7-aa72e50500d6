{% extends "base.html" %}

{% block title %}Admin & Assignment Console - Archives Management System{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
<link href="/static/style.css" rel="stylesheet">
<style>
    .nav-pills .nav-link.active { background-color: #667eea; }
    .chart-container { position: relative; height: 300px; }
    .config-section { border-left: 4px solid #667eea; padding-left: 1rem; margin-bottom: 2rem; }
    .path-item { background-color: #f8f9fa; border-radius: 0.375rem; padding: 0.75rem; margin-bottom: 0.5rem; }
    .audit-log { font-family: 'Courier New', monospace; font-size: 0.875rem; max-height: 400px; overflow-y: auto; }
    .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 0.5rem; padding: 1.5rem; text-align: center; }
    .stat-number { font-size: 2rem; font-weight: bold; margin-bottom: 0.5rem; }
    
    /* Folder Browser Styles */
    .folder-browser {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 2px solid #dee2e6;
    }

    .folder-tree {
        max-height: 400px;
        overflow-y: auto;
        background: white;
        border-radius: 10px;
        padding: 15px;
        border: 1px solid #dee2e6;
    }

    .tree-node {
        margin: 2px 0;
        user-select: none;
    }

    .file-node {
        background: rgba(0,123,255,0.05);
        border-left: 3px solid #007bff;
        border-radius: 6px;
        margin: 1px 0;
        transition: all 0.2s ease;
    }

    .file-node:hover {
        background: rgba(0,123,255,0.15);
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba(0,123,255,0.2);
    }

    .assignment-panel {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 2px solid #e9ecef;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .assignment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .queue-section {
        background: linear-gradient(135deg, #fff, #f8f9fa);
        border-radius: 15px;
        padding: 25px;
        border: 2px solid #e9ecef;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .queue-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .queue-stats {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .queue-stat {
        text-align: center;
        padding: 10px 15px;
        background: rgba(255,255,255,0.8);
        border-radius: 10px;
        border: 1px solid #dee2e6;
        min-width: 80px;
    }

    .queue-stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #495057;
    }

    .queue-stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .path-selector {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #dee2e6;
    }

    .category-path-config {
        background: #fff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    .selected-folders-panel {
        background: #fff;
        border-radius: 10px;
        padding: 15px;
        border: 1px solid #dee2e6;
        max-height: 400px;
        overflow-y: auto;
    }

    .selected-folder-item {
        background: #f8f9fa;
        border-radius: 5px;
        padding: 8px 12px;
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #e9ecef;
    }

    .progress-section {
        background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
        border-radius: 15px;
        padding: 25px;
        margin-top: 30px;
        border: 2px solid #e1bee7;
        display: none;
    }

    @media (max-width: 768px) {
        .assignment-grid {
            grid-template-columns: 1fr;
        }
        
        .queue-stats {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 mb-0">
            <i class="fas fa-shield-alt me-2 text-gradient"></i>Admin & Assignment Console
        </h1>
        <p class="text-muted">Comprehensive system administration and folder assignment management</p>
    </div>
</div>

<!-- Navigation Tabs -->
<div class="row mb-4">
    <div class="col-12">
        <ul class="nav nav-pills nav-fill" id="adminTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="assignment-tab" data-bs-toggle="pill" data-bs-target="#assignment-pane" type="button" role="tab">
                    <i class="fas fa-archive me-2"></i>Assignment Console
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dashboard-tab" data-bs-toggle="pill" data-bs-target="#dashboard-pane" type="button" role="tab">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="paths-tab" data-bs-toggle="pill" data-bs-target="#paths-pane" type="button" role="tab">
                    <i class="fas fa-folder-tree me-2"></i>Path Config
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="users-tab" data-bs-toggle="pill" data-bs-target="#users-pane" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>Users
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-tab" data-bs-toggle="pill" data-bs-target="#system-pane" type="button" role="tab">
                    <i class="fas fa-cogs me-2"></i>System
                </button>
            </li>
        </ul>
    </div>
</div>

<!-- Tab Content -->
<div class="tab-content" id="adminTabContent">
    <!-- Assignment Console Tab -->
    <div class="tab-pane fade show active" id="assignment-pane" role="tabpanel">
        <!-- Path Selector Section -->
        <div class="path-selector">
            <h5 class="mb-3">
                <i class="fas fa-folder-open me-2"></i>Source Path Configuration
            </h5>
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-folder"></i></span>
                        <input type="text" class="form-control" id="sourcePathInput" 
                               value="{{ categories[0] if categories else 'T:\\To_Process\\Rough folder\\To Process\\restore Files' }}"
                               placeholder="Enter source folder path...">
                        <button class="btn btn-outline-secondary" type="button" onclick="browseForPath()">
                            <i class="fas fa-search"></i> Browse
                        </button>
                    </div>
                    <small class="form-text text-muted">Current source path for folder browsing</small>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary w-100" onclick="updateSourcePath()">
                        <i class="fas fa-sync-alt me-2"></i>Update Path & Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Category Path Configuration -->
        <div class="category-path-config">
            <h5 class="mb-3">
                <i class="fas fa-tags me-2"></i>Destination Category Paths
            </h5>
            <div class="row">
                {% for category in categories %}
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ category }}</label>
                    <div class="input-group">
                        <input type="text" class="form-control category-path-input" 
                               data-category="{{ category }}"
                               value="{{ category_paths.get(category, '') }}"
                               placeholder="Enter destination path for {{ category }}...">
                        <button class="btn btn-outline-primary btn-sm" onclick="updateCategoryPath('{{ category }}')">
                            <i class="fas fa-save"></i>
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Folder Browser Section -->
        <div class="folder-browser">
            <h3 class="mb-4">
                <i class="fas fa-folder-tree me-2"></i>Folder Browser
                <button class="btn btn-outline-primary btn-sm float-end" onclick="refreshFolders()">
                    <i class="fas fa-refresh me-1"></i>Refresh
                </button>
            </h3>
            <div class="row">
                <div class="col-md-8">
                    <div class="folder-tree" id="folderTree">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                            <p class="mt-2">Loading folders...</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="selected-folders-panel">
                        <h5><i class="fas fa-check-square me-2"></i>Selected Folders</h5>
                        <div id="selectedFoldersList" class="selected-folders-list">
                            <p class="text-muted">No folders selected</p>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="selectAllFolders()">
                                <i class="fas fa-check-double me-1"></i>Select All
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                                <i class="fas fa-times me-1"></i>Clear
                            </button>
                        </div>
                        <div class="mt-3 text-center">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-mini">
                                        <div class="stat-mini-number" id="selectedFolders">0</div>
                                        <div class="stat-mini-label">Selected</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-mini">
                                        <div class="stat-mini-number" id="totalFolders">0</div>
                                        <div class="stat-mini-label">Total</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-mini">
                                        <div class="stat-mini-number" id="queuedFolders">0</div>
                                        <div class="stat-mini-label">Queued</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assignment Panel -->
        <div class="assignment-panel">
            <h3 class="mb-4">
                <i class="fas fa-cogs me-2"></i>Assignment Configuration
            </h3>
            <div class="assignment-grid">
                <div class="form-group">
                    <label class="form-label" for="categorySelect">
                        <i class="fas fa-tags me-1"></i>Destination Category *
                    </label>
                    <select class="form-select" id="categorySelect" required>
                        <option value="">Select Category...</option>
                        {% for category in categories %}
                        <option value="{{ category }}">{{ category }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="assignToSelect">
                        <i class="fas fa-user me-1"></i>Assign To
                    </label>
                    <select class="form-select" id="assignToSelect">
                        <option value="">Select Assignee...</option>
                        {% for user in assignable_users %}
                        <option value="{{ user }}">{{ user }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="videoIdsInput">
                        <i class="fas fa-video me-1"></i>Video IDs
                    </label>
                    <input type="text" class="form-control" id="videoIdsInput"
                           placeholder="e.g., Z6486, Z6371, Z6472">
                    <small class="form-text text-muted">Comma-separated video IDs</small>
                </div>
                <div class="form-group">
                    <label class="form-label" for="urlInput">
                        <i class="fas fa-link me-1"></i>URL/Reference
                    </label>
                    <input type="url" class="form-control" id="urlInput"
                           placeholder="https://example.com/video">
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <label class="form-label" for="remarksInput">
                        <i class="fas fa-comment me-1"></i>Remarks
                    </label>
                    <textarea class="form-control" id="remarksInput" rows="3"
                              placeholder="Additional notes or comments..."></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" for="operationSelect">
                        <i class="fas fa-exchange-alt me-1"></i>Operation Type
                    </label>
                    <select class="form-select" id="operationSelect">
                        <option value="move">Move Folders</option>
                        <option value="copy">Copy Folders</option>
                    </select>
                </div>
            </div>
            <div class="text-center mt-4">
                <button class="btn btn-primary btn-lg me-3" onclick="addToQueue()" id="addToQueueBtn">
                    <i class="fas fa-plus me-2"></i>Add to Queue
                </button>
                <button class="btn btn-outline-secondary btn-lg me-3" onclick="previewSelection()">
                    <i class="fas fa-eye me-2"></i>Preview Selection
                </button>
                <button class="btn btn-outline-info btn-lg" onclick="openEmbeddedVLC()">
                    <i class="fas fa-play-circle me-2"></i>Open VLC Player
                </button>
            </div>
        </div>

        <!-- Queue Section -->
        <div class="queue-section">
            <div class="queue-header">
                <h3 class="mb-0">
                    <i class="fas fa-list-ol me-2"></i>Processing Queue
                </h3>
                <div class="queue-stats">
                    <div class="queue-stat">
                        <div class="queue-stat-number" id="queueTotal">0</div>
                        <div class="queue-stat-label">Total</div>
                    </div>
                    <div class="queue-stat">
                        <div class="queue-stat-number" id="queueQueued">0</div>
                        <div class="queue-stat-label">Queued</div>
                    </div>
                    <div class="queue-stat">
                        <div class="queue-stat-number" id="queueProcessing">0</div>
                        <div class="queue-stat-label">Processing</div>
                    </div>
                    <div class="queue-stat">
                        <div class="queue-stat-number" id="queueCompleted">0</div>
                        <div class="queue-stat-label">Completed</div>
                    </div>
                    <div class="queue-stat">
                        <div class="queue-stat-number" id="queueFailed">0</div>
                        <div class="queue-stat-label">Failed</div>
                    </div>
                </div>
            </div>
            <div class="queue-items" id="queueItems">
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>No items in queue</p>
                </div>
            </div>
            <div class="queue-controls mt-3">
                <button class="btn btn-success btn-lg me-3" onclick="processQueue()" id="processQueueBtn">
                    <i class="fas fa-play me-2"></i>Process Queue
                </button>
                <button class="btn btn-warning btn-lg me-3" onclick="refreshQueue()">
                    <i class="fas fa-refresh me-2"></i>Refresh Queue
                </button>
                <button class="btn btn-danger btn-lg" onclick="clearQueue()">
                    <i class="fas fa-trash me-2"></i>Clear Completed
                </button>
            </div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section" id="progressSection">
            <h4><i class="fas fa-tasks me-2"></i>Processing Progress</h4>
            <div class="progress mb-3">
                <div class="progress-bar" role="progressbar" id="progressBar" style="width: 0%">
                    <span id="progressText">0%</span>
                </div>
            </div>
            <div id="progressDetails" class="text-muted">
                Ready to process...
            </div>
        </div>
    </div>

    <!-- Dashboard Tab -->
    <div class="tab-pane fade" id="dashboard-pane" role="tabpanel">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">{{ users|length if users else 0 }}</div>
                    <div>Total Users</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                    <div class="stat-number" id="completedFiles">{{ file_stats.get('Completed', 0) if file_stats else 0 }}</div>
                    <div>Completed Files</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                    <div class="stat-number" id="pendingFiles">{{ (file_stats.get('Assigned', 0) + file_stats.get('Pending', 0)) if file_stats else 0 }}</div>
                    <div>Pending Files</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                    <div class="stat-number" id="totalFiles">{{ file_stats.values()|sum if file_stats else 0 }}</div>
                    <div>Total Files</div>
                </div>
            </div>
        </div>

        <!-- System Health Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>System Health Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="systemHealthContainer">
                            <div class="text-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-muted">Loading system health...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-primary w-100" onclick="generateSystemReport()">
                                    <i class="fas fa-file-alt me-2"></i>Generate Report
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-success w-100" onclick="backupSystem()">
                                    <i class="fas fa-download me-2"></i>Backup System
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-warning w-100" onclick="clearCache('all')">
                                    <i class="fas fa-broom me-2"></i>Clear Cache
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-info w-100" onclick="refreshStats()">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh Stats
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Path Configuration Tab -->
    <div class="tab-pane fade" id="paths-pane" role="tabpanel">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-folder-tree me-2"></i>System Path Configuration
                            </h5>
                            <button class="btn btn-primary" onclick="validateAllPaths()">
                                <i class="fas fa-check-circle me-1"></i>Validate All Paths
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="config-section">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-cog me-2"></i>Source Path Configuration
                            </h6>
                            <div class="path-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Current Source Path:</strong>
                                        <br><code id="currentSourcePath">{{ category_paths.get('To Be Processed', 'T:\\To_Process\\Rough folder\\To Process\\restore Files') }}</code>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm" onclick="editSourcePath()">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="config-section">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-folder me-2"></i>Destination Category Paths
                            </h6>
                            {% for category in categories %}
                            <div class="path-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ category }}:</strong>
                                        <br><code>{{ category_paths.get(category, 'Not configured') }}</code>
                                    </div>
                                    <div>
                                        <button class="btn btn-outline-success btn-sm me-2" onclick="validatePath('{{ category }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" onclick="editCategoryPath('{{ category }}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Tab -->
    <div class="tab-pane fade" id="users-pane" role="tabpanel">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>User Management
                            </h5>
                            <button class="btn btn-success" onclick="addNewUser()">
                                <i class="fas fa-user-plus me-1"></i>Add User
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Role</th>
                                        <th>Created</th>
                                        <th>Last Login</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in users %}
                                    <tr>
                                        <td>{{ user.username }}</td>
                                        <td><span class="badge bg-primary">{{ user.role }}</span></td>
                                        <td>{{ user.created_at[:10] if user.created_at else 'N/A' }}</td>
                                        <td>{{ user.last_login[:10] if user.last_login else 'Never' }}</td>
                                        <td>
                                            {% if user.is_active %}
                                            <span class="badge bg-success">Active</span>
                                            {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser({{ user.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ user.id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Tab -->
    <div class="tab-pane fade" id="system-pane" role="tabpanel">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>System Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">System Information</h6>
                                <div class="path-item">
                                    <strong>Database:</strong> {{ 'archives_management.db' }}
                                    <br><small class="text-muted">SQLite database file</small>
                                </div>
                                <div class="path-item">
                                    <strong>Categories:</strong> {{ categories|length }} configured
                                    <br><small class="text-muted">Processing destination categories</small>
                                </div>
                                <div class="path-item">
                                    <strong>Users:</strong> {{ users|length }} total
                                    <br><small class="text-muted">System user accounts</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success mb-3">System Actions</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="exportSystemConfig()">
                                        <i class="fas fa-download me-2"></i>Export Configuration
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="resetSystemCache()">
                                        <i class="fas fa-broom me-2"></i>Reset System Cache
                                    </button>
                                    <button class="btn btn-outline-info" onclick="viewSystemLogs()">
                                        <i class="fas fa-file-alt me-2"></i>View System Logs
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="emergencyReset()">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Emergency Reset
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Global variables
    let selectedFolders = new Set();
    let folderData = {};
    let queueData = {};
    let currentSourcePath = "{{ category_paths.get('To Be Processed', 'T:\\\\To_Process\\\\Rough folder\\\\To Process\\\\restore Files') }}";

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadFolders();
        refreshQueue();
        loadCategoryPaths();
        setInterval(refreshQueue, 5000); // Auto-refresh queue every 5 seconds
    });

    // Load category paths
    async function loadCategoryPaths() {
        try {
            const response = await fetch('/api/get-category-paths');
            const data = await response.json();

            if (data.success) {
                // Update category path inputs
                document.querySelectorAll('.category-path-input').forEach(input => {
                    const category = input.dataset.category;
                    if (data.category_paths[category]) {
                        input.value = data.category_paths[category];
                    }
                });

                // Update source path
                if (data.source_path) {
                    currentSourcePath = data.source_path;
                    document.getElementById('sourcePathInput').value = data.source_path;
                    document.getElementById('currentSourcePath').textContent = data.source_path;
                }
            }
        } catch (error) {
            console.error('Error loading category paths:', error);
        }
    }

    // Update source path
    async function updateSourcePath() {
        const newPath = document.getElementById('sourcePathInput').value.trim();

        if (!newPath) {
            showAlert('Please enter a valid path', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/update-source-path', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ path: newPath })
            });

            const data = await response.json();

            if (data.success) {
                currentSourcePath = newPath;
                document.getElementById('currentSourcePath').textContent = newPath;
                showAlert('Source path updated successfully!', 'success');
                loadFolders(); // Refresh folder tree
            } else {
                showAlert('Error updating source path: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error updating source path: ' + error.message, 'danger');
        }
    }

    // Update category path
    async function updateCategoryPath(category) {
        const input = document.querySelector(`[data-category="${category}"]`);
        const newPath = input.value.trim();

        if (!newPath) {
            showAlert('Please enter a valid path', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/update-category-path', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ category: category, path: newPath })
            });

            const data = await response.json();

            if (data.success) {
                showAlert(`Path for ${category} updated successfully!`, 'success');
            } else {
                showAlert('Error updating category path: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error updating category path: ' + error.message, 'danger');
        }
    }

    // Load folder structure
    async function loadFolders() {
        try {
            showAlert('Loading folders...', 'info');
            const response = await fetch(`/api/get-folders?path=${encodeURIComponent(currentSourcePath)}`);
            const data = await response.json();

            if (data.success) {
                folderData = data.folders;
                renderFolderTree(data.folders);
                updateStats();
                hideAlert();
            } else {
                showAlert('Error loading folders: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error loading folders: ' + error.message, 'danger');
        }
    }

    // Render folder tree
    function renderFolderTree(folders) {
        const treeContainer = document.getElementById('folderTree');
        treeContainer.innerHTML = '';

        if (!folders || folders.length === 0) {
            treeContainer.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-folder-open fa-2x mb-2"></i><p>No folders found</p></div>';
            return;
        }

        folders.forEach(folder => {
            const folderElement = createFolderElement(folder, 0);
            treeContainer.appendChild(folderElement);
        });
    }

    // Create folder element
    function createFolderElement(item, level) {
        const div = document.createElement('div');
        const isFile = item.type === 'file';
        div.className = `tree-node ${isFile ? 'file-node' : 'folder-node'}`;
        div.style.marginLeft = (level * 20) + 'px';

        const hasChildren = item.children && item.children.length > 0;

        if (isFile) {
            // File element
            const isMedia = item.is_media || false;
            const fileIcon = isMedia ? 'fas fa-file-video text-success' : 'fas fa-file text-info';

            div.innerHTML = `
                <div class="node-content" data-path="${item.path}" data-type="file">
                    <span class="expand-icon" style="visibility: hidden; width: 20px;"></span>
                    <i class="${fileIcon} me-2"></i>
                    <span class="file-name" title="${item.name}">${item.name}</span>
                    <small class="text-muted ms-2">(${item.size || 'Unknown'})</small>
                </div>
            `;
        } else {
            // Folder element
            const folderIcon = hasChildren ? 'fas fa-folder' : 'fas fa-folder-open';
            const mediaCount = item.media_count || item.file_count || 0;

            div.innerHTML = `
                <div class="node-content" data-path="${item.path}" data-type="folder">
                    <span class="expand-icon" onclick="toggleFolder(this)" style="visibility: ${hasChildren ? 'visible' : 'hidden'}">
                        <i class="fas fa-chevron-right"></i>
                    </span>
                    <input type="checkbox" class="folder-checkbox" onchange="toggleFolderSelection(this.closest('.node-content').dataset.path, this.checked)">
                    <i class="${folderIcon} me-2 text-warning"></i>
                    <span class="folder-name" title="${item.name}">${item.name}</span>
                    <small class="text-muted ms-2">(${mediaCount} media files)</small>
                </div>
            `;
        }

        if (hasChildren) {
            const childrenDiv = document.createElement('div');
            childrenDiv.className = 'folder-children';
            childrenDiv.style.display = 'none';

            item.children.forEach(child => {
                const childElement = createFolderElement(child, level + 1);
                childrenDiv.appendChild(childElement);
            });

            div.appendChild(childrenDiv);
        }

        return div;
    }

    // Toggle folder expansion
    function toggleFolder(element) {
        const icon = element.querySelector('i');
        const nodeContent = element.parentElement;
        const treeNode = nodeContent.parentElement;
        const childrenDiv = treeNode.querySelector('.folder-children');

        if (childrenDiv) {
            const isExpanded = childrenDiv.style.display !== 'none';
            childrenDiv.style.display = isExpanded ? 'none' : 'block';
            icon.className = isExpanded ? 'fas fa-chevron-right' : 'fas fa-chevron-down';
        }
    }

    // Toggle folder selection
    function toggleFolderSelection(folderPath, isSelected) {
        if (isSelected) {
            selectedFolders.add(folderPath);
        } else {
            selectedFolders.delete(folderPath);
        }

        updateSelectedFoldersList();
        updateStats();
    }

    // Update selected folders list
    function updateSelectedFoldersList() {
        const listContainer = document.getElementById('selectedFoldersList');

        if (selectedFolders.size === 0) {
            listContainer.innerHTML = '<p class="text-muted">No folders selected</p>';
            return;
        }

        let html = '';
        selectedFolders.forEach(folderPath => {
            const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();
            html += `
                <div class="selected-folder-item d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                    <span class="folder-name">${folderName}</span>
                    <div>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="previewInVLC('${folderPath}')" title="Preview in VLC">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFolderSelection('${folderPath}')" title="Remove">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        listContainer.innerHTML = html;
    }

    // Remove folder from selection
    function removeFolderSelection(folderPath) {
        selectedFolders.delete(folderPath);

        // Uncheck checkbox
        const nodeContents = document.querySelectorAll('[data-path]');
        for (const nodeContent of nodeContents) {
            if (nodeContent.dataset.path === folderPath) {
                const checkbox = nodeContent.parentElement.querySelector('.folder-checkbox');
                if (checkbox) {
                    checkbox.checked = false;
                }
                break;
            }
        }

        updateSelectedFoldersList();
        updateStats();
    }

    // Select all folders
    function selectAllFolders() {
        const checkboxes = document.querySelectorAll('.folder-checkbox');
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                checkbox.checked = true;
                const folderPath = checkbox.closest('.node-content').dataset.path;
                toggleFolderSelection(folderPath, true);
            }
        });
    }

    // Clear all selections
    function clearSelection() {
        const checkboxes = document.querySelectorAll('.folder-checkbox');
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                checkbox.checked = false;
                const folderPath = checkbox.closest('.node-content').dataset.path;
                toggleFolderSelection(folderPath, false);
            }
        });
    }

    // Update statistics
    function updateStats() {
        document.getElementById('selectedFolders').textContent = selectedFolders.size;

        const allFolderCheckboxes = document.querySelectorAll('.folder-checkbox');
        let topLevelFolderCount = 0;

        allFolderCheckboxes.forEach(checkbox => {
            const treeNode = checkbox.closest('.tree-node');
            if (treeNode && treeNode.style.marginLeft === '0px') {
                topLevelFolderCount++;
            }
        });

        document.getElementById('totalFolders').textContent = topLevelFolderCount;
        document.getElementById('queuedFolders').textContent = queueData.queued || 0;
    }

    // Add folders to queue
    async function addToQueue() {
        if (selectedFolders.size === 0) {
            showAlert('Please select at least one folder', 'warning');
            return;
        }

        const category = document.getElementById('categorySelect').value;
        if (!category) {
            showAlert('Please select a destination category', 'warning');
            return;
        }

        const assignTo = document.getElementById('assignToSelect').value;
        const videoIds = document.getElementById('videoIdsInput').value;
        const url = document.getElementById('urlInput').value;
        const remarks = document.getElementById('remarksInput').value;
        const operationType = document.getElementById('operationSelect').value;

        const button = document.getElementById('addToQueueBtn');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding to Queue...';
        button.disabled = true;

        try {
            const response = await fetch('/api/add-to-queue', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify({
                    folder_paths: Array.from(selectedFolders),
                    category: category,
                    assign_to: assignTo,
                    video_ids: videoIds,
                    url: url,
                    remarks: remarks,
                    operation_type: operationType
                })
            });

            const data = await response.json();

            if (data.success) {
                showAlert(`Successfully added ${data.added} folders to queue!`, 'success');
                if (data.failed > 0) {
                    showAlert(`Failed to add ${data.failed} folders. Check console for details.`, 'warning');
                }

                // Clear form and selection
                clearSelection();
                document.getElementById('videoIdsInput').value = '';
                document.getElementById('urlInput').value = '';
                document.getElementById('remarksInput').value = '';

                // Refresh queue
                refreshQueue();
            } else {
                showAlert('Error adding to queue: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error adding to queue: ' + error.message, 'danger');
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    // Refresh queue
    async function refreshQueue() {
        try {
            const response = await fetch('/api/queue-status');
            const data = await response.json();

            if (data.success) {
                queueData = data;
                updateQueueDisplay(data);
                updateQueueStats(data);
            }
        } catch (error) {
            console.error('Error refreshing queue:', error);
        }
    }

    // Update queue display
    function updateQueueDisplay(data) {
        const container = document.getElementById('queueItems');

        if (!data.items || data.items.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>No items in queue</p>
                </div>
            `;
            return;
        }

        let html = '';
        data.items.forEach(item => {
            const statusClass = {
                'queued': 'bg-warning',
                'processing': 'bg-info',
                'completed': 'bg-success',
                'failed': 'bg-danger'
            }[item.status] || 'bg-secondary';

            html += `
                <div class="queue-item p-3 mb-2 border rounded">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${item.folder_name}</h6>
                            <p class="mb-1 text-muted small">${item.folder_path}</p>
                            <div class="d-flex gap-2 mb-2">
                                <span class="badge ${statusClass}">${item.status}</span>
                                <span class="badge bg-secondary">${item.category}</span>
                                ${item.assign_to ? `<span class="badge bg-primary">${item.assign_to}</span>` : ''}
                            </div>
                            ${item.remarks ? `<p class="mb-0 small text-muted">${item.remarks}</p>` : ''}
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${item.created_at}</small>
                            ${item.status === 'failed' ? `<br><button class="btn btn-sm btn-outline-warning mt-1" onclick="retryQueueItem(${item.id})"><i class="fas fa-redo"></i> Retry</button>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    // Update queue statistics
    function updateQueueStats(data) {
        document.getElementById('queueTotal').textContent = data.total || 0;
        document.getElementById('queueQueued').textContent = data.queued || 0;
        document.getElementById('queueProcessing').textContent = data.processing || 0;
        document.getElementById('queueCompleted').textContent = data.completed || 0;
        document.getElementById('queueFailed').textContent = data.failed || 0;
    }

    // Process queue
    async function processQueue() {
        if (!queueData.queued || queueData.queued === 0) {
            showAlert('No items in queue to process', 'warning');
            return;
        }

        const button = document.getElementById('processQueueBtn');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
        button.disabled = true;

        // Show progress section
        document.getElementById('progressSection').style.display = 'block';

        try {
            const response = await fetch('/api/process-queue', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();

            if (data.success) {
                showAlert(`Successfully processed ${data.processed} folders!`, 'success');
                if (data.failed > 0) {
                    showAlert(`Failed to process ${data.failed} folders. Check queue for details.`, 'warning');
                }

                // Refresh queue and folders
                refreshQueue();
                setTimeout(() => {
                    loadFolders(); // Refresh folder list as items have been moved
                }, 1000);
            } else {
                showAlert('Error processing queue: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error processing queue: ' + error.message, 'danger');
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    // Clear completed queue items
    async function clearQueue() {
        try {
            const response = await fetch('/api/clear-completed-queue', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();
            if (data.success) {
                showAlert('Completed items cleared from queue', 'success');
                refreshQueue();
            } else {
                showAlert('Error clearing queue: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error clearing queue: ' + error.message, 'danger');
        }
    }

    // Preview selection
    function previewSelection() {
        if (selectedFolders.size === 0) {
            showAlert('No folders selected to preview', 'warning');
            return;
        }

        const folderList = Array.from(selectedFolders).map(path => {
            const name = path.split('\\').pop() || path.split('/').pop();
            return `• ${name}`;
        }).join('\n');

        alert(`Selected Folders (${selectedFolders.size}):\n\n${folderList}`);
    }

    // Refresh folders
    function refreshFolders() {
        loadFolders();
    }

    // Browse for path (placeholder - would need file dialog integration)
    function browseForPath() {
        const newPath = prompt('Enter the folder path:', currentSourcePath);
        if (newPath && newPath.trim()) {
            document.getElementById('sourcePathInput').value = newPath.trim();
        }
    }

    // Admin functions (placeholders)
    function generateSystemReport() {
        showAlert('System report generation started...', 'info');
    }

    function backupSystem() {
        showAlert('System backup started...', 'info');
    }

    function clearCache(type) {
        showAlert(`${type} cache cleared successfully`, 'success');
    }

    function refreshStats() {
        location.reload();
    }

    function validateAllPaths() {
        showAlert('Path validation completed', 'success');
    }

    function editSourcePath() {
        const newPath = prompt('Enter new source path:', currentSourcePath);
        if (newPath && newPath.trim()) {
            document.getElementById('sourcePathInput').value = newPath.trim();
            updateSourcePath();
        }
    }

    function editCategoryPath(category) {
        const input = document.querySelector(`[data-category="${category}"]`);
        const newPath = prompt(`Enter new path for ${category}:`, input.value);
        if (newPath && newPath.trim()) {
            input.value = newPath.trim();
            updateCategoryPath(category);
        }
    }

    function validatePath(category) {
        showAlert(`Path for ${category} is valid`, 'success');
    }

    function addNewUser() {
        showAlert('Add user functionality would be implemented here', 'info');
    }

    function editUser(userId) {
        showAlert(`Edit user ${userId} functionality would be implemented here`, 'info');
    }

    function deleteUser(userId) {
        if (confirm('Are you sure you want to delete this user?')) {
            showAlert(`Delete user ${userId} functionality would be implemented here`, 'info');
        }
    }

    function exportSystemConfig() {
        showAlert('System configuration export started...', 'info');
    }

    function resetSystemCache() {
        if (confirm('Are you sure you want to reset the system cache?')) {
            showAlert('System cache reset successfully', 'success');
        }
    }

    function viewSystemLogs() {
        showAlert('System logs viewer would open here', 'info');
    }

    function emergencyReset() {
        if (confirm('Are you sure you want to perform an emergency reset? This action cannot be undone.')) {
            showAlert('Emergency reset functionality would be implemented here', 'warning');
        }
    }

    // Utility functions
    function showAlert(message, type = 'info') {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    function hideAlert() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => alert.remove());
    }

    function updateProgress(percentage, text) {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const progressDetails = document.getElementById('progressDetails');

        progressBar.style.width = percentage + '%';
        progressText.textContent = percentage + '%';
        progressDetails.textContent = text;
    }

    // VLC-related functions
    async function previewInVLC(folderPath) {
        try {
            showAlert('Opening folder in VLC...', 'info');

            const response = await fetch('/api/preview-folder-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ folder_path: folderPath })
            });

            const data = await response.json();

            if (data.success) {
                showAlert(`Opened ${data.video_count} video(s) in VLC`, 'success');
            } else {
                showAlert('Error opening VLC: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error opening VLC: ' + error.message, 'danger');
        }
    }

    function openEmbeddedVLC() {
        // Open embedded VLC player in new tab
        window.open('/embedded-vlc-player', '_blank');
    }

    // Enhanced file node creation with VLC preview
    function createFileNodeWithVLC(item, level) {
        const div = document.createElement('div');
        const isFile = item.type === 'file';
        div.className = `tree-node ${isFile ? 'file-node' : 'folder-node'}`;
        div.style.marginLeft = (level * 20) + 'px';

        if (isFile && item.is_media) {
            // Media file with VLC preview option
            const fileIcon = 'fas fa-file-video text-success';

            div.innerHTML = `
                <div class="node-content" data-path="${item.path}" data-type="file">
                    <span class="expand-icon" style="visibility: hidden; width: 20px;"></span>
                    <i class="${fileIcon} me-2"></i>
                    <span class="file-name" title="${item.name}">${item.name}</span>
                    <small class="text-muted ms-2">(${item.size || 'Unknown'})</small>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="previewSingleFile('${item.path}')" title="Preview in VLC">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            `;
        } else {
            // Regular file or folder
            return createFolderElement(item, level);
        }

        return div;
    }

    async function previewSingleFile(filePath) {
        try {
            showAlert('Opening file in VLC...', 'info');

            const response = await fetch('/api/open-vlc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ file_path: filePath })
            });

            const data = await response.json();

            if (data.success) {
                showAlert('File opened in VLC successfully', 'success');
            } else {
                showAlert('Error opening VLC: ' + data.error, 'danger');
            }
        } catch (error) {
            showAlert('Error opening VLC: ' + error.message, 'danger');
        }
    }
</script>

{% endblock %}
