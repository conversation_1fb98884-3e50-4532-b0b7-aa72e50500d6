{% extends "base.html" %}

{% block title %}Cross Checker - Archives Management System{% endblock %}

{% block head %}
<style>
/* EXACT COPY - Folder Browser Styles from Archives Assignment Console */
.folder-browser {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 2px solid #dee2e6;
}

.folder-tree {
    max-height: 400px;
    overflow-y: auto;
    background: white;
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #dee2e6;
}

.tree-node {
    margin: 2px 0;
    user-select: none;
}

.file-node {
    background: rgba(0,123,255,0.05);
    border-left: 3px solid #007bff;
    border-radius: 6px;
    margin: 1px 0;
    transition: all 0.2s ease;
}

.file-node:hover {
    background: rgba(0,123,255,0.15);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.folder-node:hover .node-content {
    background: rgba(255,193,7,0.1);
}

.node-content {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.node-content:hover {
    background: rgba(0,123,255,0.1);
}

.folder-node .node-content {
    font-weight: 500;
}

.folder-node.selected .node-content {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.expand-icon {
    cursor: pointer;
    margin-right: 8px;
    transition: transform 0.2s ease;
    width: 16px;
    text-align: center;
}

.folder-children {
    border-left: 2px solid rgba(0,123,255,0.2);
    margin-left: 15px;
    padding-left: 5px;
}

.tree-node .btn {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.tree-node:hover .btn {
    opacity: 1;
}

.selected-folders-panel {
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.selected-folders-list {
    max-height: 300px;
    overflow-y: auto;
    margin: 15px 0;
}

.selected-folder-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.selected-folder-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.node-content {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.node-content:hover {
    background: rgba(0,123,255,0.1);
}

.expand-icon {
    cursor: pointer;
    width: 20px;
    text-align: center;
    color: #6c757d;
}

.expand-icon:hover {
    color: #007bff;
}

.folder-children {
    border-left: 1px dashed #dee2e6;
    margin-left: 10px;
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 mb-0">
            <i class="fas fa-check-double me-2"></i>Cross Check Folders
        </h1>
        <p class="text-muted">Review and validate processed folders with metadata</p>
    </div>
</div>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs" id="crossCheckTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending-pane" type="button" role="tab">
            <i class="fas fa-list me-2"></i>Pending Folders
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="folder-tree-tab" data-bs-toggle="tab" data-bs-target="#folder-tree-pane" type="button" role="tab">
            <i class="fas fa-folder-tree me-2"></i>Folder Tree View
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="audio-check-tab" data-bs-toggle="tab" data-bs-target="#audio-check-pane" type="button" role="tab">
            <i class="fas fa-music me-2"></i>Audio Check
        </button>
    </li>
</ul>

<div class="tab-content" id="crossCheckTabContent">
    <!-- Pending Folders Tab -->
    <div class="tab-pane fade show active" id="pending-pane" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder-open me-2"></i>Pending Folders for Review
                    <span class="badge bg-warning ms-2">{{ pending_folders|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if pending_folders %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Folder Name</th>
                                <th>Files & Size</th>
                                <th>Metadata</th>
                                <th>Audio Files</th>
                                <th>Processed Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for folder in pending_folders %}
                            <tr id="folder-row-{{ loop.index }}">
                                <td>
                                    <i class="fas fa-folder me-2 text-warning"></i>
                                    <strong>{{ folder.folder_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ folder.folder_path }}</small>
                                    {% if folder.video_ids %}
                                    <br>
                                    <span class="badge bg-secondary">{{ folder.video_ids }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span><i class="fas fa-file me-1"></i>{{ folder.file_count }} files</span>
                                        <span><i class="fas fa-hdd me-1"></i>{{ folder.folder_size }}</span>
                                    </div>
                                </td>
                                <td>
                                    {% if folder.has_metadata %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Complete
                                        </span>
                                        {% if folder.ocd_vp_number %}
                                        <br><small class="text-muted">{{ folder.ocd_vp_number }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-exclamation me-1"></i>Missing
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if folder.audio_files > 0 %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-volume-up me-1"></i>{{ folder.audio_files }} audio
                                        </span>
                                    {% else %}
                                        <span class="text-muted">No audio</span>
                                    {% endif %}
                                </td>
                                <td class="format-date">{{ folder.processed_date }}</td>
                                <td>
                                    <div class="btn-group-vertical" role="group">
                                        <button class="btn btn-primary btn-sm mb-1" onclick="showFolderDetails('{{ folder.folder_name }}', '{{ folder.folder_path }}')">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </button>
                                        <button class="btn btn-warning btn-sm mb-1" onclick="previewFolder('{{ folder.folder_path }}')">
                                            <i class="fas fa-play me-1"></i>Preview VLC
                                        </button>
                                        {% if folder.audio_files > 0 %}
                                        <button class="btn btn-info btn-sm mb-1" onclick="playAudio('{{ folder.folder_path }}')">
                                            <i class="fas fa-headphones me-1"></i>Play Audio
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-success btn-sm" onclick="approveFolder('{{ folder.folder_name }}', '{{ folder.folder_path }}')">
                                            <i class="fas fa-check me-1"></i>Approve & Validate
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                    <h4>All Caught Up!</h4>
                    <p class="text-muted">No folders pending for cross-check at the moment.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Folder Tree View Tab (EXACT MATCH - Archives Assignment Console) -->
    <div class="tab-pane fade" id="folder-tree-pane" role="tabpanel">
        <!-- Folder Browser Section (EXACT COPY from Archives Console) -->
        <div class="folder-browser">
            <h3 class="mb-4">
                <i class="fas fa-folder-tree me-2"></i>Cross Checker Folder Browser
                <button class="btn btn-outline-primary btn-sm float-end" onclick="refreshFolders()">
                    <i class="fas fa-refresh me-1"></i>Refresh
                </button>
            </h3>
            <div class="row">
                <div class="col-md-8">
                    <div class="folder-tree" id="folderTree">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                            <p class="mt-2">Loading cross-check folders...</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="selected-folders-panel">
                        <h5><i class="fas fa-check-square me-2"></i>Selected Folders</h5>
                        <div id="selectedFoldersList" class="selected-folders-list">
                            <p class="text-muted">No folders selected</p>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-secondary btn-sm me-2" onclick="selectAllFolders()">
                                <i class="fas fa-check-double me-1"></i>Select All
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                <i class="fas fa-times me-1"></i>Clear All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audio Check Tab -->
    <div class="tab-pane fade" id="audio-check-pane" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-music me-2"></i>Audio Check
                        </h5>
                        <p class="text-muted mb-0">Review and validate audio files in cross-check folder</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary" onclick="refreshAudioFiles()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <button class="btn btn-outline-info" onclick="openAudioFolder()">
                            <i class="fas fa-folder-open me-1"></i>Open Folder
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Audio Files List -->
                <div id="audioFilesContainer">
                    <div class="text-center py-5" id="audioLoadingState">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">Loading audio files...</p>
                    </div>

                    <div id="audioFilesContent" style="display: none;">
                        <!-- Audio files will be loaded here -->
                    </div>

                    <div id="audioEmptyState" class="text-center py-5" style="display: none;">
                        <i class="fas fa-music fa-4x text-muted mb-3"></i>
                        <h4>No Audio Files Found</h4>
                        <p class="text-muted">No WAV audio files found in the cross-check folder.</p>
                        <button class="btn btn-primary" onclick="refreshAudioFiles()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Approve & Validate Modal -->
<div class="modal fade" id="approveValidateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check me-2"></i>Approve & Validate Folder
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Choose the action for this validated folder:</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-recycle fa-3x text-success mb-3"></i>
                                <h6>Reingest</h6>
                                <p class="small text-muted">Move folder to "To Reingest" for reprocessing</p>
                                <button class="btn btn-success" onclick="processFolder('reingest')">
                                    <i class="fas fa-recycle me-1"></i>Reingest
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-trash fa-3x text-danger mb-3"></i>
                                <h6>Delete</h6>
                                <p class="small text-muted">Move folder to "To Be Deleted"</p>
                                <button class="btn btn-danger" onclick="processFolder('delete')">
                                    <i class="fas fa-trash me-1"></i>Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <label for="processingNotes" class="form-label">Notes (Optional)</label>
                    <textarea class="form-control" id="processingNotes" rows="3" placeholder="Add any notes about this decision..."></textarea>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audio Validation Modal -->
<div class="modal fade" id="audioValidationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-music me-2"></i>Validate Audio File
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>Audio File:</strong> <span id="currentAudioFileName">--</span>
                </div>
                <p>Choose the action for this audio file:</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-paper-plane fa-3x text-info mb-3"></i>
                                <h6>Send to TR</h6>
                                <p class="small text-muted">Move audio to "Audio Sent to TR"</p>
                                <button class="btn btn-info" onclick="processAudio('send_to_tr')">
                                    <i class="fas fa-paper-plane me-1"></i>Send to TR
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-archive fa-3x text-warning mb-3"></i>
                                <h6>Archive</h6>
                                <p class="small text-muted">Save in "Audios Repository"</p>
                                <button class="btn btn-warning" onclick="processAudio('archive')">
                                    <i class="fas fa-archive me-1"></i>Archive
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <label for="audioNotes" class="form-label">Notes (Optional)</label>
                    <textarea class="form-control" id="audioNotes" rows="3" placeholder="Add any notes about this audio file..."></textarea>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cross Check Modal -->
<div class="modal fade" id="crossCheckModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="crossCheckModalTitle">
                    <i class="fas fa-check-double me-2"></i>Cross Check File
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="crossCheckNotes" class="form-label">Notes (Optional)</label>
                    <textarea class="form-control" id="crossCheckNotes" rows="3" 
                              placeholder="Add any notes about this file..."></textarea>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="crossCheckAction"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn" id="confirmCrossCheck">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- File Details Modal -->
<div class="modal fade" id="fileDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>File Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="fileDetailsContent">
                <!-- File details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentFolderName = null;
let currentFolderPath = null;

$(document).ready(function() {
    // Format dates
    $('.format-date').each(function() {
        const text = $(this).text();
        if (text && text !== 'None') {
            $(this).text(formatDate(text));
        }
    });

    // Add event listener for folder tree tab
    $('#folder-tree-tab').on('click', function() {
        loadFolders();
    });

    // Add event listener for audio check tab
    $('#audio-check-tab').on('click', function() {
        refreshAudioFiles();
    });
});

function showFolderDetails(folderName, folderPath) {
    $('#fileDetailsContent').html(`
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading folder details...</p>
        </div>
    `);

    $('#fileDetailsModal').modal('show');

    // Load folder details via API
    fetch(`/api/cross-checker/folder-details?folder_name=${encodeURIComponent(folderName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const folder = data.folder_details;
                $('#fileDetailsContent').html(`
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-folder me-2"></i>Folder Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Folder Name:</strong></td>
                                    <td>${folder.folder_name}</td>
                                </tr>
                                <tr>
                                    <td><strong>Number of Files:</strong></td>
                                    <td>${folder.file_count} files</td>
                                </tr>
                                <tr>
                                    <td><strong>Size of folder (GB):</strong></td>
                                    <td>${folder.total_file_size || folder.folder_size}</td>
                                </tr>
                                <tr>
                                    <td><strong>Audio Files:</strong></td>
                                    <td>${folder.audio_files} files</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>Basic Metadata</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>OCD/ VP Number:</strong></td>
                                    <td>${folder.ocd_vp_number || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Edited file name:</strong></td>
                                    <td>${folder.edited_file_name || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Language:</strong></td>
                                    <td>${folder.language || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Edited Year:</strong></td>
                                    <td>${folder.edited_year || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Type Of Video:</strong></td>
                                    <td>${folder.video_type || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Edited in Ashram/US:</strong></td>
                                    <td>${folder.edited_location || 'N/A'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6><i class="fas fa-building me-2"></i>Department & Content</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Published Platforms:</strong></td>
                                    <td>${folder.published_platforms || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Department name:</strong></td>
                                    <td>${folder.department_name || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Component (Sadhguru/Non Sadhguru):</strong></td>
                                    <td>${folder.component || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Content Tag (multiple possible values):</strong></td>
                                    <td>${folder.content_tags || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Backup type:</strong></td>
                                    <td>${folder.backup_type || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Access:</strong></td>
                                    <td>${folder.access_level || 'N/A'}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-microphone me-2"></i>Audio & Transcript</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Software show_Renamed-foldername-Video-Audio-And-Transcript:</strong></td>
                                    <td style="max-width: 200px; word-wrap: break-word;">${folder.software_show_name || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Audio Code:</strong></td>
                                    <td>${folder.audio_code || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>AUDIO/Transcript CODE:</strong></td>
                                    <td>${folder.audio_code || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>AUDIO FILE NAME:</strong></td>
                                    <td>${folder.audio_file_name || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Transcription Status:</strong></td>
                                    <td>${folder.transcription_status || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Transcription file name:</strong></td>
                                    <td>${folder.transcription_file_name || 'N/A'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6><i class="fas fa-video me-2"></i>Video & Publication Details</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td style="width: 30%;"><strong>Video Released Date:</strong></td>
                                    <td>${folder.published_date || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Video ID:</strong></td>
                                    <td>${folder.video_id || folder.video_ids || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Published Title:</strong></td>
                                    <td style="max-width: 400px; word-wrap: break-word;">${folder.social_media_title || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Description:</strong></td>
                                    <td style="max-width: 400px; word-wrap: break-word;">${folder.description || 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td><strong>URL:</strong></td>
                                    <td><a href="${folder.url || folder.social_media_url || '#'}" target="_blank" style="word-break: break-all;">${folder.url || folder.social_media_url || 'N/A'}</a></td>
                                </tr>
                                <tr>
                                    <td><strong>Duration:</strong></td>
                                    <td>${folder.duration_category || 'N/A'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6><i class="fas fa-list me-2"></i>File List</h6>
                            <div class="bg-light p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                                ${folder.file_list.map(file => `
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span><i class="fas fa-file me-1"></i>${file.name}</span>
                                        <small class="text-muted">${file.size}</small>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `);
            } else {
                $('#fileDetailsContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading folder details: ${data.error}
                    </div>
                `);
            }
        })
        .catch(error => {
            $('#fileDetailsContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Failed to load folder details. Please try again.
                </div>
            `);
        });
}

function previewFolder(folderPath) {
    fetch('/api/cross-checker/preview-folder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            folder_path: folderPath
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`Opened ${data.video_files.length} video files in VLC`, 'success');
        } else {
            showAlert(`Error: ${data.error}`, 'danger');
        }
    })
    .catch(error => {
        showAlert('Failed to open VLC. Please try again.', 'danger');
    });
}

function playAudio(folderPath) {
    fetch('/api/cross-checker/play-audio', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            folder_path: folderPath
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`Playing ${data.audio_files.length} audio files`, 'success');
        } else {
            showAlert(`Error: ${data.error}`, 'danger');
        }
    })
    .catch(error => {
        showAlert('Failed to play audio. Please try again.', 'danger');
    });
}

function approveFolder(folderName, folderPath) {
    currentFolderName = folderName;
    currentFolderPath = folderPath;

    $('#crossCheckModalTitle').html(`
        <i class="fas fa-check me-2"></i>Approve & Validate Folder
    `);

    $('#crossCheckAction').text(`You are about to approve and validate this folder. This will move the video to "To be Ingested/Videos" and audio to "To be Ingested/Audios".`);
    $('#confirmCrossCheck').removeClass('btn-success btn-danger').addClass('btn-success');
    $('#confirmCrossCheck').html(`
        <i class="fas fa-check me-1"></i>Approve & Validate
    `);

    $('#crossCheckNotes').val('');
    $('#crossCheckModal').modal('show');
}

$('#confirmCrossCheck').click(function() {
    const notes = $('#crossCheckNotes').val();

    $(this).prop('disabled', true).html(`
        <div class="spinner-border spinner-border-sm me-1" role="status"></div>Processing...
    `);

    fetch('/api/cross-checker/approve-folder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            folder_name: currentFolderName,
            folder_path: currentFolderPath,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');

            // Remove the row from table and reload if empty
            setTimeout(() => {
                location.reload();
            }, 2000);

            $('#crossCheckModal').modal('hide');
        } else {
            showAlert(`Error: ${data.error}`, 'danger');
        }
    })
    .catch(error => {
        showAlert('Failed to process approval. Please try again.', 'danger');
    })
    .finally(() => {
        $('#confirmCrossCheck').prop('disabled', false).html(`
            <i class="fas fa-check me-1"></i>Approve & Validate
        `);
    });
});

function formatDate(dateString) {
    if (!dateString || dateString === 'None') return 'N/A';
    try {
        return new Date(dateString).toLocaleString();
    } catch (e) {
        return dateString;
    }
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Add to top of page
    $('body').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// EXACT COPY - Folder Tree Functionality from Archives Assignment Console
let selectedFolders = new Set();
let folderData = [];

// Load folder structure (EXACT COPY from Archives Console)
async function loadFolders() {
    try {
        showAlert('Loading cross-check folders...', 'info');
        const response = await fetch('/api/cross-checker/folder-tree');
        const data = await response.json();

        if (data.success) {
            folderData = data.folders;
            renderFolderTree(data.folders);
            updateStats();
            hideAlert();
        } else {
            showAlert('Error loading cross-check folders: ' + data.error, 'danger');
        }
    } catch (error) {
        showAlert('Error loading cross-check folders: ' + error.message, 'danger');
    }
}

// Refresh folders (EXACT COPY from Archives Console)
function refreshFolders() {
    loadFolders();
}

// Render folder tree (EXACT COPY from Archives Assignment Console)
function renderFolderTree(folders) {
    const treeContainer = document.getElementById('folderTree');
    treeContainer.innerHTML = '';

    if (!folders || folders.length === 0) {
        treeContainer.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-folder-open fa-2x mb-2"></i><p>No cross-check folders found</p></div>';
        return;
    }

    folders.forEach(folder => {
        const folderElement = createFolderElement(folder, 0);
        treeContainer.appendChild(folderElement);
    });
}

// Create folder or file element with enhanced display (EXACT COPY from Archives Assignment Console)
function createFolderElement(item, level) {
    const div = document.createElement('div');
    const isFile = item.type === 'file';
    div.className = `tree-node ${isFile ? 'file-node' : 'folder-node'}`;
    div.style.marginLeft = (level * 20) + 'px';

    const hasChildren = item.children && item.children.length > 0;

    if (isFile) {
        // File element with enhanced media detection
        const isMedia = item.is_media || false;
        const fileIcon = getFileIcon(item.extension, isMedia);
        const fileColor = isMedia ? 'text-success' : 'text-info';

        div.innerHTML = `
            <div class="node-content" data-path="${item.path}" data-type="file">
                <span class="expand-icon" style="visibility: hidden; width: 20px;"></span>
                <i class="${fileIcon} me-2 ${fileColor}"></i>
                <span class="file-name" title="${item.name}">${truncateFileName(item.name, 40)}</span>
                <small class="text-muted ms-2">(${item.size || 'Unknown'})</small>
                ${isMedia ? `
                    <button class="btn btn-sm btn-success ms-2" onclick="openFileInVLC('${escapeHtml(item.path)}')" title="Open in VLC">
                        <i class="fas fa-play"></i>
                    </button>
                ` : ''}
                <button class="btn btn-sm btn-outline-secondary ms-1" onclick="showFileInfo('${escapeHtml(item.path)}')" title="File Info">
                    <i class="fas fa-info"></i>
                </button>
            </div>
        `;
    } else {
        // Folder element with enhanced display (cross-check specific)
        const folderIcon = hasChildren ? 'fas fa-folder' : 'fas fa-folder-open';
        const mediaCount = item.media_count || item.file_count || 0;
        const status = item.status || 'Pending validation';

        div.innerHTML = `
            <div class="node-content" data-path="${item.path}" data-type="folder">
                <span class="expand-icon" onclick="toggleFolder(this)" style="visibility: ${hasChildren ? 'visible' : 'hidden'}">
                    <i class="fas fa-chevron-right"></i>
                </span>
                <input type="checkbox" class="folder-checkbox" onchange="toggleFolderSelection('${escapeHtml(item.path)}', this.checked)">
                <i class="${folderIcon} me-2 text-warning"></i>
                <span class="folder-name" title="${item.name}">${truncateFileName(item.name, 35)}</span>
                <small class="text-muted ms-2">(${mediaCount} media files)</small>
                <span class="badge bg-info ms-2">${status}</span>
                ${mediaCount > 0 ? `
                    <button class="btn btn-sm btn-warning ms-2" onclick="openFolderInVLC('${escapeHtml(item.path)}')" title="Open first media file in VLC">
                        <i class="fas fa-play"></i>
                    </button>
                ` : ''}
                <button class="btn btn-sm btn-outline-info ms-1" onclick="showFolderInfo('${escapeHtml(item.path)}')" title="Folder Info">
                    <i class="fas fa-info"></i>
                </button>
                <button class="btn btn-sm btn-success ms-1" onclick="validateFolder('${escapeHtml(item.path)}')" title="Validate Folder">
                    <i class="fas fa-check"></i>
                </button>
            </div>
        `;
    }

    if (hasChildren) {
        const childrenDiv = document.createElement('div');
        childrenDiv.className = 'folder-children';
        childrenDiv.style.display = 'none';

        item.children.forEach(child => {
            const childElement = createFolderElement(child, level + 1);
            childrenDiv.appendChild(childElement);
        });

        div.appendChild(childrenDiv);
    }

    return div;
}

// Helper functions (same as Assigner)
function getFileIcon(extension, isMedia) {
    if (isMedia) {
        if (['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'].includes(extension)) {
            return 'fas fa-file-video';
        } else if (['.mp3', '.wav', '.m4a', '.aac', '.ogg'].includes(extension)) {
            return 'fas fa-file-audio';
        }
    }
    return 'fas fa-file';
}

function truncateFileName(name, maxLength) {
    if (name.length <= maxLength) return name;
    return name.substring(0, maxLength - 3) + '...';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Toggle folder expansion (EXACT COPY from Archives Assignment Console)
function toggleFolder(element) {
    const icon = element.querySelector('i');
    const nodeContent = element.parentElement;
    const treeNode = nodeContent.parentElement;
    const childrenDiv = treeNode.querySelector('.folder-children');

    if (childrenDiv) {
        const isExpanded = childrenDiv.style.display !== 'none';
        childrenDiv.style.display = isExpanded ? 'none' : 'block';
        icon.className = isExpanded ? 'fas fa-chevron-right' : 'fas fa-chevron-down';
    }
}

// Toggle folder selection (EXACT COPY from Archives Assignment Console)
function toggleFolderSelection(folderPath, isSelected) {
    if (isSelected) {
        selectedFolders.add(folderPath);
    } else {
        selectedFolders.delete(folderPath);
    }

    updateSelectedFoldersList();
    updateStats();

    // Update visual selection
    const nodeContent = document.querySelector(`[data-path="${folderPath}"]`);
    if (nodeContent) {
        const folderNode = nodeContent.parentElement;
        if (isSelected) {
            folderNode.classList.add('selected');
        } else {
            folderNode.classList.remove('selected');
        }
    }
}

// Update selected folders list (EXACT COPY from Archives Assignment Console)
function updateSelectedFoldersList() {
    const listContainer = document.getElementById('selectedFoldersList');

    if (selectedFolders.size === 0) {
        listContainer.innerHTML = '<p class="text-muted">No folders selected</p>';
        return;
    }

    let html = '';
    selectedFolders.forEach(folderPath => {
        const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();
        html += `
            <div class="selected-folder-item d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                <span class="folder-name">${folderName}</span>
                <button class="btn btn-sm btn-outline-danger" onclick="removeFolderSelection('${folderPath}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    });

    listContainer.innerHTML = html;
}

// Remove folder from selection (EXACT COPY from Archives Assignment Console)
function removeFolderSelection(folderPath) {
    selectedFolders.delete(folderPath);

    // Uncheck checkbox
    const checkbox = document.querySelector(`[data-path="${folderPath}"]`).parentElement.querySelector('.folder-checkbox');
    if (checkbox) {
        checkbox.checked = false;
    }

    // Remove visual selection
    const nodeContent = document.querySelector(`[data-path="${folderPath}"]`);
    if (nodeContent) {
        nodeContent.parentElement.classList.remove('selected');
    }

    updateSelectedFoldersList();
    updateStats();
}

// Select all folders (EXACT COPY from Archives Assignment Console)
function selectAllFolders() {
    const checkboxes = document.querySelectorAll('.folder-checkbox');
    checkboxes.forEach(checkbox => {
        if (!checkbox.checked) {
            checkbox.checked = true;
            const folderPath = checkbox.closest('.node-content').dataset.path;
            toggleFolderSelection(folderPath, true);
        }
    });
}

// Clear all selections (EXACT COPY from Archives Assignment Console)
function clearSelection() {
    const checkboxes = document.querySelectorAll('.folder-checkbox');
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            checkbox.checked = false;
            const folderPath = checkbox.closest('.node-content').dataset.path;
            toggleFolderSelection(folderPath, false);
        }
    });
}

// Update statistics (EXACT COPY from Archives Assignment Console)
function updateStats() {
    // Count only top-level folders (those with marginLeft: 0px)
    const allFolderCheckboxes = document.querySelectorAll('.folder-checkbox');
    let topLevelFolderCount = 0;

    allFolderCheckboxes.forEach(checkbox => {
        const treeNode = checkbox.closest('.tree-node');
        if (treeNode && treeNode.style.marginLeft === '0px') {
            topLevelFolderCount++;
        }
    });
}

// VLC integration functions (EXACT COPY from Archives Assignment Console)
function openFolderInVLC(folderPath) {
    fetch('/api/preview-folder-vlc', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ folder_path: folderPath })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`Opened ${data.video_count} videos in VLC`, 'success');
        } else {
            showAlert('Error previewing folder: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error previewing folder:', error);
        showAlert('Failed to preview folder', 'danger');
    });
}

// File/folder info functions (EXACT COPY from Archives Assignment Console)
function showFileInfo(filePath) {
    showAlert('Showing file info: ' + filePath, 'info');
    // Implement file info modal
}

function showFolderInfo(folderPath) {
    showAlert('Showing folder info: ' + folderPath, 'info');
    // Implement folder info modal
}

// Cross-checker specific functions
function validateFolder(folderPath) {
    showAlert('Validating folder: ' + folderPath, 'info');
    // Implement folder validation
}

function toggleCrossCheckFolder(element, folderPath) {
    const childrenDiv = element.parentElement.querySelector('.children');
    const toggleIcon = element.querySelector('.toggle-icon');

    if (childrenDiv) {
        if (childrenDiv.style.display === 'none') {
            childrenDiv.style.display = 'block';
            toggleIcon.classList.remove('fa-chevron-right');
            toggleIcon.classList.add('fa-chevron-down');
        } else {
            childrenDiv.style.display = 'none';
            toggleIcon.classList.remove('fa-chevron-down');
            toggleIcon.classList.add('fa-chevron-right');
        }
    }

    // Load folder details
    loadCrossCheckFolderDetails(folderPath);
}

function selectCrossCheckFile(filePath) {
    // Handle file selection if needed
    console.log('Selected file:', filePath);
    showAlert(`Selected file: ${filePath.split('\\').pop() || filePath.split('/').pop()}`, 'info');
}

// VLC integration functions for Cross Checker
function openFileInVLC(filePath) {
    fetch('/api/preview-file-vlc', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ file_path: filePath })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Opening file in VLC...', 'success');
        } else {
            showAlert('Error opening file in VLC: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Error opening file in VLC', 'danger');
    });
}

async function loadCrossCheckFolderDetails(folderPath) {
    selectedCrossCheckFolder = folderPath;

    try {
        const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop();
        const response = await fetch(`/api/cross-checker/folder-details?folder_name=${encodeURIComponent(folderName)}`);
        const data = await response.json();

        if (data.success) {
            const details = data.folder_details;
            document.getElementById('selectedFolderDetails').innerHTML = `
                <h6><i class="fas fa-folder me-2"></i>${details.folder_name}</h6>
                <hr>
                <p><strong>Path:</strong> <small>${details.folder_path}</small></p>
                <p><strong>Files:</strong> ${details.file_count}</p>
                <p><strong>Size:</strong> ${details.folder_size}</p>
                <p><strong>Audio Files:</strong> ${details.audio_files}</p>
                ${details.has_metadata ? `
                    <div class="mt-3">
                        <h6><i class="fas fa-info-circle me-2"></i>Metadata Available</h6>
                        <p><strong>OCD Number:</strong> ${details.ocd_vp_number || 'N/A'}</p>
                        <p><strong>Language:</strong> ${details.language || 'N/A'}</p>
                        <p><strong>Video Type:</strong> ${details.video_type || 'N/A'}</p>
                    </div>
                ` : '<div class="alert alert-warning mt-3">No metadata available</div>'}
            `;

            // Update actions panel
            document.getElementById('folderActions').innerHTML = `
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="showFolderDetails('${details.folder_name}', '${details.folder_path}')">
                        <i class="fas fa-eye me-2"></i>View Full Details
                    </button>
                    <button class="btn btn-warning" onclick="previewFolder('${details.folder_path}')">
                        <i class="fas fa-play me-2"></i>Preview in VLC
                    </button>
                    ${details.audio_files > 0 ? `
                        <button class="btn btn-info" onclick="playAudio('${details.folder_path}')">
                            <i class="fas fa-headphones me-2"></i>Play Audio
                        </button>
                    ` : ''}
                    <button class="btn btn-success" onclick="approveFolder('${details.folder_name}', '${details.folder_path}')">
                        <i class="fas fa-check me-2"></i>Approve & Validate
                    </button>
                </div>
            `;
        } else {
            document.getElementById('selectedFolderDetails').innerHTML =
                '<div class="alert alert-warning">Error loading folder details</div>';
        }
    } catch (error) {
        console.error('Error loading cross-check folder details:', error);
        document.getElementById('selectedFolderDetails').innerHTML =
            '<div class="alert alert-danger">Failed to load folder details</div>';
    }
}

// Audio Check Functions
let currentAudioFile = null;

// Refresh audio files
async function refreshAudioFiles() {
    try {
        document.getElementById('audioLoadingState').style.display = 'block';
        document.getElementById('audioFilesContent').style.display = 'none';
        document.getElementById('audioEmptyState').style.display = 'none';

        const response = await fetch('/api/cross-checker/audio-files');
        const data = await response.json();

        if (data.success) {
            displayAudioFiles(data.audio_files);
        } else {
            showAlert('Error loading audio files: ' + data.error, 'danger');
            document.getElementById('audioEmptyState').style.display = 'block';
        }
    } catch (error) {
        console.error('Error loading audio files:', error);
        showAlert('Failed to load audio files', 'danger');
        document.getElementById('audioEmptyState').style.display = 'block';
    } finally {
        document.getElementById('audioLoadingState').style.display = 'none';
    }
}

// Display audio files
function displayAudioFiles(audioFiles) {
    const container = document.getElementById('audioFilesContent');

    if (!audioFiles || audioFiles.length === 0) {
        document.getElementById('audioEmptyState').style.display = 'block';
        return;
    }

    let html = '<div class="row">';
    audioFiles.forEach((file, index) => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1">
                                    <i class="fas fa-file-audio text-info me-2"></i>
                                    ${file.name}
                                </h6>
                                <small class="text-muted">Size: ${file.size}</small>
                                <br>
                                <small class="text-muted">Modified: ${formatDate(file.modified)}</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="btn-group w-100" role="group">
                                <button class="btn btn-outline-success btn-sm" onclick="playAudioFile('${file.path}')">
                                    <i class="fas fa-play me-1"></i>Play
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="showAudioDetails('${file.path}', '${file.name}')">
                                    <i class="fas fa-info me-1"></i>Details
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="validateAudioFile('${file.path}', '${file.name}')">
                                    <i class="fas fa-check me-1"></i>Validate
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';

    container.innerHTML = html;
    container.style.display = 'block';
}

// Play audio file
function playAudioFile(filePath) {
    fetch('/api/cross-checker/play-audio-file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ file_path: filePath })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Audio file opened in VLC for playback', 'success');
        } else {
            showAlert('Error playing audio: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error playing audio:', error);
        showAlert('Failed to play audio file', 'danger');
    });
}

// Show audio details
function showAudioDetails(filePath, fileName) {
    fetch('/api/cross-checker/audio-details', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ file_path: filePath })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const details = data.details;
            $('#fileDetailsContent').html(`
                <div class="row">
                    <div class="col-md-12">
                        <h6><i class="fas fa-file-audio me-2"></i>Audio File Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>File Name:</strong></td>
                                <td>${fileName}</td>
                            </tr>
                            <tr>
                                <td><strong>File Path:</strong></td>
                                <td style="word-break: break-all;">${filePath}</td>
                            </tr>
                            <tr>
                                <td><strong>File Size:</strong></td>
                                <td>${details.size}</td>
                            </tr>
                            <tr>
                                <td><strong>Duration:</strong></td>
                                <td>${details.duration || 'Unknown'}</td>
                            </tr>
                            <tr>
                                <td><strong>Format:</strong></td>
                                <td>${details.format || 'WAV'}</td>
                            </tr>
                            <tr>
                                <td><strong>Sample Rate:</strong></td>
                                <td>${details.sample_rate || 'Unknown'}</td>
                            </tr>
                            <tr>
                                <td><strong>Channels:</strong></td>
                                <td>${details.channels || 'Unknown'}</td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>${formatDate(details.created)}</td>
                            </tr>
                            <tr>
                                <td><strong>Modified:</strong></td>
                                <td>${formatDate(details.modified)}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            `);
            $('#fileDetailsModal').modal('show');
        } else {
            showAlert('Error loading audio details: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error loading audio details:', error);
        showAlert('Failed to load audio details', 'danger');
    });
}

// Validate audio file
function validateAudioFile(filePath, fileName) {
    currentAudioFile = { path: filePath, name: fileName };
    document.getElementById('currentAudioFileName').textContent = fileName;
    $('#audioValidationModal').modal('show');
}

// Process audio file
function processAudio(action) {
    if (!currentAudioFile) {
        showAlert('No audio file selected', 'warning');
        return;
    }

    const notes = document.getElementById('audioNotes').value;

    fetch('/api/cross-checker/process-audio', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            file_path: currentAudioFile.path,
            file_name: currentAudioFile.name,
            action: action,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            $('#audioValidationModal').modal('hide');

            // Refresh audio files list
            setTimeout(() => {
                refreshAudioFiles();
            }, 1000);
        } else {
            showAlert('Error processing audio: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error processing audio:', error);
        showAlert('Failed to process audio file', 'danger');
    });
}

// Open audio folder
function openAudioFolder() {
    fetch('/api/open-folder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            folder_path: 'T:\\To_Process\\Rough folder\\Folder to be cross checked'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Cross check folder opened', 'success');
        } else {
            showAlert('Error opening folder: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error opening folder:', error);
        showAlert('Failed to open cross check folder', 'danger');
    });
}

// Enhanced folder processing with reingest/delete options
function processFolder(action) {
    if (!currentFolderName || !currentFolderPath) {
        showAlert('No folder selected', 'warning');
        return;
    }

    const notes = document.getElementById('processingNotes').value;

    fetch('/api/cross-checker/process-folder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            folder_name: currentFolderName,
            folder_path: currentFolderPath,
            action: action,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            $('#approveValidateModal').modal('hide');

            // Reload page after processing
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('Error processing folder: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error processing folder:', error);
        showAlert('Failed to process folder', 'danger');
    });
}
</script>
{% endblock %}
