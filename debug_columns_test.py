#!/usr/bin/env python3
"""
DEBUG COLUMNS TEST
Test with enhanced debugging to see exactly which columns are written to
"""

import requests
import json
from datetime import datetime

def debug_columns_test():
    print("🚨 DEBUG COLUMNS TEST - ENHANCED LOGGING")
    print("="*60)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # Login as assigner
    print("🔐 Logging in as assigner...")
    login_data = {'username': 'assigner', 'password': 'Shiva@123'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    print("✅ Login successful")
    
    # Test with unique data to track in Google Sheets
    timestamp = datetime.now().strftime("%H%M%S")
    test_data = {
        'folder_name': f'DEBUG_TEST_{timestamp}',
        'date_processed': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'moved_to_folder': 'Internal video without stems',
        'social_media_url': f'https://debug-test-{timestamp}.com',
        'assigned_to': 'Executor Public',
        'remarks': f'DEBUG TEST {timestamp} - SHOULD BE IN COLUMNS A-F'
    }
    
    print(f"\n📝 Test data with unique timestamp {timestamp}:")
    print(f"   📁 Folder Name: {test_data['folder_name']}")
    print(f"   📅 Date: {test_data['date_processed']}")
    print(f"   📂 Category: {test_data['moved_to_folder']}")
    print(f"   🔗 URL: {test_data['social_media_url']}")
    print(f"   👤 Assigned: {test_data['assigned_to']}")
    print(f"   📝 Remarks: {test_data['remarks']}")
    
    print(f"\n🔧 Calling Google Sheets API with debug logging...")
    
    # Call the test API
    response = session.post(f"{base_url}/api/test-google-sheets", json=test_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"\n✅ API Response: {result.get('success', False)}")
        print(f"✅ Message: {result.get('message', 'No message')}")
        
        if result.get('success'):
            print(f"✅ Sheet ID: {result.get('sheet_id', 'Not found')}")
            print(f"✅ Sheet Name: {result.get('sheet_name', 'Not found')}")
            
            if 'expected_columns' in result:
                expected = result['expected_columns']
                print(f"\n📊 EXPECTED COLUMN MAPPING:")
                print(f"   Column A: {expected.get('A', 'Missing')}")
                print(f"   Column B: {expected.get('B', 'Missing')}")
                print(f"   Column C: {expected.get('C', 'Missing')}")
                print(f"   Column D: {expected.get('D', 'Missing')}")
                print(f"   Column E: {expected.get('E', 'Missing')}")
                print(f"   Column F: {expected.get('F', 'Missing')}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
    else:
        print(f"❌ API call failed: {response.status_code}")
        print(f"❌ Response: {response.text}")
    
    print(f"\n🔍 MANUAL VERIFICATION STEPS:")
    print(f"1. Open Google Sheets: https://docs.google.com/spreadsheets/d/13Yluuoc5gyVuBWp3jQYiGW0ywXeGGIWYjk96xVeNIb4")
    print(f"2. Go to 'Records' sheet")
    print(f"3. Look for row with 'DEBUG_TEST_{timestamp}' in ANY column")
    print(f"4. Check which column it appears in:")
    print(f"   - If in Column A: ✅ CORRECT (A-F mapping working)")
    print(f"   - If in Column G: ❌ BUG (G-M mapping happening)")
    print(f"5. Note the exact column letters where the data appears")
    
    print(f"\n⚠️  IMPORTANT:")
    print(f"The unique timestamp {timestamp} will help identify this specific test entry")
    print(f"Look for this exact text: 'DEBUG_TEST_{timestamp}'")
    print("="*60)

if __name__ == "__main__":
    debug_columns_test()
